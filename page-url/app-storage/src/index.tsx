
import React from 'react'
import ReactDOM from 'react-dom'
import {  ConfigProvider } from "antd";
import zhCN from "antd/es/locale/zh_CN";

import { RouterProvider } from "react-router-dom"
import Context from '@/context/masterContext'
import initRouter from '@/router'
import './public-path'
import './index.css'

const rootId = 'micro-app'

const render = (props?: any) => {
  const { container, base } = props || {}


  const root = container ? container.querySelector(`#${rootId}`) : document.getElementById(rootId)
  ReactDOM.render((
    <Context.Provider value={props}>
      <ConfigProvider locale={zhCN}>
        <RouterProvider router={initRouter(base)} />
      </ConfigProvider>
    </Context.Provider>
  ), root)
}

// @ts-ignore
if (!window.__POWERED_BY_QIANKUN__) {
  render()
}

/**
 * bootstrap 只会在微应用初始化的时候调用一次，下次微应用重新进入时会直接调用 mount 钩子，不会再重复触发 bootstrap。
 * 通常我们可以在这里做一些全局变量的初始化，比如不会在 unmount 阶段被销毁的应用级别的缓存等。
 */
export async function bootstrap() {
}

/**
 * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
 */
export async function mount(props: any) {
  render(props)
}

/**
 * 应用每次 切出/卸载 会调用的方法，通常在这里我们会卸载微应用的应用实例
 */
export async function unmount(props: any) {
  const { container } = props || {}
  const root = container ? container.querySelector(`#${rootId}`) : document.getElementById(rootId)
  root && ReactDOM.unmountComponentAtNode(root)
}


export async function update(props: any) {
  console.log('update props', props)

  render(props)
}