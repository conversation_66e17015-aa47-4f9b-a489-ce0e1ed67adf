import React from "react";
import { <PERSON><PERSON>, Space, Typography } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";

const { Title } = Typography;

export interface HeaderAction {
  key: string;
  label: string;
  icon?: React.ReactNode;
  type?: "default" | "primary" | "text" | "link" | "dashed";
  loading?: boolean;
  disabled?: boolean;
  danger?: boolean;
  onClick: () => void;
}

export interface PageHeaderProps {
  /** 页面标题 */
  title: string;
  /** 是否显示返回按钮 */
  showBack?: boolean;
  /** 返回按钮点击事件 */
  onBack?: () => void;
  /** 返回按钮文本，默认为"返回" */
  backText?: string;
  /** 副标题或状态标签 */
  subtitle?: React.ReactNode;
  /** 右侧操作按钮配置 */
  actions?: HeaderAction[];
  /** 是否吸顶显示（使用sticky定位） */
  fixed?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 是否显示底部边框 */
  bordered?: boolean;
  /** 背景色类型 */
  background?: "white" | "gray" | "gradient";
  /** 标题级别 */
  titleLevel?: 1 | 2 | 3 | 4 | 5;
}

/**
 * 统一的页面头部组件
 * 提供一致的页面头部样式和功能
 */
const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  showBack = false,
  onBack,
  backText = "返回",
  subtitle,
  actions = [],
  fixed = false,
  className = "",
  bordered = true,
  background = "white",
  titleLevel = 4,
}) => {
  // 背景样式映射
  const backgroundClasses = {
    white: "bg-white",
    gray: "bg-gray-50",
    gradient: "bg-gradient-to-r from-blue-500 to-purple-600 text-white",
  };

  // 标题样式映射
  const titleClasses = {
    white: "text-gray-900",
    gray: "text-gray-900",
    gradient: "!text-white",
  };

  // 基础样式类
  const baseClasses = `
    px-6 py-4 
    ${backgroundClasses[background]}
    ${bordered ? "border-b border-gray-200 shadow-sm" : ""}
    ${fixed ? "sticky top-0 z-50" : ""}
    ${className}
  `.trim();

  return (
    <div className={baseClasses}>
      <div className="flex justify-between items-center mx-auto max-w-7xl">
        {/* 左侧：返回按钮 + 标题 + 副标题 */}
        <Space size="large" className="flex items-center">
          {showBack && onBack && (
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={onBack}
              type="text"
              className={`flex items-center ${
                background === "gradient"
                  ? "text-white hover:text-gray-200"
                  : ""
              }`}
            >
              {backText}
            </Button>
          )}

          <div className="flex items-center space-x-3">
            <Title
              level={titleLevel}
              className={`!m-0 font-semibold ${titleClasses[background]}`}
            >
              {title}
            </Title>
            {subtitle && <div className="flex items-center">{subtitle}</div>}
          </div>
        </Space>

        {/* 右侧：操作按钮 */}
        {actions.length > 0 && (
          <Space>
            {actions.map((action) => (
              <Button
                key={action.key}
                type={action.type || "default"}
                icon={action.icon}
                loading={action.loading}
                disabled={action.disabled}
                danger={action.danger}
                onClick={action.onClick}
                className={
                  background === "gradient" && action.type === "text"
                    ? "text-white hover:text-gray-200"
                    : ""
                }
              >
                {action.label}
              </Button>
            ))}
          </Space>
        )}
      </div>
    </div>
  );
};

export default PageHeader;
