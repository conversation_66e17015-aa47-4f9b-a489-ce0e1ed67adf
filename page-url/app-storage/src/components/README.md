# 统一Header组件使用指南

本目录包含了项目中的公用组件，主要是为了保持页面header的一致性而封装的组件。

## 组件说明

### PageHeader 组件

统一的页面头部组件，提供一致的页面头部样式和功能。

#### 主要特性

- ✅ 统一的视觉样式和布局
- ✅ 灵活的按钮配置
- ✅ 支持固定定位和普通定位
- ✅ 支持多种背景主题
- ✅ 完整的TypeScript类型支持

#### 基本用法

```tsx
import { PageHeader } from '@/components';
import type { HeaderAction } from '@/components';

const actions: HeaderAction[] = [
  {
    key: 'refresh',
    label: '刷新',
    icon: <ReloadOutlined />,
    type: 'text',
    onClick: handleRefresh,
  },
  {
    key: 'edit',
    label: '编辑',
    icon: <EditOutlined />,
    type: 'primary',
    onClick: handleEdit,
  },
];

<PageHeader
  title="页面标题"
  showBack={true}
  onBack={handleBack}
  actions={actions}
  fixed={true}
/>
```

### PageLayout 组件

页面布局组件，统一管理带固定header的页面布局。

#### 基本用法

```tsx
import { PageLayout } from '@/components';
import type { HeaderAction } from '@/components';

const actions: HeaderAction[] = [
  {
    key: 'save',
    label: '保存',
    icon: <SaveOutlined />,
    type: 'primary',
    loading: saving,
    onClick: handleSave,
  },
];

<PageLayout
  title="页面标题"
  showBack={true}
  onBack={handleBack}
  actions={actions}
  fixed={true}
  subtitle={<span className="text-orange-600">有未保存的更改</span>}
>
  {/* 页面内容 */}
  <div>页面内容...</div>
</PageLayout>
```

## 属性说明

### PageHeaderProps

| 属性 | 类型 | 默认值 | 说明 |
|-----|------|--------|------|
| title | string | - | 页面标题 |
| showBack | boolean | false | 是否显示返回按钮 |
| onBack | () => void | - | 返回按钮点击事件 |
| backText | string | '返回' | 返回按钮文本 |
| subtitle | React.ReactNode | - | 副标题或状态标签 |
| actions | HeaderAction[] | [] | 右侧操作按钮配置 |
| fixed | boolean | false | 是否固定在顶部 |
| className | string | '' | 自定义样式类名 |
| bordered | boolean | true | 是否显示底部边框 |
| background | 'white' \| 'gray' \| 'gradient' | 'white' | 背景色类型 |
| titleLevel | 1 \| 2 \| 3 \| 4 \| 5 | 2 | 标题级别 |

### HeaderAction

| 属性 | 类型 | 默认值 | 说明 |
|-----|------|--------|------|
| key | string | - | 唯一标识 |
| label | string | - | 按钮文本 |
| icon | React.ReactNode | - | 按钮图标 |
| type | 'default' \| 'primary' \| 'text' \| 'link' \| 'dashed' | 'default' | 按钮类型 |
| loading | boolean | false | 是否显示加载状态 |
| disabled | boolean | false | 是否禁用 |
| danger | boolean | false | 是否为危险按钮 |
| onClick | () => void | - | 点击事件 |

## 使用场景

### 1. 详情页面

```tsx
const DetailPage = () => {
  const headerActions: HeaderAction[] = [
    {
      key: 'refresh',
      label: '刷新',
      icon: <ReloadOutlined />,
      type: 'text',
      onClick: handleRefresh,
    },
    {
      key: 'edit',
      label: '编辑',
      icon: <EditOutlined />,
      type: 'primary',
      onClick: handleEdit,
    },
  ];

  return (
    <PageLayout
      title="合同详情"
      showBack={true}
      onBack={handleBack}
      actions={headerActions}
      fixed={true}
    >
      {/* 详情内容 */}
    </PageLayout>
  );
};
```

### 2. 列表页面

```tsx
const ListPage = () => {
  const headerActions: HeaderAction[] = [
    {
      key: 'refresh',
      label: '刷新',
      icon: <ReloadOutlined />,
      type: 'primary',
      loading: loading,
      onClick: handleRefresh,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader
        title="回款列表"
        actions={headerActions}
        bordered={true}
      />
      <div className="p-6">
        {/* 列表内容 */}
      </div>
    </div>
  );
};
```

### 3. 编辑页面

```tsx
const EditPage = () => {
  const subtitle = hasChanges ? (
    <span className="px-2 py-1 text-xs text-orange-600 bg-orange-100 rounded">
      有未保存的更改
    </span>
  ) : null;

  const headerActions: HeaderAction[] = [
    {
      key: 'save',
      label: '保存',
      icon: <SaveOutlined />,
      type: 'default',
      loading: saving,
      disabled: !hasChanges,
      onClick: handleSave,
    },
    {
      key: 'confirm',
      label: '确认',
      icon: <CheckOutlined />,
      type: 'primary',
      loading: saving,
      onClick: handleConfirm,
    },
  ];

  return (
    <PageLayout
      title="确认签约"
      showBack={true}
      onBack={handleBack}
      subtitle={subtitle}
      actions={headerActions}
      fixed={true}
    >
      {/* 编辑表单 */}
    </PageLayout>
  );
};
```

## 迁移指南

### 从旧的Header实现迁移

#### 旧代码
```tsx
<div className="fixed top-0 right-0 left-0 z-50 px-6 py-4 bg-white border-b border-gray-200 shadow-sm">
  <div className="flex justify-between items-center mx-auto max-w-7xl">
    <Space size="large" className="flex items-center">
      <Button icon={<ArrowLeftOutlined />} onClick={onBack} type="text">
        返回
      </Button>
      <h1 className="m-0 text-xl font-semibold text-gray-900">合同详情</h1>
    </Space>
    <Space>
      <Button icon={<ReloadOutlined />} onClick={onRefresh} type="text">
        刷新
      </Button>
      <Button type="primary" icon={<EditOutlined />} onClick={onEdit}>
        编辑
      </Button>
    </Space>
  </div>
</div>
```

#### 新代码
```tsx
const headerActions: HeaderAction[] = [
  {
    key: 'refresh',
    label: '刷新',
    icon: <ReloadOutlined />,
    type: 'text',
    onClick: onRefresh,
  },
  {
    key: 'edit',
    label: '编辑',
    icon: <EditOutlined />,
    type: 'primary',
    onClick: onEdit,
  },
];

<PageHeader
  title="合同详情"
  showBack={true}
  onBack={onBack}
  actions={headerActions}
  fixed={true}
/>
```

## 注意事项

1. **固定定位**: 当使用 `fixed={true}` 时，PageLayout会自动给内容区域添加顶部边距
2. **按钮配置**: actions数组的顺序决定了按钮的显示顺序
3. **类型安全**: 所有props都有完整的TypeScript类型定义
4. **样式一致性**: 组件使用了统一的Tailwind CSS类名，确保视觉一致性 