# FileUpload 文件上传组件

## 概述

FileUpload 是一个基于 Ant Design Upload 组件封装的可复用文件上传组件，集成了文件类型验证、大小限制、上传状态管理等功能。

## 特性

- ✅ **两种上传模式**: 按钮上传 (`button`) 和拖拽上传 (`dragger`)
- ✅ **文件类型验证**: 支持配置允许的文件扩展名
- ✅ **文件大小限制**: 可配置单个文件最大尺寸
- ✅ **数量限制**: 支持限制上传文件数量
- ✅ **状态管理**: 自动处理上传状态和进度
- ✅ **错误处理**: 统一的错误提示和处理
- ✅ **TypeScript**: 完整的类型支持

## 接口定义

### FileUploadProps

```typescript
interface FileUploadProps {
  value?: ExtendedUploadFile[];           // 当前文件列表
  onChange?: (fileList: ExtendedUploadFile[], fileInfos: FileInfo[]) => void; // 文件变化回调
  config?: FileUploadConfig;              // 上传配置
  className?: string;                     // 自定义样式类名
}
```

### FileUploadConfig

```typescript
interface FileUploadConfig {
  // 上传相关配置
  uploadUrl?: string;                     // 上传接口地址
  fieldId?: string;                       // 字段ID
  standardCollectionId?: string;          // 标准集合ID
  headers?: Record<string, string>;       // 请求头
  
  // 文件限制配置
  maxCount?: number;                      // 最大文件数量
  maxSizeMB?: number;                     // 单个文件最大尺寸(MB)
  allowedExts?: string[];                 // 允许的文件扩展名
  
  // UI配置
  mode?: 'button' | 'dragger';            // 上传模式
  buttonText?: string;                    // 按钮文本
  dragText?: string;                      // 拖拽区域文本
  dragHint?: string;                      // 拖拽提示文本
  showDescription?: boolean;              // 是否显示描述信息
  disabled?: boolean;                     // 是否禁用
}
```

### FileInfo

```typescript
interface FileInfo {
  fileId: string;        // 文件ID
  fileName: string;      // 文件名
  fileUrl: string;       // 文件URL
  fileSize: number;      // 文件大小
}
```

## 默认配置

```typescript
const defaultConfig = {
  uploadUrl: "/app/api/io/file/uploadFile",
  fieldId: "KQM19659",
  standardCollectionId: "KQM1892",
  headers: { authorization: "authorization-text" },
  maxCount: 5,
  maxSizeMB: 10,
  allowedExts: ["doc", "docx", "ppt", "pptx", "xls", "xlsx", "pdf", "jpg", "jpeg", "png", "zip", "rar"],
  mode: 'button',
  buttonText: "点击上传",
  dragText: "点击或拖拽文件到此区域上传",
  dragHint: "支持扩展名：.rar .zip .doc .docx .pdf .jpg...",
  showDescription: true,
  disabled: false,
};
```

## 使用示例

### 1. 基础按钮上传

```typescript
import { FileUpload } from "@/components";
import type { ExtendedUploadFile, FileInfo } from "@/components";

const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);

const handleFileChange = (newFileList: ExtendedUploadFile[], fileInfos: FileInfo[]) => {
  setFileList(newFileList);
  console.log('文件信息:', fileInfos);
};

<FileUpload
  value={fileList}
  onChange={handleFileChange}
/>
```

### 2. 拖拽上传模式

```typescript
<FileUpload
  value={fileList}
  onChange={handleFileChange}
  config={{
    mode: 'dragger',
    maxCount: 1,
    dragText: '点击或拖拽文件到此区域上传',
    dragHint: '支持扩展名：.pdf .jpg .png',
  }}
/>
```

### 3. 自定义配置

```typescript
<FileUpload
  value={fileList}
  onChange={handleFileChange}
  config={{
    mode: 'button',
    maxCount: 3,
    maxSizeMB: 20,
    allowedExts: ['pdf', 'jpg', 'png', 'doc', 'docx'],
    buttonText: '选择文件',
    showDescription: true,
    disabled: uploading,
  }}
  className="my-custom-upload"
/>
```

### 4. 完整使用示例（PaymentOut upload.tsx）

```typescript
import React, { useState, useCallback } from "react";
import { FileUpload } from "@/components";
import type { ExtendedUploadFile, FileInfo } from "@/components";

const UploadReceipt: React.FC = () => {
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [uploading, setUploading] = useState(false);

  // 处理文件变化
  const handleFileChange = useCallback((newFileList: ExtendedUploadFile[], fileInfos: FileInfo[]) => {
    setFileList(newFileList);
    console.log('上传的文件信息:', fileInfos);
  }, []);

  // 提交处理
  const handleSubmit = useCallback(async () => {
    const uploadedFiles = fileList.filter(file => file.status === 'done' && file.fileId);
    if (uploadedFiles.length === 0) {
      message.error('请等待文件上传完成');
      return;
    }

    // 使用 uploadedFiles 进行后续处理
    console.log('已上传的文件:', uploadedFiles);
  }, [fileList]);

  return (
    <Card>
      <FileUpload
        value={fileList}
        onChange={handleFileChange}
        config={{
          mode: 'dragger',
          maxCount: 1,
          maxSizeMB: 20,
          allowedExts: ['rar', 'zip', 'doc', 'docx', 'pdf', 'jpg', 'jpeg', 'png'],
          dragText: '点击或拖拽文件到此区域上传',
          dragHint: '支持扩展名：.rar .zip .doc .docx .pdf .jpg...',
          showDescription: true,
          disabled: uploading,
        }}
      />
    </Card>
  );
};
```

## 文件上传流程

1. **文件选择**: 用户选择或拖拽文件
2. **验证检查**: 自动验证文件类型、大小、数量
3. **开始上传**: 通过配置的API接口上传文件
4. **状态更新**: 更新文件状态和进度
5. **结果处理**: 成功后更新文件信息，失败则显示错误

## 注意事项

1. **文件状态检查**: 提交前需要检查 `file.status === 'done' && file.fileId` 确保文件上传成功
2. **类型安全**: 使用 TypeScript 类型确保代码安全
3. **错误处理**: 组件内置了常见的错误处理，但具体业务逻辑需要自行处理
4. **样式定制**: 可通过 `className` 自定义样式
5. **配置合并**: 传入的配置会与默认配置合并，只需覆盖需要修改的部分

## 相关文件

- `src/components/FileUpload.tsx` - 组件实现
- `src/components/index.ts` - 组件导出
- `src/pages/PaymentOut/upload.tsx` - 使用示例
- `src/pages/PaymentList/components/AddPaymentModal.tsx` - 原始参考实现 