import React, { useState, useCallback } from "react";
import { Upload, Button, message, Typography } from "antd";
import { UploadOutlined, InboxOutlined } from "@ant-design/icons";
import type { UploadProps } from "antd";
import type { UploadFile } from "antd/es/upload/interface";

const { Dragger } = Upload;
const { Text } = Typography;

// 扩展UploadFile接口，添加自定义属性
export interface ExtendedUploadFile extends UploadFile {
  fileId?: string;
}

// 文件上传配置接口
export interface FileUploadConfig {
  // 上传相关配置
  uploadUrl?: string;
  fieldId?: string;
  standardCollectionId?: string;
  headers?: Record<string, string>;
  
  // 文件限制配置
  maxCount?: number;
  maxSizeMB?: number;
  allowedExts?: string[];
  
  // UI配置
  mode?: 'button' | 'dragger';
  buttonText?: string;
  dragText?: string;
  dragHint?: string;
  showDescription?: boolean;
  disabled?: boolean;
}

// 文件信息接口
export interface FileInfo {
  fileId: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
}

// 组件Props接口
export interface FileUploadProps {
  value?: ExtendedUploadFile[];
  onChange?: (fileList: ExtendedUploadFile[], fileInfos: FileInfo[]) => void;
  config?: FileUploadConfig;
  className?: string;
}

// 默认配置
const defaultConfig: Required<FileUploadConfig> = {
  uploadUrl: "/app/api/io/file/uploadFile",
  fieldId: "KQM19659",
  standardCollectionId: "KQM1892",
  headers: { authorization: "authorization-text" },
  maxCount: 5,
  maxSizeMB: 10,
  allowedExts: ["doc", "docx", "ppt", "pptx", "xls", "xlsx", "pdf", "jpg", "jpeg", "png", "zip", "rar"],
  mode: 'button',
  buttonText: "点击上传",
  dragText: "点击或拖拽文件到此区域上传",
  dragHint: "支持扩展名：.rar .zip .doc .docx .pdf .jpg...",
  showDescription: true,
  disabled: false,
};

/**
 * 生成文件限制描述
 */
function getFileDescription(exts: string[], maxCount: number, maxSize: number): string {
  const extStr = exts.join("/");
  return `只能上传${extStr}文件，文件数量不超过${maxCount}个，单个文件不超过${maxSize}M`;
}

/**
 * 可复用的文件上传组件
 */
const FileUpload: React.FC<FileUploadProps> = ({
  value = [],
  onChange,
  config = {},
  className = "",
}) => {
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>(value);
  
  // 合并配置
  const finalConfig = { ...defaultConfig, ...config };

  // 处理文件上传变化
  const handleUploadChange: UploadProps["onChange"] = useCallback((info) => {
    let newFileList = [...info.fileList] as ExtendedUploadFile[];

    // 处理上传成功的文件
    newFileList = newFileList.map((file) => {
      if (file.response) {
        const baseUrl = "https://pms.tongbaninfo.com:8888/app";
        file.fileId = file.response?.data?.fileId;
        file.url = baseUrl + file.response?.data?.filePath;
      }
      return file;
    });

    setFileList(newFileList);

    // 获取上传成功的文件信息
    const fileInfos = newFileList
      .filter(file => file.status === 'done' && file.fileId)
      .map(file => ({
        fileId: file.fileId!,
        fileName: file.name,
        fileUrl: file.url || '',
        fileSize: file.size || 0,
      }));

    // 触发onChange回调
    onChange?.(newFileList, fileInfos);

    if (info.file.status === "done") {
      message.success(`${info.file.name} 文件上传成功`);
    } else if (info.file.status === "error") {
      message.error(`${info.file.name} 文件上传失败`);
    }
  }, [onChange]);

  // 上传前的验证
  const beforeUpload = useCallback((file: File) => {
    const fileType = file.name.split(".").pop()?.toLowerCase() || "";
    const isValidExtension = finalConfig.allowedExts.includes(fileType);
    const isUnderMax = file.size / 1024 / 1024 < finalConfig.maxSizeMB;

    if (!isValidExtension) {
      message.error(`仅支持上传${finalConfig.allowedExts.join("/")}文件`);
      return Upload.LIST_IGNORE;
    }

    if (!isUnderMax) {
      message.error(`单个文件大小不能超过${finalConfig.maxSizeMB}MB`);
      return Upload.LIST_IGNORE;
    }

    if (fileList.length >= finalConfig.maxCount) {
      message.error(`最多只能上传${finalConfig.maxCount}个文件`);
      return Upload.LIST_IGNORE;
    }

    return true;
  }, [fileList.length, finalConfig]);

  // 处理文件移除
  const handleRemove = useCallback((file: ExtendedUploadFile) => {
    const updatedFileList = fileList.filter((item) => item.uid !== file.uid);
    setFileList(updatedFileList);

    // 获取更新后的文件信息
    const fileInfos = updatedFileList
      .filter(f => f.status === 'done' && f.fileId)
      .map(f => ({
        fileId: f.fileId!,
        fileName: f.name,
        fileUrl: f.url || '',
        fileSize: f.size || 0,
      }));

    onChange?.(updatedFileList, fileInfos);
    return true;
  }, [fileList, onChange]);

  // 通用上传属性
  const uploadProps: UploadProps = {
    fileList,
    name: "file",
    action: finalConfig.uploadUrl,
    headers: finalConfig.headers,
    data: {
      fieldId: finalConfig.fieldId,
      standardCollectionId: finalConfig.standardCollectionId,
    },
    beforeUpload,
    onChange: handleUploadChange,
    onRemove: handleRemove,
    multiple: true,
    accept: finalConfig.allowedExts.map((ext) => "." + ext).join(","),
    disabled: finalConfig.disabled,
  };

  // 渲染按钮上传模式
  if (finalConfig.mode === 'button') {
    return (
      <div className={`flex items-center ${className}`}>
        <div className="max-w-xs">
          <Upload {...uploadProps}>
            <Button
              disabled={fileList.length >= finalConfig.maxCount || finalConfig.disabled}
              type="primary"
              icon={<UploadOutlined />}
            >
              {finalConfig.buttonText}
            </Button>
          </Upload>
        </div>
        {finalConfig.showDescription && (
          <div className="ml-3 text-xs text-gray-500">
            {getFileDescription(finalConfig.allowedExts, finalConfig.maxCount, finalConfig.maxSizeMB)}
          </div>
        )}
      </div>
    );
  }

  // 渲染拖拽上传模式
  return (
    <div className={className}>
      <Dragger {...uploadProps} className="mb-4">
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">{finalConfig.dragText}</p>
        <p className="ant-upload-hint">{finalConfig.dragHint}</p>
      </Dragger>
      
      {finalConfig.showDescription && (
        <Text type="secondary" className="text-sm">
          {getFileDescription(finalConfig.allowedExts, finalConfig.maxCount, finalConfig.maxSizeMB)}
        </Text>
      )}
    </div>
  );
};

export default FileUpload; 