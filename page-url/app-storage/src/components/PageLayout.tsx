import React from 'react';
import PageHeader, { PageHeaderProps } from './PageHeader';
import { shouldUseMicroAppMode } from '../utils/layoutConfig';

export interface PageLayoutProps extends PageHeaderProps {
  /** 页面内容 */
  children: React.ReactNode;
  /** 内容区域的自定义样式类名 */
  contentClassName?: string;
  /** 是否给内容区域添加默认的间距和样式 */
  defaultContentStyle?: boolean;
  /** 是否为微应用模式，微应用模式下不使用固定高度避免双滚动条 */
  isMicroApp?: boolean;
}

/**
 * 页面布局组件
 * 统一管理带固定header的页面布局
 */
const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  contentClassName = '',
  defaultContentStyle = true,
  isMicroApp,
  ...headerProps
}) => {
  // 自动检测或使用传入的微应用标识
  const isInMicroApp = isMicroApp !== undefined ? isMicroApp : shouldUseMicroAppMode();
  
  // 根据是否为微应用模式选择不同的布局策略
  if (isInMicroApp) {
    // 微应用模式：使用自然高度，避免双滚动条
    const contentClasses = `
      w-full
      ${defaultContentStyle ? 'p-6 bg-gray-50' : ''}
      ${contentClassName}
    `.trim();

    return (
      <div className="w-full bg-gray-50">
        <div className="w-full">
          <PageHeader {...headerProps} />
        </div>
        <div className={contentClasses}>
          {children}
        </div>
      </div>
    );
  }

  // 独立应用模式：使用固定高度和内部滚动
  const contentClasses = `
    flex-1 overflow-y-auto w-full
    ${defaultContentStyle ? 'p-6 bg-gray-50' : ''}
    ${contentClassName}
  `.trim();

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      <div className="flex-shrink-0">
        <PageHeader {...headerProps} />
      </div>
      <div className={contentClasses}>
        {children}
      </div>
    </div>
  );
};

export default PageLayout; 