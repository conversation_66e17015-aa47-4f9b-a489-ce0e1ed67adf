import React from 'react'
import { Outlet, Link } from "react-router-dom"
import styles from './BasicLayout.less'
import { shouldUseMicroAppMode } from '../utils/layoutConfig';

const BasicLayout: React.FC = props=> {
  const isMicroApp = shouldUseMicroAppMode();
  
  if (isMicroApp) {
    // 微应用模式：使用自然高度布局
    return (
      <div className={styles.microAppContainer}>
        <div className={styles.microAppContent}>
          <Outlet />
        </div>
      </div>
    );
  }
  
  // 独立应用模式：使用固定高度布局
  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <Outlet />
      </div>
    </div>
  )
}

export default BasicLayout