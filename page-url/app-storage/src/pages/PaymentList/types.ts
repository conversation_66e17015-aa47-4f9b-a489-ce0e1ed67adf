/**
 * 回款列表相关类型定义
 */

// 回款列表项数据接口
export interface PaymentListItem {
  id: string;
  序号: number;
  发票申请编号: string;
  合同名称: string;
  合同编码: string;
  客户名称: string;
  发票抬头: string;
  纳税人识别号: string;
  开票金额: number; // 万元
  回款金额: number; // 万元
  销售负责人: string;
  发票是否发送: string;
}

// 回款列表页面Props
export interface PaymentListProps {
  // 数据相关
  paymentList: PaymentListItem[];
  loading: boolean;
  error?: string;
  
  // 分页相关
  currentPage: number;
  pageSize: number;
  total: number;
  
  // 回调函数
  onPageChange: (page: number, size?: number) => void;
  onView: (itemId: string) => void;
  onConfirmPayment: (itemId: string) => void;
  onSubmitPayment: (itemId: string) => void;
  onSearch?: (searchText: string) => void;
  onRefresh?: () => void;
}

// 回款状态枚举
export enum PaymentStatus {
  PENDING = '未发送',
  SENT = '已发送',
  CONFIRMED = '已确认',
  COMPLETED = '已完成'
}

// 回款详情数据接口
export interface PaymentDetailData {
  id: string;
  付款申请编号: string;
  合同名称: string;
  合同编号: string;
  客户名称: string;
  发票申请编号: string;
  发票金额: number; // 万元
  付款金额: number; // 万元
  付款方式: string;
  付款银行: string;
  付款账户: string;
  付款时间: string;
  上传付款材料: string;
  备注: string;
}

// 回款详情页面Props
export interface PaymentDetailProps {
  paymentData: PaymentDetailData | null;
  loading: boolean;
  error?: string;
  onBack?: () => void;
  onRefresh?: () => void;
}

// 回款记录项
export interface PaymentRecordItem {
  id: string;
  回款时间: string;
  回款金额: number; // 万元
  备注: string;
}

// 发票详情数据接口
export interface InvoiceDetailData {
  id: string;
  发票申请编号: string;
  合同名称: string;
  合同编号: string;
  客户名称: string;
  发票抬头: string;
  纳税人识别号: string;
  开票金额: number; // 万元
  发票类型: string;
  税率: string;
  发票接收方式: string;
  收票人姓名: string;
  收票人地址邮箱: string;
  备注: string;
  发票发送状态: '未发送' | '已发送';
  实际回款时间金额: number; // 万元
  回款记录列表: PaymentRecordItem[];
}

// 发票详情页面Props
export interface InvoiceDetailProps {
  invoiceData: InvoiceDetailData | null;
  loading: boolean;
  error?: string;
  onBack?: () => void;
  onRefresh?: () => void;
  onConfirm?: () => void;
}

// 添加回款记录表单数据
export interface AddPaymentFormData {
  回款时间: string;
  回款金额: number;
  备注: string;
  回款凭证?: File;
  // 添加上传文件的详细信息
  上传文件信息?: {
    fileId: string;
    fileName: string;
    fileUrl: string;
    fileSize: number;
  }[];
}

// 添加回款弹窗Props
export interface AddPaymentModalProps {
  visible: boolean;
  loading: boolean;
  onCancel: () => void;
  onSubmit: (data: AddPaymentFormData) => Promise<void>;
}

// 表格列配置
export interface PaymentTableColumn {
  fixed?: 'left' | 'right';
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (text: any, record: PaymentListItem, index: number) => React.ReactNode;
} 