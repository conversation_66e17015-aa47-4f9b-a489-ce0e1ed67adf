# 回款列表页面

## 功能概述

回款列表页面用于展示和管理发票申请的回款信息，包括发票详情、客户信息、金额信息以及相关操作。

## 主要功能

### 1. 数据展示
- 发票申请编号、合同名称、合同编码
- 客户名称、发票抬头、纳税人识别号
- 开票金额和回款金额（万元）
- 销售负责人、发票发送状态

### 2. 操作功能
- **查看**: 查看回款详细信息
- **确认收款**: 确认收款操作
- **提交回款**: 提交回款信息

### 3. 交互功能
- **搜索**: 支持按合同名称、客户名称、发票申请编号、销售负责人搜索
- **分页**: 支持分页浏览，可调整每页显示数量
- **刷新**: 手动刷新数据

## 文件结构

```
PaymentList/
├── index.tsx              # 主页面组件
├── detail.tsx            # 详情页面组件
├── submit.tsx            # 发票详情页面组件
├── types.ts              # 类型定义
├── components/           # 子组件
│   ├── PaymentTable.tsx  # 回款表格组件
│   ├── AddPaymentModal.tsx # 添加回款弹窗组件
│   └── index.ts          # 组件导出
└── README.md             # 说明文档
```

## 组件说明

### PaymentList (主组件)
- 负责数据管理和业务逻辑
- 处理搜索、分页、刷新等交互
- 调用API获取数据（当前使用模拟数据）

### PaymentTable (表格组件)
- 负责数据展示和表格交互
- 支持排序、分页等功能
- 处理操作按钮的点击事件

### PaymentDetail (详情页面)
- 展示回款的完整详情信息
- 支持返回列表、刷新数据功能
- 支持下载付款材料附件

### InvoiceSubmit (发票详情页面)
- 展示发票的完整详情信息
- 支持修改发票发送状态
- 管理回款记录（添加、删除）
- 支持上传回款凭证
- 计算实际回款时间/金额汇总

### AddPaymentModal (添加回款弹窗)
- 支持选择回款时间
- 输入回款金额（万元）
- 填写备注信息
- 上传回款凭证文件

## 类型定义

### PaymentListItem
```typescript
interface PaymentListItem {
  id: string;
  序号: number;
  发票申请编号: string;
  合同名称: string;
  合同编码: string;
  客户名称: string;
  发票抬头: string;
  纳税人识别号: string;
  开票金额: number; // 万元
  回款金额: number; // 万元
  销售负责人: string;
  发票是否发送: string;
}
```

### PaymentDetailData
```typescript
interface PaymentDetailData {
  id: string;
  付款申请编号: string;
  合同名称: string;
  合同编号: string;
  客户名称: string;
  发票申请编号: string;
  发票金额: number; // 万元
  付款金额: number; // 万元
  付款方式: string;
  付款银行: string;
  付款账户: string;
  付款时间: string;
  上传付款材料: string;
  备注: string;
}
```

## 路由配置

在 `src/router.tsx` 中添加路由：
```typescript
{
  // 回款列表
  path: "/payment-list",
  element: <PaymentList />,
},
{
  // 回款详情
  path: "/payment-list/:paymentId",
  element: <PaymentDetail />,
},
{
  // 提交回款
  path: "/payment-list/:paymentId/submit",
  element: <InvoiceSubmit />,
}
```

## 使用方式

- 列表页面：`/payment-list`
- 详情页面：`/payment-list/:paymentId`
- 发票详情页面：`/payment-list/:paymentId/submit`

## 待完成功能

1. 集成真实API接口
2. ✅ 实现查看详情页面
3. 实现确认收款功能
4. ✅ 实现提交回款功能（发票详情页面）
5. ✅ 实现添加回款时间/金额功能
6. 添加数据导出功能
7. 添加高级筛选功能
8. 实现付款材料文件下载功能
9. 实现回款凭证文件上传和下载功能

## 开发规范

- 遵循项目的TypeScript类型安全规范
- 使用Tailwind CSS进行样式设计
- 组件大小控制在400行以内
- 使用React Hooks进行状态管理 