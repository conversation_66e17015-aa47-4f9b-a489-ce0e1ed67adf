import React, { useState, useCallback, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Input, Button, Space, Card, message } from "antd";
import { SearchOutlined, ReloadOutlined } from "@ant-design/icons";
import { PageHeader } from "@/components";
import type { HeaderAction } from "@/components";
import { PaymentTable } from "./components";
import type { PaymentListItem } from "./types";

const { Search } = Input;

/**
 * 回款列表页面组件
 */
const PaymentList: React.FC = () => {
  const navigate = useNavigate();
  // 模拟数据
  const mockPaymentData: PaymentListItem[] = [
    {
      id: "1",
      序号: 1,
      发票申请编号: "HT2025051800TSC",
      合同名称: "2025年四川省大府易享享数据平台合同",
      合同编码: "CT0092",
      客户名称: "成都大数据中心",
      发票抬头: "成都启明科技有限公司",
      纳税人识别号: "91110108M3333123X",
      开票金额: 3539.82,
      回款金额: 0,
      销售负责人: "李明",
      发票是否发送: "已完成",
    },
    {
      id: "2",
      序号: 2,
      发票申请编号: "HT2025051800TSC",
      合同名称: "2025年四川省大府易享享数据平台合同",
      合同编码: "CT0092",
      客户名称: "成都大数据中心",
      发票抬头: "成都启明科技有限公司",
      纳税人识别号: "91110108MA01A123X",
      开票金额: 3539.82,
      回款金额: 0,
      销售负责人: "李明",
      发票是否发送: "未发送",
    },
  ];

  // 状态管理
  const [paymentList, setPaymentList] =
    useState<PaymentListItem[]>(mockPaymentData);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState("");

  // 模拟数据加载
  const fetchPaymentData = useCallback(async (search?: string) => {
    setLoading(true);
    try {
      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 800));

      let filteredData = mockPaymentData;
      if (search) {
        filteredData = mockPaymentData.filter(
          (item) =>
            item.合同名称.includes(search) ||
            item.客户名称.includes(search) ||
            item.发票申请编号.includes(search) ||
            item.销售负责人.includes(search)
        );
      }

      setPaymentList(filteredData);
    } catch (error) {
      message.error("获取回款数据失败");
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始化数据
  useEffect(() => {
    fetchPaymentData();
  }, [fetchPaymentData]);

  // 分页处理
  const handlePageChange = useCallback(
    (page: number, size?: number) => {
      setCurrentPage(page);
      if (size && size !== pageSize) {
        setPageSize(size);
      }
    },
    [pageSize]
  );

  // 搜索处理
  const handleSearch = useCallback(
    (value: string) => {
      setSearchText(value);
      setCurrentPage(1);
      fetchPaymentData(value);
    },
    [fetchPaymentData]
  );

  // 刷新数据
  const handleRefresh = useCallback(() => {
    setCurrentPage(1);
    setSearchText("");
    fetchPaymentData();
  }, [fetchPaymentData]);

  // 查看详情
  const handleView = useCallback((itemId: string) => {
    navigate(`/payment-list/${itemId}`);
  }, [navigate]);

  // 确认收款
  const handleConfirmPayment = useCallback((itemId: string) => {
    message.success(`确认收款操作: ${itemId}`);
    // TODO: 实现确认收款功能
  }, []);

  // 提交回款
  const handleSubmitPayment = useCallback((itemId: string) => {
    navigate(`/payment-list/${itemId}/submit`);
  }, [navigate]);

  // 配置header操作按钮
  const headerActions: HeaderAction[] = [
    {
      key: 'refresh',
      label: '刷新',
      icon: <ReloadOutlined />,
      type: 'primary',
      loading: loading,
      onClick: handleRefresh,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader
        title="回款列表"
        actions={headerActions}
        bordered={true}
      />

      <div className="p-6">
        <Card className="mb-6">
          <div className="flex justify-between items-center">
            <Space>
              <Search
                placeholder="搜索合同名称、客户名称、发票申请编号或销售负责人"
                allowClear
                enterButton={<SearchOutlined />}
                size="middle"
                style={{ width: 400 }}
                onSearch={handleSearch}
                loading={loading}
              />
            </Space>

            <div className="text-gray-600">
              共找到 {paymentList.length} 条记录
            </div>
          </div>
        </Card>

        <PaymentTable
          dataSource={paymentList}
          loading={loading}
          currentPage={currentPage}
          pageSize={pageSize}
          total={paymentList.length}
          onPageChange={handlePageChange}
          onView={handleView}
          onConfirmPayment={handleConfirmPayment}
          onSubmitPayment={handleSubmitPayment}
        />
      </div>
    </div>
  );
};

export default PaymentList;
