import React from "react";
import { Table, Button, Space, Tag } from "antd";
import { EyeOutlined, CheckOutlined, SendOutlined } from "@ant-design/icons";
import type { PaymentListItem, PaymentTableColumn } from "../types";
import { ColumnsType } from "antd/es/table";

interface PaymentTableProps {
  dataSource: PaymentListItem[];
  loading: boolean;
  currentPage: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number, size?: number) => void;
  onView: (itemId: string) => void;
  onConfirmPayment: (itemId: string) => void;
  onSubmitPayment: (itemId: string) => void;
}

const PaymentTable: React.FC<PaymentTableProps> = ({
  dataSource,
  loading,
  currentPage,
  pageSize,
  total,
  onPageChange,
  onView,
  onConfirmPayment,
  onSubmitPayment,
}) => {
  const columns: ColumnsType<PaymentListItem> = [
    {
      title: "序号",
      dataIndex: "序号",
      key: "序号",
      width: 60,
      align: "center",
      fixed: "left",
    },
    {
      title: "发票申请编号",
      dataIndex: "发票申请编号",
      key: "发票申请编号",
      width: 150,
      fixed: "left",
    },
    {
      title: "合同名称",
      dataIndex: "合同名称",
      key: "合同名称",
      ellipsis: true,
    },
    {
      title: "合同编码",
      dataIndex: "合同编码",
      key: "合同编码",
    },
    {
      title: "客户名称",
      dataIndex: "客户名称",
      key: "客户名称",
      ellipsis: true,
    },
    {
      title: "发票抬头",
      dataIndex: "发票抬头",
      key: "发票抬头",
      ellipsis: true,
    },
    {
      title: "纳税人识别号",
      dataIndex: "纳税人识别号",
      key: "纳税人识别号",
      ellipsis: true,
    },
    {
      title: "开票金额 (万元)",
      dataIndex: "开票金额",
      key: "开票金额",
      align: "right",
      render: (amount: number) => (
        <span className="font-medium text-gray-900">{amount.toFixed(2)}</span>
      ),
    },
    {
      title: "回款金额 (万元)",
      dataIndex: "回款金额",
      key: "回款金额",
      align: "right",
      render: (amount: number) => (
        <span className="font-medium text-blue-600">{amount.toFixed(2)}</span>
      ),
    },
    {
      title: "销售负责人",
      dataIndex: "销售负责人",
      key: "销售负责人",
    },
    {
      title: "发票是否发送",
      dataIndex: "发票是否发送",
      key: "发票是否发送",
      width: 120,
      align: "center",
      render: (status: string) => {
        const statusConfig = {
          已完成: { color: "green", text: "已完成" },
          未发送: { color: "orange", text: "未发送" },
          已发送: { color: "blue", text: "已发送" },
        };
        const config = statusConfig[status as keyof typeof statusConfig] || {
          color: "default",
          text: status,
        };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: "操作",
      dataIndex: "action",
      key: "action",
      width: 200,
      align: "center",
      fixed: "right",
      render: (_, record: PaymentListItem) => (
        <Space size={[2, 2]}>
          <Button type="link" size="small" onClick={() => onView(record.id)}>
            发票详情
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => onConfirmPayment(record.id)}
          >
            确认收款
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => onSubmitPayment(record.id)}
          >
            提交回款
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={dataSource}
      loading={loading}
      pagination={{
        current: currentPage,
        pageSize: pageSize,
        total: total,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        pageSizeOptions: ["10", "20", "50", "100"],
        onChange: onPageChange,
        onShowSizeChange: onPageChange,
      }}
      rowKey="id"
      className="bg-white rounded-lg shadow-sm"
      size="middle"
      scroll={{ x: 1400 }}
    />
  );
};

export default PaymentTable;
