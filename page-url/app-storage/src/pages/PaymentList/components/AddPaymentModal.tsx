import React, { useState } from "react";
import {
  Modal,
  Form,
  DatePicker,
  InputNumber,
  Input,
  Upload,
  Button,
  message,
} from "antd";
import { UploadOutlined } from "@ant-design/icons";
import type { UploadProps } from "antd";
import type { UploadFile } from "antd/es/upload/interface";
import dayjs from "dayjs";
import type { AddPaymentModalProps, AddPaymentFormData } from "../types";

// 扩展UploadFile接口，添加自定义属性
interface ExtendedUploadFile extends UploadFile {
  fileId?: string;
}

const defaultButtonText = "点击上传";
const defaultExts = [
  "doc",
  "docx",
  "ppt",
  "pptx",
  "xls",
  "xlsx",
  "pdf",
  "jpg",
  "jpeg",
  "png",
  "zip",
  "rar",
];
const defaultMaxCount = 5;
const defaultMaxSizeMB = 10;
const defaultFieldId = "KQM19659";
const defaultStandardCollectionId = "KQM1892";
const defaultUploadUrl = "/app/api/io/file/uploadFile";

function getDesc(exts: string[], maxCount: number, maxSize: number) {
  const extStr = exts.join("/");
  return `只能上传${extStr}文件，文件数量不超过${maxCount}个，单个文件不超过${maxSize}M`;
}

const { TextArea } = Input;

/**
 * 添加回款时间/金额弹窗组件
 */
const AddPaymentModal: React.FC<AddPaymentModalProps> = ({
  visible,
  loading,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);

  // 处理文件上传变化
  const handleUploadChange: UploadProps["onChange"] = (info) => {
    let newFileList = [...info.fileList] as ExtendedUploadFile[];

    // 处理上传成功的文件
    newFileList = newFileList.map((file) => {
      if (file.response) {
        const baseUrl = "https://pms.tongbaninfo.com:8888/app";
        file.fileId = file.response?.data?.fileId;
        file.url = baseUrl + file.response?.data?.filePath;
      }
      return file;
    });

    setFileList(newFileList);
    form.setFieldsValue({ 回款凭证: newFileList });

    if (info.file.status === "done") {
      message.success(`${info.file.name} 文件上传成功`);
    } else if (info.file.status === "error") {
      message.error(`${info.file.name} 文件上传失败`);
    }
  };

  // 上传前的验证
  const beforeUpload = (file: File) => {
    const fileType = file.name.split(".").pop()?.toLowerCase() || "";
    const isValidExtension = defaultExts.includes(fileType);
    const isUnderMax = file.size / 1024 / 1024 < defaultMaxSizeMB;

    if (!isValidExtension) {
      message.error(`仅支持上传${defaultExts.join("/")}文件`);
      return Upload.LIST_IGNORE;
    }

    if (!isUnderMax) {
      message.error(`单个文件大小不能超过${defaultMaxSizeMB}MB`);
      return Upload.LIST_IGNORE;
    }

    if (fileList.length >= defaultMaxCount) {
      message.error(`最多只能上传${defaultMaxCount}个文件`);
      return Upload.LIST_IGNORE;
    }

    return true;
  };

  // 处理文件移除
  const handleRemove = (file: ExtendedUploadFile) => {
    const updatedFileList = fileList.filter((item) => item.uid !== file.uid);
    setFileList(updatedFileList);
    form.setFieldsValue({ 回款凭证: updatedFileList });
    return true;
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 获取上传成功的文件信息
      const uploadedFiles = fileList
        .filter(file => file.status === 'done' && file.fileId)
        .map(file => ({
          fileId: file.fileId!,
          fileName: file.name,
          fileUrl: file.url || '',
          fileSize: file.size || 0,
        }));
      
      const formData: AddPaymentFormData = {
        回款时间: values.回款时间.format("YYYY-MM-DD"),
        回款金额: values.回款金额,
        备注: values.备注 || "",
        回款凭证: fileList.length > 0 ? fileList[0]?.originFileObj : undefined,
        上传文件信息: uploadedFiles.length > 0 ? uploadedFiles : undefined,
      };

      console.log("提交的表单数据:", formData);
      console.log("上传的文件列表:", fileList);
      console.log("处理后的文件信息:", uploadedFiles);

      await onSubmit(formData);
      
      // 成功后重置表单
      form.resetFields();
      setFileList([]);
    } catch (error) {
      console.error("表单验证失败:", error);
    }
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    setFileList([]);
    onCancel();
  };

  return (
    <Modal
      title="添加实际回款时间/金额"
      visible={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          提交
        </Button>,
      ]}
      width={600}
      destroyOnClose
    >
      <Form form={form} layout="vertical" className="mt-4">
        <Form.Item
          label="回款时间"
          name="回款时间"
          rules={[{ required: true, message: "请选择回款时间" }]}
        >
          <DatePicker
            placeholder="选择时间"
            style={{ width: "100%" }}
            format="YYYY-MM-DD"
            disabledDate={(current) =>
              current && current > dayjs().endOf("day")
            }
          />
        </Form.Item>

        <Form.Item
          label="回款金额（万元）"
          name="回款金额"
          rules={[
            { required: true, message: "请输入回款金额" },
            { type: "number", min: 0.01, message: "回款金额必须大于0" },
          ]}
        >
          <InputNumber
            placeholder="请输入"
            style={{ width: "100%" }}
            min={0}
            precision={2}
            step={0.01}
          />
        </Form.Item>

        <Form.Item label="备注" name="备注">
          <TextArea
            placeholder="请输入备注信息"
            rows={4}
            maxLength={200}
            showCount
          />
        </Form.Item>

        <Form.Item label="回款凭证" name="回款凭证">
          <div style={{ display: "flex", alignItems: "center" }}>
            <div style={{ maxWidth: 250 }}>
              <Upload
                fileList={fileList}
                name="file"
                action={defaultUploadUrl}
                headers={{
                  authorization: "authorization-text",
                }}
                data={{
                  fieldId: defaultFieldId,
                  standardCollectionId: defaultStandardCollectionId,
                }}
                beforeUpload={beforeUpload}
                onChange={handleUploadChange}
                onRemove={handleRemove}
                multiple={true}
                accept={defaultExts.map((ext) => "." + ext).join(",")}
              >
                <Button
                  disabled={fileList.length >= defaultMaxCount}
                  type="primary"
                  icon={<UploadOutlined />}
                >
                  {defaultButtonText}
                </Button>
              </Upload>
            </div>
            <div style={{ color: "#999", fontSize: 12, marginLeft: 12 }}>
              {getDesc(defaultExts, defaultMaxCount, defaultMaxSizeMB)}
            </div>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddPaymentModal;
