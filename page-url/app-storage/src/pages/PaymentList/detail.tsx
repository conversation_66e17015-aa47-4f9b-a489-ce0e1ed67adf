import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Card, Descriptions, Spin, Alert, message, Button } from "antd";
import { ReloadOutlined, DownloadOutlined } from "@ant-design/icons";
import { PageLayout } from "@/components";
import type { HeaderAction } from "@/components";
import type { InvoiceDetailData } from "./types";

/**
 * 发票详情页面
 */
const InvoiceDetail: React.FC = () => {
  const { paymentId } = useParams<{ paymentId: string }>();
  const navigate = useNavigate();

  // 状态管理
  const [invoiceData, setInvoiceData] = useState<InvoiceDetailData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>();

  // 模拟数据获取函数
  const fetchInvoiceDetail = useCallback(async (id: string): Promise<void> => {
    setLoading(true);
    setError(undefined);

    try {
      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 模拟发票详情数据
      const mockDetailData: InvoiceDetailData = {
        id: id,
        发票申请编号: "FP2025061500",
        合同名称: "2025年四川省天府易享享数据平台合同",
        合同编号: "CT0092213",
        客户名称: "成都大数据中心",
        发票抬头: "北京启航科技有限公司",
        纳税人识别号: "91110108MA01A123X",
        开票金额: 120.00,
        发票类型: "增值税电子专票",
        税率: "13%",
        发票接收方式: "电子发票",
        收票人姓名: "张三",
        收票人地址邮箱: "<EMAIL>",
        备注: "结算第1期开发费程款项",
        发票发送状态: "未发送",
        实际回款时间金额: 0,
        回款记录列表: []
      };

      setInvoiceData(mockDetailData);
    } catch (err) {
      setError("获取发票详情失败，请稍后重试");
      console.error("获取发票详情失败:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 页面初始化时获取数据
  useEffect(() => {
    if (paymentId) {
      fetchInvoiceDetail(paymentId);
    } else {
      setError("缺少发票ID参数");
    }
  }, [paymentId, fetchInvoiceDetail]);

  // 返回按钮处理
  const handleBack = useCallback((): void => {
    navigate("/payment-list");
  }, [navigate]);

  // 刷新按钮处理
  const handleRefresh = useCallback((): void => {
    if (paymentId) {
      fetchInvoiceDetail(paymentId);
    }
  }, [paymentId, fetchInvoiceDetail]);

  // 配置header操作按钮
  const headerActions: HeaderAction[] = [
    {
      key: 'refresh',
      label: '刷新',
      icon: <ReloadOutlined />,
      type: 'primary',
      loading: loading,
      onClick: handleRefresh,
    },
  ];

  // 如果没有数据且不在加载中，显示错误信息
  if (!invoiceData && !loading && !error) {
    return (
      <PageLayout
        title="发票详情"
        showBack={true}
        onBack={handleBack}
        actions={headerActions}
      >
        <div className="flex justify-center items-center min-h-96">
          <div className="text-center">
            <p className="mb-4 text-gray-500">未找到发票信息</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      title="发票详情"
      showBack={true}
      onBack={handleBack}
      actions={headerActions}
    >
      <div className="space-y-6">
        <Card title="发票详情" className="shadow-sm">
          {error && (
            <Alert
              message="错误"
              description={error}
              type="error"
              showIcon
              className="mb-6"
            />
          )}

          <Spin spinning={loading}>
            {invoiceData && (
              <Descriptions
                bordered
                column={2}
                size="middle"
                labelStyle={{
                  width: "150px",
                  fontWeight: "500",
                  backgroundColor: "#fafafa",
                }}
                contentStyle={{
                  backgroundColor: "#ffffff",
                }}
              >
                <Descriptions.Item label="发票申请编号" span={2}>
                  <span className="font-medium text-blue-600">
                    {invoiceData.发票申请编号}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="合同名称" span={2}>
                  <span className="font-medium">{invoiceData.合同名称}</span>
                </Descriptions.Item>

                <Descriptions.Item label="合同编号">
                  {invoiceData.合同编号}
                </Descriptions.Item>

                <Descriptions.Item label="客户名称">
                  {invoiceData.客户名称}
                </Descriptions.Item>

                <Descriptions.Item label="发票抬头" span={2}>
                  <span className="font-medium">{invoiceData.发票抬头}</span>
                </Descriptions.Item>

                <Descriptions.Item label="纳税人识别号" span={2}>
                  <span className="font-mono text-sm">{invoiceData.纳税人识别号}</span>
                </Descriptions.Item>

                <Descriptions.Item label="开票金额（万元）">
                  <span className="text-lg font-medium text-green-600">
                    {invoiceData.开票金额.toFixed(2)}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="发票类型">
                  <span className="px-2 py-1 text-sm text-blue-800 bg-blue-100 rounded">
                    {invoiceData.发票类型}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="税率">
                  <span className="font-medium text-orange-600">
                    {invoiceData.税率}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="发票接收方式">
                  {invoiceData.发票接收方式}
                </Descriptions.Item>

                <Descriptions.Item label="收票人姓名">
                  {invoiceData.收票人姓名}
                </Descriptions.Item>

                <Descriptions.Item label="收票人地址/邮箱">
                  <span className="font-mono text-sm">{invoiceData.收票人地址邮箱}</span>
                </Descriptions.Item>

                <Descriptions.Item label="备注" span={2}>
                  <div className="p-3 bg-gray-50 rounded-md">
                    <span className="text-gray-700">{invoiceData.备注}</span>
                  </div>
                </Descriptions.Item>
              </Descriptions>
            )}
          </Spin>
        </Card>
      </div>
    </PageLayout>
  );
};

export default InvoiceDetail; 