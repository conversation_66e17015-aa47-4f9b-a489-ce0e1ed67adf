import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Card,
  Button,
  Descriptions,
  Radio,
  Table,
  Space,
  Spin,
  Alert,
  message,
} from "antd";
import {
  ReloadOutlined,
  PlusOutlined,
  DeleteOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import { PageLayout } from "@/components";
import type { HeaderAction } from "@/components";
import { AddPaymentModal } from "./components";
import type {
  InvoiceDetailData,
  PaymentRecordItem,
  AddPaymentFormData,
} from "./types";

/**
 * 发票详情页面（提交回款）
 */
const InvoiceSubmit: React.FC = () => {
  const { paymentId } = useParams<{ paymentId: string }>();
  const navigate = useNavigate();

  // 状态管理
  const [invoiceData, setInvoiceData] = useState<InvoiceDetailData | null>(
    null
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [error, setError] = useState<string>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalLoading, setModalLoading] = useState<boolean>(false);

  // 模拟数据获取函数
  const fetchInvoiceDetail = useCallback(async (id: string): Promise<void> => {
    setLoading(true);
    setError(undefined);

    try {
      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 模拟发票详情数据
      const mockInvoiceData: InvoiceDetailData = {
        id: id,
        发票申请编号: "FP2025061500",
        合同名称: "2025年四川省天府易享享数据平台合同",
        合同编号: "CT0092213",
        客户名称: "成都大数据中心",
        发票抬头: "北京启航科技有限公司",
        纳税人识别号: "91110108MA01A123X",
        开票金额: 120.0,
        发票类型: "增值税电子专票",
        税率: "13%",
        发票接收方式: "电子发票",
        收票人姓名: "张三",
        收票人地址邮箱: "<EMAIL>",
        备注: "结算第1期开发里程款项",
        发票发送状态: "未发送",
        实际回款时间金额: 85,
        回款记录列表: [
          {
            id: "1",
            回款时间: "2025-04-15 12:00:00",
            回款金额: 5,
            备注: "第一笔合同款项",
          },
          {
            id: "2",
            回款时间: "2025-05-10 12:00:00",
            回款金额: 30,
            备注: "项目阶段验收通过",
          },
          {
            id: "3",
            回款时间: "2025-05-22 12:00:00",
            回款金额: 50,
            备注: "项目阶段验收通过",
          },
        ],
      };

      setInvoiceData(mockInvoiceData);
    } catch (err) {
      setError("获取发票详情失败，请稍后重试");
      console.error("获取发票详情失败:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 页面初始化时获取数据
  useEffect(() => {
    if (paymentId) {
      fetchInvoiceDetail(paymentId);
    } else {
      setError("缺少发票ID参数");
    }
  }, [paymentId, fetchInvoiceDetail]);

  // 返回按钮处理
  const handleBack = useCallback((): void => {
    navigate("/payment-list");
  }, [navigate]);

  // 刷新按钮处理
  const handleRefresh = useCallback((): void => {
    if (paymentId) {
      fetchInvoiceDetail(paymentId);
    }
  }, [paymentId, fetchInvoiceDetail]);

  // 发票发送状态变化处理
  const handleStatusChange = useCallback(
    (value: string): void => {
      if (!invoiceData) return;

      setInvoiceData({
        ...invoiceData,
        发票发送状态: value as InvoiceDetailData["发票发送状态"],
      });
    },
    [invoiceData]
  );

  // 打开添加回款弹窗
  const handleAddPayment = useCallback((): void => {
    setModalVisible(true);
  }, []);

  // 关闭添加回款弹窗
  const handleModalCancel = useCallback((): void => {
    setModalVisible(false);
  }, []);

  // 提交添加回款
  const handleModalSubmit = useCallback(
    async (data: AddPaymentFormData): Promise<void> => {
      console.log("提交的值",data);
      setModalLoading(true);
      try {
        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 2000));

        if (!invoiceData) return;

        // 添加新的回款记录
        const newRecord: PaymentRecordItem = {
          id: Date.now().toString(),
          回款时间: data.回款时间,
          回款金额: data.回款金额,
          备注: data.备注,
        };

        const updatedData = {
          ...invoiceData,
          回款记录列表: [...invoiceData.回款记录列表, newRecord],
          实际回款时间金额: invoiceData.实际回款时间金额 + data.回款金额,
        };

        setInvoiceData(updatedData);
        setModalVisible(false);
        message.success("回款记录添加成功");
      } catch (err) {
        message.error("添加回款记录失败，请稍后重试");
        console.error("添加回款记录失败:", err);
      } finally {
        setModalLoading(false);
      }
    },
    [invoiceData]
  );

  // 删除回款记录
  const handleDeletePayment = useCallback(
    (recordId: string): void => {
      if (!invoiceData) return;

      const recordToDelete = invoiceData.回款记录列表.find(
        (item) => item.id === recordId
      );
      if (!recordToDelete) return;

      const updatedRecords = invoiceData.回款记录列表.filter(
        (item) => item.id !== recordId
      );
      const updatedData = {
        ...invoiceData,
        回款记录列表: updatedRecords,
        实际回款时间金额:
          invoiceData.实际回款时间金额 - recordToDelete.回款金额,
      };

      setInvoiceData(updatedData);
      message.success("回款记录删除成功");
    },
    [invoiceData]
  );

  // 确认提交
  const handleConfirm = useCallback(async (): Promise<void> => {
    if (!invoiceData) return;

    setSaving(true);
    try {
      // 模拟提交API调用
      await new Promise((resolve) => setTimeout(resolve, 2000));

      message.success("发票信息提交成功");
      navigate("/payment-list");
    } catch (err) {
      message.error("提交失败，请稍后重试");
      console.error("提交失败:", err);
    } finally {
      setSaving(false);
    }
  }, [invoiceData, navigate]);

  // 回款记录表格列定义
  const paymentColumns = [
    {
      title: "回款时间",
      dataIndex: "回款时间",
      key: "回款时间",
      width: 180,
    },
    {
      title: "回款金额（万元）",
      dataIndex: "回款金额",
      key: "回款金额",
      width: 140,
      render: (amount: number) => (
        <span className="font-medium text-blue-600">{amount.toFixed(2)}</span>
      ),
    },
    {
      title: "备注",
      dataIndex: "备注",
      key: "备注",
    },
    {
      title: "操作",
      key: "action",
      width: 80,
      render: (_: any, record: PaymentRecordItem) => (
        <Button
          type="link"
          danger
          size="small"
          icon={<DeleteOutlined />}
          onClick={() => handleDeletePayment(record.id)}
        >
          删除
        </Button>
      ),
    },
  ];
  // 配置header操作按钮
  const headerActions: HeaderAction[] = [
    ...[
      {
        key: "refresh",
        label: "刷新",
        icon: <ReloadOutlined />,
        type: "text" as const,
        disabled: saving,
        onClick: handleRefresh,
      },
    ],
    ...[
      {
        key: "confirm",
        label: "确认提交",
        type: "primary" as const,
        loading: saving,
        onClick: handleConfirm,
      },
    ],
  ];

  // 如果没有数据且不在加载中，显示错误信息
  if (!invoiceData && !loading && !error) {
    return (
      <PageLayout
        title="发票详情"
        showBack={true}
        onBack={handleBack}
        actions={headerActions}
      >
        <div className="flex justify-center items-center min-h-96">
          <div className="text-center">
            <p className="mb-4 text-gray-500">未找到发票信息</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      title="发票详情"
      showBack={true}
      onBack={handleBack}
      actions={headerActions}
    >
      <Card>
        {error && (
          <Alert
            message="错误"
            description={error}
            type="error"
            showIcon
            className="mb-6"
          />
        )}

        <Spin spinning={loading}>
          {invoiceData && (
            <>
              {/* 发票基本信息 */}
              <Descriptions
                bordered
                column={2}
                size="middle"
                className="mb-6"
                labelStyle={{
                  width: "150px",
                  fontWeight: "500",
                  backgroundColor: "#fafafa",
                }}
                contentStyle={{
                  backgroundColor: "#ffffff",
                }}
              >
                <Descriptions.Item label="发票申请编号" span={2}>
                  <span className="font-medium text-blue-600">
                    {invoiceData.发票申请编号}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="合同名称" span={2}>
                  <span className="font-medium">{invoiceData.合同名称}</span>
                </Descriptions.Item>

                <Descriptions.Item label="合同编号">
                  {invoiceData.合同编号}
                </Descriptions.Item>

                <Descriptions.Item label="客户名称">
                  {invoiceData.客户名称}
                </Descriptions.Item>

                <Descriptions.Item label="发票抬头">
                  {invoiceData.发票抬头}
                </Descriptions.Item>

                <Descriptions.Item label="纳税人识别号">
                  <span className="font-mono">{invoiceData.纳税人识别号}</span>
                </Descriptions.Item>

                <Descriptions.Item label="开票金额（万元）">
                  <span className="font-medium text-green-600">
                    {invoiceData.开票金额.toFixed(2)}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="发票类型">
                  {invoiceData.发票类型}
                </Descriptions.Item>

                <Descriptions.Item label="税率">
                  {invoiceData.税率}
                </Descriptions.Item>

                <Descriptions.Item label="发票接收方式">
                  {invoiceData.发票接收方式}
                </Descriptions.Item>

                <Descriptions.Item label="收票人姓名">
                  {invoiceData.收票人姓名}
                </Descriptions.Item>

                <Descriptions.Item label="收票人地址/邮箱">
                  {invoiceData.收票人地址邮箱}
                </Descriptions.Item>

                <Descriptions.Item label="备注" span={2}>
                  <div className="p-3 bg-gray-50 rounded-md">
                    <span className="text-gray-700">{invoiceData.备注}</span>
                  </div>
                </Descriptions.Item>
              </Descriptions>

              {/* 发票发送状态 */}
              <Card title="发票发送状态" className="mb-6">
               <Space >
                <span>发票是否已发送至客户</span>
               <Radio.Group
                  value={invoiceData.发票发送状态}
                  onChange={(e) => handleStatusChange(e.target.value)}
                >
                  <Radio value="未发送">未发送</Radio>
                  <Radio value="已发送">已发送</Radio>
                </Radio.Group>
               </Space>
              </Card>

              {/* 回款记录 */}
              <Card
                title="回款记录"
                extra={
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleAddPayment}
                  >
                    添加回款时间/金额
                  </Button>
                }
                className="mb-6"
              >
                <div className="mb-4">
                  <span className="text-gray-600">
                    实际回款时间/金额（万元）：
                  </span>
                  <span className="text-lg font-medium text-blue-600">
                    {invoiceData.实际回款时间金额.toFixed(2)}
                  </span>
                </div>

                <Table
                  columns={paymentColumns}
                  dataSource={invoiceData.回款记录列表}
                  rowKey="id"
                  pagination={false}
                  size="middle"
                />
              </Card>
            </>
          )}
        </Spin>
      </Card>

      {/* 添加回款弹窗 */}
      <AddPaymentModal
        visible={modalVisible}
        loading={modalLoading}
        onCancel={handleModalCancel}
        onSubmit={handleModalSubmit}
      />
    </PageLayout>
  );
};

export default InvoiceSubmit;
