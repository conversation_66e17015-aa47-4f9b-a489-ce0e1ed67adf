// 加班记录数据类型
export interface OvertimeRecord {
  id: string;
  name: string;
  department: string;
  role: string;
  project: string;
  product: string;
  startTime: string;
  endTime: string;
  hours: number;
  status: string;
}

// 统计数据类型
export interface Stats {
  totalRecords: number;
  totalHours: number;
  uniqueEmployees: number;
  uniqueDepartments: number;
  uniqueProjects: number;
  avgHours: string;
}

// 过滤器状态类型
export interface FilterState {
  roles: string[];
  projects: string[];
  departments: string[];
  employees: string[];
  statuses: string[];
  startDate: string;
  endDate: string;
}

// 过滤器选项类型
export interface FilterOptions {
  roles: Array<{ label: string; value: string }>;
  projects: Array<{ label: string; value: string }>;
  departments: Array<{ label: string; value: string }>;
  employees: Array<{ label: string; value: string }>;
  statuses: Array<{ label: string; value: string }>;
}

// 个人汇总数据类型
export interface PersonalSummary {
  name: string;
  totalHours: number;
  recordCount: number;
  department: string;
  avgHours: string;
  records: OvertimeRecord[];
}

// 部门分析数据类型
export interface DepartmentAnalysis {
  department: string;
  totalHours: number;
  recordCount: number;
  employeeCount: number;
  avgHours: string;
}

// 项目分析数据类型
export interface ProjectAnalysis {
  project: string;
  totalHours: number;
  recordCount: number;
  employeeCount: number;
  avgHours: string;
}

// 角色分析数据类型
export interface RoleAnalysis {
  role: string;
  totalHours: number;
  recordCount: number;
  employeeCount: number;
  avgHours: string;
}

// 时间分析数据类型
export interface TimeAnalysis {
  date: string;
  totalHours: number;
  recordCount: number;
  avgHours: string;
} 