import React, { useState } from "react";
import {
  Card,
  Table,
  Typography,
  Tag,
  Button,
  Modal,
  Tabs,
  List,
  Space,
  Statistic,
} from "antd";
import {
  ExpandAltOutlined,
  CalendarOutlined,
  BarChartOutlined,
} from "@ant-design/icons";
import OvertimeCalendar from "./OvertimeCalendar";

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface OvertimeRecord {
  id: string;
  name: string;
  department: string;
  role: string;
  project: string;
  product: string;
  startTime: string;
  endTime: string;
  hours: number;
  status: string;
  details?: string; // 跨天加班的详细信息
}

interface PersonalSummary {
  name: string;
  totalHours: number;
  recordCount: number;
  department: string;
  avgHours: string;
  records: OvertimeRecord[];
}

interface OvertimeListProps {
  data: OvertimeRecord[];
}

const OvertimeList: React.FC<OvertimeListProps> = ({ data }) => {
  const [selectedEmployee, setSelectedEmployee] =
    useState<PersonalSummary | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // 生成个人汇总数据
  const generatePersonalSummary = (): PersonalSummary[] => {
    const personalStats: {
      [key: string]: {
        hours: number;
        count: number;
        department: string;
        records: OvertimeRecord[];
      };
    } = {};
    console.log("data=========", data);
    const newData = data.filter((el) => el.name === "张辉");
    console.log("newData=========", newData);
    // 处理每条加班记录
    data.forEach((item) => {
      if (!personalStats[item.name]) {
        personalStats[item.name] = {
          hours: 0,
          count: 0,
          department: item.department,
          records: [],
        };
      }

      // 判断是否跨天加班
      const startDate = new Date(item.startTime).toDateString();
      const endDate = new Date(item.endTime).toDateString();
      const isCrossDay = startDate !== endDate;

            if (isCrossDay && item.details) {
        // 跨天加班，解析details字段
        try {
          const detailsArray = JSON.parse(item.details);
          
          // 检查details数组是否有有效数据
          if (detailsArray && detailsArray.length > 0) {
            detailsArray.forEach(
              (detail: { date: string; workHours: string }) => {
                const dailyHours = parseFloat(detail.workHours);
                
                // 为每一天创建一个单独的记录
                const dailyRecord: OvertimeRecord = {
                  ...item,
                  id: `${item.id}_${detail.date}`,
                  startTime: `${detail.date} ${item.startTime.split(" ")[1]}`,
                  endTime: `${detail.date} ${item.endTime.split(" ")[1]}`,
                  hours: dailyHours,
                };

                personalStats[item.name].hours += dailyHours;
                personalStats[item.name].count += 1;
                personalStats[item.name].records.push(dailyRecord);
              }
            );
          } else {
            // details为空数组，按原始数据处理
            personalStats[item.name].hours += item.hours;
            personalStats[item.name].count += 1;
            personalStats[item.name].records.push(item);
          }
        } catch (error) {
          console.error("解析details字段失败:", error);
          // 如果解析失败，按原来的方式处理
          personalStats[item.name].hours += item.hours;
          personalStats[item.name].count += 1;
          personalStats[item.name].records.push(item);
        }
      } else {
        // 单天加班，直接使用原始数据
        personalStats[item.name].hours += item.hours;
        personalStats[item.name].count += 1;
        personalStats[item.name].records.push(item);
      }
    });

    return Object.entries(personalStats)
      .map(([name, stats]) => ({
        name,
        totalHours: Math.round(stats.hours * 10) / 10, // 保留一位小数
        recordCount: stats.count,
        department: stats.department,
        avgHours:
          stats.count > 0 ? (stats.hours / stats.count).toFixed(1) : "0",
        records: stats.records.sort(
          (a, b) =>
            new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
        ),
      }))
      .sort((a, b) => b.totalHours - a.totalHours);
  };

  const personalSummaryData = generatePersonalSummary();
  console.log("加班数据列表=========", personalSummaryData);
  const item = personalSummaryData.find((el) => el.name === "张辉");
  console.log("item=========", item);
  // 查看详情
  const handleViewDetails = (record: PersonalSummary) => {
    setSelectedEmployee(record);
    setModalVisible(true);
  };

  // 表格列定义
  const columns = [
    {
      title: "排名",
      key: "rank",
      width: 80,
      render: (_: any, __: any, index: number) => {
        // 计算全局排名
        const globalRank = (currentPage - 1) * pageSize + index + 1;

        return (
          <div className="text-center">
            {globalRank <= 3 ? (
              <Tag
                color={
                  globalRank === 1
                    ? "gold"
                    : globalRank === 2
                    ? "silver"
                    : "#cd7f32"
                }
              >
                {globalRank}
              </Tag>
            ) : (
              <span className="text-gray-500">{globalRank}</span>
            )}
          </div>
        );
      },
    },
    {
      title: "姓名",
      dataIndex: "name",
      key: "name",
      width: 120,
      render: (name: string, record: PersonalSummary) => (
        <div>
          <div className="font-medium">{name}</div>
          <div className="text-xs text-gray-500">{record.department}</div>
        </div>
      ),
    },
    {
      title: "总加班时长",
      dataIndex: "totalHours",
      key: "totalHours",
      width: 120,
      sorter: (a: PersonalSummary, b: PersonalSummary) =>
        a.totalHours - b.totalHours,
      render: (hours: number) => (
        <div className="text-center">
          <div className="text-lg font-bold text-red-600">{hours}</div>
          <div className="text-xs text-gray-500">小时</div>
        </div>
      ),
    },
    {
      title: "加班次数",
      dataIndex: "recordCount",
      key: "recordCount",
      width: 100,
      sorter: (a: PersonalSummary, b: PersonalSummary) =>
        a.recordCount - b.recordCount,
      render: (count: number) => (
        <div className="text-center">
          <div className="text-lg font-bold text-blue-600">{count}</div>
          <div className="text-xs text-gray-500">次</div>
        </div>
      ),
    },
    {
      title: "平均时长",
      dataIndex: "avgHours",
      key: "avgHours",
      width: 100,
      sorter: (a: PersonalSummary, b: PersonalSummary) =>
        parseFloat(a.avgHours) - parseFloat(b.avgHours),
      render: (avgHours: string) => (
        <div className="text-center">
          <div className="text-lg font-bold text-green-600">{avgHours}</div>
          <div className="text-xs text-gray-500">小时/次</div>
        </div>
      ),
    },
    {
      title: "操作",
      key: "action",
      width: 120,
      render: (_: any, record: PersonalSummary) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<ExpandAltOutlined />}
            onClick={() => handleViewDetails(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  // 详情记录表格列
  const detailColumns = [
    {
      title: "日期",
      dataIndex: "startTime",
      key: "startTime",
      render: (startTime: string) => startTime.split(" ")[0],
    },

    {
      title: "时长(小时)",
      dataIndex: "hours",
      key: "hours",
      render: (hours: number) => hours.toFixed(1),
    },
    {
      title: "项目",
      dataIndex: "project",
      key: "project",
      ellipsis: true,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      render: (status: string) => (
        <Tag color={status === "审批通过" ? "green" : "orange"}>{status}</Tag>
      ),
    },
  ];

  return (
    <Card className="mb-6">
      <div className="mb-4">
        <Title level={3}>👥 员工加班总时长数据</Title>
        <Text type="secondary">
          按总加班时长排序，点击详情可查看具体加班记录和日历视图
        </Text>
      </div>

      <Table
        columns={columns}
        dataSource={personalSummaryData}
        rowKey="name"
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 名员工`,
          onChange: (page, size) => {
            setCurrentPage(page);
            setPageSize(size || 20);
          },
          onShowSizeChange: (current, size) => {
            setCurrentPage(1); // 改变页面大小时回到第一页
            setPageSize(size);
          },
        }}
        scroll={{ x: 800 }}
      />

      {/* 详情弹窗 */}
      <Modal
        title={
          <div className="flex gap-2 items-center">
            <span>{selectedEmployee?.name} 的加班详情</span>
            <Tag color="blue">{selectedEmployee?.department}</Tag>
          </div>
        }
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={1200}
        footer={null}
        destroyOnClose
      >
        {selectedEmployee && (
          <div>
            {/* 统计概览 */}
            <div className="p-4 mb-4 bg-gray-50 rounded-lg">
              <div className="grid grid-cols-4 gap-4">
                <Statistic
                  title="总加班时长"
                  value={selectedEmployee.totalHours}
                  suffix="小时"
                  valueStyle={{ color: "#cf1322" }}
                />
                <Statistic
                  title="加班次数"
                  value={selectedEmployee.recordCount}
                  suffix="次"
                  valueStyle={{ color: "#1890ff" }}
                />
                <Statistic
                  title="平均时长"
                  value={selectedEmployee.avgHours}
                  suffix="小时/次"
                  valueStyle={{ color: "#52c41a" }}
                />
                <Statistic
                  title="最近加班"
                  value={
                    selectedEmployee.records[0]?.startTime.split(" ")[0] || "无"
                  }
                  valueStyle={{ color: "#722ed1" }}
                />
              </div>
            </div>

            <Tabs defaultActiveKey="records">
              <TabPane
                tab={
                  <span>
                    <BarChartOutlined />
                    加班记录
                  </span>
                }
                key="records"
              >
                <Table
                  columns={detailColumns}
                  dataSource={selectedEmployee.records}
                  rowKey="id"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                  }}
                  scroll={{ x: 600 }}
                />
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <CalendarOutlined />
                    加班日历
                  </span>
                }
                key="calendar"
              >
                <OvertimeCalendar
                  records={selectedEmployee.records}
                  employeeName={selectedEmployee.name}
                />
              </TabPane>
            </Tabs>
          </div>
        )}
      </Modal>
    </Card>
  );
};

export default OvertimeList;
