import React from "react";
import { <PERSON>, Statistic, <PERSON>, Col } from "antd";

interface Stats {
  totalRecords: number;
  totalHours: number;
  uniqueEmployees: number;
  uniqueDepartments: number;
  uniqueProjects: number;
  avgHours: string;
}

interface StatsCardsProps {
  stats: Stats;
}

const StatsCards: React.FC<StatsCardsProps> = ({ stats }) => {
  return (
    <Row gutter={[16, 16]} className="mb-6">
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card>
          <Statistic
            title="有效加班记录数"
            value={stats.totalRecords}
            valueStyle={{ color: "#3f8600" }}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card>
          <Statistic
            title="总加班时长(小时)"
            value={stats.totalHours}
            valueStyle={{ color: "#cf1322" }}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card>
          <Statistic
            title="加班员工数"
            value={stats.uniqueEmployees}
            valueStyle={{ color: "#1890ff" }}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card>
          <Statistic
            title="涉及部门数"
            value={stats.uniqueDepartments}
            valueStyle={{ color: "#722ed1" }}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card>
          <Statistic
            title="涉及商机项目数"
            value={stats.uniqueProjects}
            valueStyle={{ color: "#fa8c16" }}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card>
          <Statistic
            title="平均加班时长(小时)"
            value={stats.avgHours}
            valueStyle={{ color: "#52c41a" }}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default StatsCards; 