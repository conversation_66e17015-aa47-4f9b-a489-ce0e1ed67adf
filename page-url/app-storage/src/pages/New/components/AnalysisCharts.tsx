import React from "react";
import { <PERSON>, Typography, Row, Col, Alert } from "antd";
import { Bar, Doughnut, Pie, Line } from "react-chartjs-2";

const { Title } = Typography;

interface OvertimeRecord {
  id: string;
  name: string;
  department: string;
  role: string;
  project: string;
  product: string;
  startTime: string;
  endTime: string;
  hours: number;
  status: string;
}

interface AnalysisChartsProps {
  data: OvertimeRecord[];
}

const AnalysisCharts: React.FC<AnalysisChartsProps> = ({ data }) => {
  // 图表配置
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "bottom" as const,
      },
    },
  };

  // 部门分析
  const analyzeDepartments = (data: OvertimeRecord[]) => {
    const deptStats: {
      [key: string]: { hours: number; count: number; employees: Set<string> };
    } = {};

    data.forEach((item) => {
      if (!deptStats[item.department]) {
        deptStats[item.department] = {
          hours: 0,
          count: 0,
          employees: new Set(),
        };
      }
      deptStats[item.department].hours += item.hours;
      deptStats[item.department].count += 1;
      deptStats[item.department].employees.add(item.name);
    });

    return Object.entries(deptStats)
      .map(([dept, stats]) => ({
        department: dept,
        totalHours: Math.round(stats.hours),
        recordCount: stats.count,
        employeeCount: stats.employees.size,
        avgHours: (stats.hours / stats.count).toFixed(1),
      }))
      .sort((a, b) => b.totalHours - a.totalHours);
  };

  // 商机分析
  const analyzeProjects = (data: OvertimeRecord[]) => {
    const projectStats: {
      [key: string]: { hours: number; count: number; employees: Set<string> };
    } = {};

    data.forEach((item) => {
      const project = item.project || "未分配";
      if (!projectStats[project]) {
        projectStats[project] = {
          hours: 0,
          count: 0,
          employees: new Set(),
        };
      }
      projectStats[project].hours += item.hours;
      projectStats[project].count += 1;
      projectStats[project].employees.add(item.name);
    });

    return Object.entries(projectStats)
      .map(([project, stats]) => ({
        project,
        totalHours: Math.round(stats.hours),
        recordCount: stats.count,
        employeeCount: stats.employees.size,
        avgHours: (stats.hours / stats.count).toFixed(1),
      }))
      .sort((a, b) => b.totalHours - a.totalHours);
  };

  // 角色分析
  const analyzeRoles = (data: OvertimeRecord[]) => {
    const roleStats: {
      [key: string]: { hours: number; count: number; employees: Set<string> };
    } = {};

    data.forEach((item) => {
      if (!roleStats[item.role]) {
        roleStats[item.role] = { hours: 0, count: 0, employees: new Set() };
      }
      roleStats[item.role].hours += item.hours;
      roleStats[item.role].count += 1;
      roleStats[item.role].employees.add(item.name);
    });

    return Object.entries(roleStats)
      .map(([role, stats]) => ({
        role,
        totalHours: Math.round(stats.hours),
        recordCount: stats.count,
        employeeCount: stats.employees.size,
        avgHours: (stats.hours / stats.count).toFixed(1),
      }))
      .sort((a, b) => b.totalHours - a.totalHours);
  };

  // 时间分析
  const analyzeTime = (data: OvertimeRecord[]) => {
    const timeStats: { [key: string]: { hours: number; count: number } } = {};

    data.forEach((item) => {
      if (item.startTime) {
        const date = item.startTime.split(" ")[0];
        if (!timeStats[date]) {
          timeStats[date] = { hours: 0, count: 0 };
        }
        timeStats[date].hours += item.hours;
        timeStats[date].count += 1;
      }
    });

    return Object.entries(timeStats)
      .map(([date, stats]) => ({
        date,
        totalHours: Math.round(stats.hours),
        recordCount: stats.count,
        avgHours: (stats.hours / stats.count).toFixed(1),
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  };

  const deptAnalysis = analyzeDepartments(data);
  const projectAnalysis = analyzeProjects(data);
  const roleAnalysis = analyzeRoles(data);
  const timeAnalysis = analyzeTime(data);

  // 部门图表数据
  const departmentChartData = {
    labels: deptAnalysis.map((d) => d.department),
    datasets: [
      {
        label: "加班时长(小时)",
        data: deptAnalysis.map((d) => d.totalHours),
        backgroundColor: "rgba(102, 126, 234, 0.8)",
        borderColor: "rgba(102, 126, 234, 1)",
        borderWidth: 1,
      },
    ],
  };

  // 部门人数饼图数据
  const departmentCountData = {
    labels: deptAnalysis.map((d) => d.department),
    datasets: [
      {
        data: deptAnalysis.map((d) => d.employeeCount),
        backgroundColor: [
          "#FF6384",
          "#36A2EB",
          "#FFCE56",
          "#4BC0C0",
          "#9966FF",
          "#FF9F40",
          "#FF6384",
          "#C9CBCF",
        ],
      },
    ],
  };

  // 商机图表数据
  const projectChartData = {
    labels: projectAnalysis
      .slice(0, 8)
      .map((d) =>
        d.project.length > 25 ? d.project.substring(0, 25) + "..." : d.project
      ),
    datasets: [
      {
        label: "加班时长(小时)",
        data: projectAnalysis.slice(0, 8).map((d) => d.totalHours),
        backgroundColor: "rgba(54, 162, 235, 0.8)",
        borderColor: "rgba(54, 162, 235, 1)",
        borderWidth: 1,
      },
    ],
  };

  // 角色饼图数据
  const roleChartData = {
    labels: roleAnalysis.map((d) => d.role),
    datasets: [
      {
        data: roleAnalysis.map((d) => d.totalHours),
        backgroundColor: [
          "#FF6384",
          "#36A2EB",
          "#FFCE56",
          "#4BC0C0",
          "#9966FF",
          "#FF9F40",
        ],
      },
    ],
  };

  // 时间趋势数据
  const timeChartData = {
    labels: timeAnalysis.map((d) => d.date),
    datasets: [
      {
        label: "每日加班时长(小时)",
        data: timeAnalysis.map((d) => d.totalHours),
        borderColor: "rgba(255, 159, 64, 1)",
        backgroundColor: "rgba(255, 159, 64, 0.2)",
        borderWidth: 2,
        fill: true,
      },
    ],
  };

  return (
    <div>
      {/* 部门分析 */}
      <Card className="mb-6">
        <Title level={3}>部门加班分析</Title>
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="各部门加班时长统计" size="small">
              <div className="h-80">
                <Bar data={departmentChartData} options={chartOptions} />
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="各部门加班人数统计" size="small">
              <div className="h-80">
                <Doughnut data={departmentCountData} options={chartOptions} />
              </div>
            </Card>
          </Col>
        </Row>
        <Alert
          message="部门分析要点"
          description={
            <div>
              <p>
                <strong>加班时长最多的部门：</strong>
                {deptAnalysis[0]?.department} ({deptAnalysis[0]?.totalHours}
                小时)
              </p>
              <p>
                <strong>加班人数最多的部门：</strong>
                {
                  deptAnalysis.sort(
                    (a, b) => b.employeeCount - a.employeeCount
                  )[0]?.department
                }{" "}
                (
                {
                  deptAnalysis.sort(
                    (a, b) => b.employeeCount - a.employeeCount
                  )[0]?.employeeCount
                }
                人)
              </p>
            </div>
          }
          type="warning"
          showIcon
          className="mt-4"
        />
      </Card>

      {/* 商机分析 */}
      <Card className="mb-6">
        <Title level={3}>商机项目分析</Title>
        <Card title="各商机加班时长统计" size="small">
          <div className="h-96">
            <Bar
              data={projectChartData}
              options={{
                ...chartOptions,
                indexAxis: "y" as const,
                scales: {
                  x: {
                    beginAtZero: true,
                  },
                },
              }}
            />
          </div>
        </Card>
        <Alert
          message="商机分析要点"
          description={
            <div>
              <p>
                <strong>加班时长最多的商机：</strong>
                {projectAnalysis[0]?.project} ({projectAnalysis[0]?.totalHours}
                小时)
              </p>
              <p>
                <strong>总商机项目数：</strong>
                {projectAnalysis.length}个
              </p>
            </div>
          }
          type="warning"
          showIcon
          className="mt-4"
        />
      </Card>

      {/* 角色分析 */}
      <Card className="mb-6">
        <Title level={3}>角色分析</Title>
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="各角色加班时长分布" size="small">
              <div className="h-80">
                <Pie data={roleChartData} options={chartOptions} />
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="各角色平均加班时长" size="small">
              <div className="h-80">
                <Bar
                  data={{
                    labels: roleAnalysis.map((d) => d.role),
                    datasets: [
                      {
                        label: "平均加班时长(小时)",
                        data: roleAnalysis.map((d) => parseFloat(d.avgHours)),
                        backgroundColor: "rgba(255, 99, 132, 0.8)",
                        borderColor: "rgba(255, 99, 132, 1)",
                        borderWidth: 1,
                      },
                    ],
                  }}
                  options={{
                    ...chartOptions,
                    scales: {
                      y: {
                        beginAtZero: true,
                      },
                    },
                  }}
                />
              </div>
            </Card>
          </Col>
        </Row>
        <Alert
          message="角色分析要点"
          description={
            <p>
              <strong>加班时长最多的角色：</strong>
              {roleAnalysis[0]?.role} ({roleAnalysis[0]?.totalHours}小时)
            </p>
          }
          type="warning"
          showIcon
          className="mt-4"
        />
      </Card>

      {/* 时间趋势分析 */}
      <Card className="mb-6">
        <Title level={3}>时间趋势分析</Title>
        <div className="h-96">
          <Line data={timeChartData} options={chartOptions} />
        </div>
        <Alert
          message="时间趋势要点"
          description={
            <p>
              <strong>加班时长最多的日期：</strong>
              {
                timeAnalysis.sort((a, b) => b.totalHours - a.totalHours)[0]
                  ?.date
              }{" "}
              (
              {
                timeAnalysis.sort((a, b) => b.totalHours - a.totalHours)[0]
                  ?.totalHours
              }
              小时)
            </p>
          }
          type="warning"
          showIcon
          className="mt-4"
        />
      </Card>
    </div>
  );
};

export default AnalysisCharts; 