import React from "react";
import { Card, Button, Select, Row, Col, Space, Typography, DatePicker } from "antd";
import moment from "moment";

const { Title, Text } = Typography;
const { Option } = Select;

interface FilterState {
  roles: string[];
  projects: string[];
  departments: string[];
  employees: string[];
  statuses: string[];
  startDate: string;
  endDate: string;
}

interface FilterOptions {
  roles: Array<{ label: string; value: string }>;
  projects: Array<{ label: string; value: string }>;
  departments: Array<{ label: string; value: string }>;
  employees: Array<{ label: string; value: string }>;
  statuses: Array<{ label: string; value: string }>;
}

interface FilterPanelProps {
  filters: FilterState;
  filterOptions: FilterOptions;
  loading: boolean;
  onFiltersChange: (filters: FilterState) => void;
  onApplyFilters: () => void;
  onResetFilters: () => void;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  filterOptions,
  loading,
  onFiltersChange,
  onApplyFilters,
  onResetFilters,
}) => {
  const updateFilter = (key: keyof FilterState, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  return (
    <Card className="mb-6">
      <div className="flex justify-between items-center mb-4">
        <Title level={3} className="!mb-0">
          🔍 数据过滤器
        </Title>
        <Space>
          <Button type="primary" onClick={onApplyFilters} loading={loading}>
            应用过滤
          </Button>
          <Button onClick={onResetFilters}>重置过滤</Button>
        </Space>
      </div>

      <Row gutter={[16, 16]}>
        {/* 项目角色过滤 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div className="space-y-2">
            <Text strong>项目角色：</Text>
            <Select
              mode="multiple"
              placeholder="选择项目角色"
              value={filters.roles}
              onChange={(values) => updateFilter('roles', values)}
              className="w-full"
              maxTagCount="responsive"
              allowClear
            >
              {filterOptions.roles.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </div>
        </Col>

        {/* 项目过滤 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div className="space-y-2">
            <Text strong>所属项目：</Text>
            <Select
              mode="multiple"
              placeholder="选择项目"
              value={filters.projects}
              onChange={(values) => updateFilter('projects', values)}
              className="w-full"
              maxTagCount="responsive"
              allowClear
            >
              {filterOptions.projects.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </div>
        </Col>

        {/* 部门过滤 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div className="space-y-2">
            <Text strong>部门：</Text>
            <Select
              mode="multiple"
              placeholder="选择部门"
              value={filters.departments}
              onChange={(values) => updateFilter('departments', values)}
              className="w-full"
              maxTagCount="responsive"
              allowClear
            >
              {filterOptions.departments.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </div>
        </Col>

        {/* 开始时间过滤 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div className="space-y-2">
            <Text strong>开始时间：</Text>
            <DatePicker
              placeholder="选择开始时间"
              value={filters.startDate ? moment(filters.startDate) : null}
              onChange={(date) =>
                updateFilter('startDate', date ? date.format("YYYY-MM-DD") : "")
              }
              className="w-full"
              format="YYYY-MM-DD"
              allowClear
            />
          </div>
        </Col>

        {/* 结束时间过滤 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <div className="space-y-2">
            <Text strong>结束时间：</Text>
            <DatePicker
              placeholder="选择结束时间"
              value={filters.endDate ? moment(filters.endDate) : null}
              onChange={(date) =>
                updateFilter('endDate', date ? date.format("YYYY-MM-DD") : "")
              }
              className="w-full"
              format="YYYY-MM-DD"
              allowClear
            />
          </div>
        </Col>
      </Row>
    </Card>
  );
};

export default FilterPanel; 