import React, { useState } from "react";
import { Calendar, Badge, Card, Typography, List, Tag, Button } from "antd";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import moment from "moment";

const { Text, Title } = Typography;

interface OvertimeRecord {
  id: string;
  name: string;
  department: string;
  role: string;
  project: string;
  product: string;
  startTime: string;
  endTime: string;
  hours: number;
  status: string;
}

interface OvertimeCalendarProps {
  records: OvertimeRecord[];
  employeeName: string;
}

const OvertimeCalendar: React.FC<OvertimeCalendarProps> = ({
  records,
  employeeName,
}) => {
  // 当前显示的月份
  const [currentMonth, setCurrentMonth] = useState(moment());

  // 触摸滑动相关状态
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // 按日期分组加班记录
  const recordsByDate = records.reduce((acc, record) => {
    if (record.startTime) {
      const date = record.startTime.split(" ")[0];
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(record);
    }
    return acc;
  }, {} as { [key: string]: OvertimeRecord[] });

  // 根据加班时长获取颜色等级
  const getOvertimeLevel = (hours: number) => {
    if (hours >= 8)
      return { level: "extreme", color: "#ff1744", bgColor: "#ffebee" }; // 极重度加班 - 红色
    if (hours >= 6)
      return { level: "heavy", color: "#ff5722", bgColor: "#fff3e0" }; // 重度加班 - 深橙色
    if (hours >= 4)
      return { level: "moderate", color: "#ff9800", bgColor: "#fff8e1" }; // 中度加班 - 橙色
    if (hours >= 2)
      return { level: "light", color: "#ffc107", bgColor: "#fffde7" }; // 轻度加班 - 黄色
    return { level: "minimal", color: "#4caf50", bgColor: "#f1f8e9" }; // 最少加班 - 绿色
  };

  // 日历单元格渲染
  const dateCellRender = (value: moment.Moment) => {
    const dateStr = value.format("YYYY-MM-DD");
    const dayRecords = recordsByDate[dateStr] || [];

    if (dayRecords.length === 0) return null;

    const totalHours = dayRecords.reduce(
      (sum, record) => sum + record.hours,
      0
    );
    const { color, bgColor } = getOvertimeLevel(totalHours);

    return (
      <div className="overflow-hidden relative">
        {/* 背景高亮 */}
        <div
          className="absolute inset-0 rounded-md opacity-20 animate-pulse"
          style={{ backgroundColor: color }}
        />

        {/* 主要内容 */}
        <div className="relative z-10 p-1">
          {/* 加班次数徽章 */}
          <div className="flex justify-center mb-1">
            <Badge
              count={dayRecords.length}
              style={{
                backgroundColor: color,
                fontWeight: "bold",
                fontSize: "10px",
                boxShadow: `0 2px 8px ${color}40`,
              }}
              size="small"
            />
          </div>

          {/* 时长显示 */}
          <div
            className="text-center text-xs font-bold rounded px-1 py-0.5 shadow-sm"
            style={{
              color: color,
              backgroundColor: bgColor,
              border: `1px solid ${color}30`,
            }}
          >
            {totalHours.toFixed(1)}h
          </div>

          {/* 强度指示器 */}
          <div className="flex justify-center mt-1">
            <div
              className="w-4 h-1 rounded-full"
              style={{ backgroundColor: color }}
            />
          </div>
        </div>
      </div>
    );
  };

  // 月份单元格渲染
  const monthCellRender = (value: moment.Moment) => {
    const monthStr = value.format("YYYY-MM");
    const monthRecords = Object.keys(recordsByDate)
      .filter((date) => date.startsWith(monthStr))
      .reduce(
        (acc, date) => acc.concat(recordsByDate[date]),
        [] as OvertimeRecord[]
      );

    if (monthRecords.length === 0) return null;

    const totalHours = monthRecords.reduce(
      (sum, record) => sum + record.hours,
      0
    );
    const { color, bgColor } = getOvertimeLevel(totalHours);

    return (
      <div
        className="p-2 text-center rounded-lg border-2 shadow-md"
        style={{
          backgroundColor: bgColor,
          borderColor: color + "40",
        }}
      >
        <div className="text-sm font-bold" style={{ color: color }}>
          {monthRecords.length}次
        </div>
        <div className="text-xs font-medium" style={{ color: color }}>
          {totalHours.toFixed(1)}h
        </div>
      </div>
    );
  };

  // 选择日期时显示详情
  const onSelect = (value: moment.Moment) => {
    const dateStr = value.format("YYYY-MM-DD");
    const dayRecords = recordsByDate[dateStr] || [];

    if (dayRecords.length > 0) {
      // 这里可以触发显示详情的回调
      console.log(`${dateStr} 的加班记录:`, dayRecords);
    }
  };

  // 月份切换函数
  const handlePrevMonth = () => {
    setCurrentMonth((prev) => prev.clone().subtract(1, "month"));
  };

  const handleNextMonth = () => {
    setCurrentMonth((prev) => prev.clone().add(1, "month"));
  };

  // 触摸事件处理
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      handleNextMonth(); // 左滑切换到下个月
    }
    if (isRightSwipe) {
      handlePrevMonth(); // 右滑切换到上个月
    }
  };

  // 获取当前显示月份的加班统计
  const getCurrentMonthStats = () => {
    const monthStr = currentMonth.format("YYYY-MM");
    const currentMonthRecords = Object.keys(recordsByDate)
      .filter((date) => date.startsWith(monthStr))
      .reduce(
        (acc, date) => acc.concat(recordsByDate[date]),
        [] as OvertimeRecord[]
      );

    const totalHours = currentMonthRecords.reduce(
      (sum, record) => sum + record.hours,
      0
    );
    const totalDays = new Set(
      currentMonthRecords.map((r) => r.startTime.split(" ")[0])
    ).size;

    return {
      totalRecords: currentMonthRecords.length,
      totalHours: totalHours.toFixed(1),
      totalDays,
      avgHours:
        currentMonthRecords.length > 0
          ? (totalHours / currentMonthRecords.length).toFixed(1)
          : "0",
    };
  };

  const monthStats = getCurrentMonthStats();

  return (
    <Card>
      <div className="mb-4">
        <Title level={4}>📅 {employeeName} 的加班日历</Title>

        {/* 自定义月份切换头部 */}
        <div className="flex justify-between items-center p-3 mb-3 bg-gray-50 rounded-lg">
          <Button
            type="text"
            icon={<LeftOutlined />}
            onClick={handlePrevMonth}
            className="flex gap-1 items-center hover:bg-blue-50"
          >
            上个月
          </Button>

          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">
              {currentMonth.format("YYYY年MM月")}
            </div>
            <div className="text-xs text-gray-500">滑动切换月份</div>
          </div>

          <Button
            type="text"
            icon={<RightOutlined />}
            onClick={handleNextMonth}
            className="flex gap-1 items-center hover:bg-blue-50"
          >
            下个月
          </Button>
        </div>

        <div className="flex gap-4 mb-3 text-sm text-gray-600">
          <span>当月加班: {monthStats.totalRecords}次</span>
          <span>总时长: {monthStats.totalHours}小时</span>
          <span>加班天数: {monthStats.totalDays}天</span>
          <span>平均时长: {monthStats.avgHours}小时/次</span>
        </div>

        {/* 颜色图例 */}
        <div className="flex flex-wrap gap-2 text-xs">
          <div className="flex gap-1 items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>轻度 (&lt;2h)</span>
          </div>
          <div className="flex gap-1 items-center">
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <span>一般 (2-4h)</span>
          </div>
          <div className="flex gap-1 items-center">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span>较重 (4-6h)</span>
          </div>
          <div className="flex gap-1 items-center">
            <div className="w-3 h-3 bg-red-600 rounded-full"></div>
            <span>严重 (6-8h)</span>
          </div>
        </div>
      </div>

      <div
        className="calendar-container"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <Calendar
          value={currentMonth}
          dateCellRender={dateCellRender}
          monthCellRender={monthCellRender}
          onSelect={onSelect}
          headerRender={() => null} // 隐藏默认的头部
          className="rounded-lg border overtime-calendar"
        />
      </div>

      <div className="mt-4">
        <Text type="secondary" className="text-xs">
          💡
          提示：颜色越鲜艳表示加班时长越长，数字表示当天加班次数和总时长。点击日期可查看详细记录。
        </Text>
      </div>

      {/* 添加自定义样式 */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
          .calendar-container {
            touch-action: pan-x;
            user-select: none;
            position: relative;
            overflow: hidden;
          }
          
          .overtime-calendar {
            transition: transform 0.3s ease;
          }
          
          .overtime-calendar .ant-picker-calendar-date-content {
            height: 60px !important;
          }
          
          .overtime-calendar .ant-picker-cell-inner {
            padding: 2px !important;
          }
          
          .overtime-calendar .ant-picker-calendar-date {
            border-radius: 8px;
            transition: all 0.3s ease;
          }
          
          .overtime-calendar .ant-picker-calendar-date:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          }
          
          /* 隐藏默认的头部选择器 */
          .overtime-calendar .ant-picker-calendar-header {
            display: none !important;
          }
          
          /* 月份切换按钮样式 */
          .ant-btn-text:hover {
            background-color: #e6f7ff !important;
            border-color: #91d5ff !important;
          }
          
          @keyframes pulse-overtime {
            0%, 100% { opacity: 0.2; }
            50% { opacity: 0.4; }
          }
          
          .animate-pulse {
            animation: pulse-overtime 2s infinite;
          }
          
          /* 滑动提示动画 */
          @keyframes swipe-hint {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(5px); }
          }
          
          .calendar-container::before {
            content: '';
            position: absolute;
            top: 50%;
            right: 10px;
            width: 0;
            height: 0;
            border-left: 8px solid #1890ff;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
            opacity: 0.3;
            animation: swipe-hint 2s infinite;
            z-index: 1;
          }
          
          .calendar-container::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 10px;
            width: 0;
            height: 0;
            border-right: 8px solid #1890ff;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
            opacity: 0.3;
            animation: swipe-hint 2s infinite reverse;
            z-index: 1;
          }
        `,
        }}
      />
    </Card>
  );
};

export default OvertimeCalendar;
