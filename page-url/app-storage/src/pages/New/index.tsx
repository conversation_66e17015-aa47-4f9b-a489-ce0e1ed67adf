import React, { useState, useEffect } from "react";
import moment from "moment";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  Filler,
} from "chart.js";
import { Card, Spin, Typography, Alert } from "antd";

// 导入组件
import FilterPanel from "./components/FilterPanel";
import StatsCards from "./components/StatsCards";
import AnalysisCharts from "./components/AnalysisCharts";
import OvertimeList from "./components/OvertimeList";

// 导入类型
import type {
  OvertimeRecord,
  Stats,
  FilterState,
  FilterOptions,
} from "./types";

const { Title: AntTitle, Text } = Typography;

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  Filler
);

// API调用函数（简化版，不包含过滤器）
const fetchOvertimeData = async (
  pageNo: number = 1,
  pageSize: number = 500
): Promise<any> => {
  const baseRequestData = {
    requestType: "code",
    layoutId: "coordinate_KQM4479",
    resourceType: "menu",
    resourceId: "KQM1652",
    standardCollectionId: "KQM1833",
    pageNo,
    pageSize,
    tempFilterBoxDataMap: null,
    showModelType: "list",
    filterBoxDataMap: {
      columnFilterObj: null,
      filterObj: null,
      listViewFilterObj: null,
      searchFilterObj: null,
      viewId: "all_KQM1833",
    },
    needToRecordLastViewId: true,
    lastViewId: "all_KQM1833",
    filterBoxData: null,
    _crumb: "********************************",
  };

  try {
    const response = await fetch("/app/api/standardList/front/getListData", {
      method: "POST",
      headers: {
        Accept: "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Content-Type": "application/json",
        _lang: "zh_CN",
      },
      credentials: "include",
      body: JSON.stringify(baseRequestData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("获取加班数据失败:", error);
    throw error;
  }
};

// 获取所有数据（分页获取）
const fetchAllOvertimeData = async (): Promise<any[]> => {
  try {
    // 先获取第一页数据以获取总数
    const firstPageData = await fetchOvertimeData(1, 500);
    const total = firstPageData.data.pagination.total;
    console.log("total=========", firstPageData);
    const pageSize = 500;
    const totalPages = Math.ceil(total / pageSize);

    let allData = [...firstPageData.data.body];

    // 如果有多页，继续获取剩余页面
    if (totalPages > 1) {
      const promises = [];
      for (let page = 2; page <= totalPages; page++) {
        promises.push(fetchOvertimeData(page, pageSize));
      }

      const remainingPages = await Promise.all(promises);
      remainingPages.forEach((pageData) => {
        allData = allData.concat(pageData.data.body);
      });
    }

    console.log(`成功获取 ${allData.length} 条加班记录`);
    return allData;
  } catch (error) {
    console.error("获取所有加班数据失败:", error);
    return [];
  }
};

const New = () => {
  const [loading, setLoading] = useState(true);
  const [processedData, setProcessedData] = useState<OvertimeRecord[]>([]);
  const [allRawData, setAllRawData] = useState<any[]>([]);
  const [stats, setStats] = useState<Stats>({
    totalRecords: 0,
    totalHours: 0,
    uniqueEmployees: 0,
    uniqueDepartments: 0,
    uniqueProjects: 0,
    avgHours: "0",
  });

  // 过滤器状态
  const [filters, setFilters] = useState<FilterState>({
    roles: [],
    projects: [],
    departments: [],
    employees: [],
    statuses: [],
    startDate: "",
    endDate: "",
  });

  // 过滤选项数据
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    roles: [],
    projects: [],
    departments: [],
    employees: [],
    statuses: [],
  });

  // 数据处理函数
  const processData = (jsonData: any[]): OvertimeRecord[] => {
    if (!jsonData || jsonData.length === 0) {
      return [];
    }

    const originalCount = jsonData.length;

    const filteredData = jsonData
      .filter((item) => {
        // 过滤掉已作废和审批驳回的数据
        const data = item.data || item;
        const status1 = data.KQM18605?.text || "";
        const status2 = data.KQM19019?.text || "";

        // 只保留审批通过的数据
        const isValidStatus1 = status1 === "审批通过" || status1 === "";
        const isValidStatus2 = status2 === "审批通过" || status2 === "";

        // 排除已作废和审批驳回的数据
        const isNotInvalid =
          status1 !== "已作废" &&
          status1 !== "审批驳回" &&
          status2 !== "已作废" &&
          status2 !== "审批驳回";

        return isNotInvalid && (isValidStatus1 || isValidStatus2);
      })
      .map((item) => {
        const data = item.data || item;
        return {
          id: item.id || Math.random().toString(),
          name: data.KQM18601?.text || data.KQM18573?.text || "未知",
          department: data.KQM19194?.text || "未知部门",
          role: data.KQM18986?.text || "未知角色",
          project: data.KQM19158?.text || data.KQM19159?.text,
          product: data.KQM19159?.text || "未分配",
          startTime: data.KQM18602?.text || "",
          endTime: data.KQM18603?.text || "",
          hours: data.KQM19197?.value || data.KQM19197?.text || 0,
          status: data.KQM18605?.text || "未知状态",
          details: data.KQM18970?.text || "[]",
        };
      });

    const filteredCount = filteredData.length;
    const filteredOutCount = originalCount - filteredCount;

    console.log(
      `数据过滤统计: 原始数据 ${originalCount} 条, 有效数据 ${filteredCount} 条, 过滤掉 ${filteredOutCount} 条`
    );

    return filteredData;
  };

  // 生成统计数据
  const generateStats = (data: OvertimeRecord[]): Stats => {
    const totalRecords = data.length;
    const totalHours = data.reduce((sum, item) => sum + item.hours, 0);
    const uniqueEmployees = new Set(data.map((item) => item.name)).size;
    const uniqueDepartments = new Set(data.map((item) => item.department)).size;
    const uniqueProjects = new Set(
      data.map((item) => item.project).filter((p) => p && p !== "未分配")
    ).size;
    const avgHours = totalHours / totalRecords;

    return {
      totalRecords,
      totalHours: Math.round(totalHours),
      uniqueEmployees,
      uniqueDepartments,
      uniqueProjects,
      avgHours: avgHours.toFixed(1),
    };
  };

  // 从原始数据中提取过滤选项
  const extractFilterOptions = (rawData: any[]) => {
    const roles = new Set<string>();
    const projects = new Set<string>();
    const departments = new Set<string>();
    const employees = new Set<string>();
    const statuses = new Set<string>();

    rawData.forEach((item) => {
      const data = item.data || item;

      if (data.KQM18986?.text) roles.add(data.KQM18986.text);
      if (data.KQM19158?.text) projects.add(data.KQM19158.text);
      if (data.KQM19194?.text) departments.add(data.KQM19194.text);
      if (data.KQM18601?.text) employees.add(data.KQM18601.text);
      if (data.KQM18605?.text) statuses.add(data.KQM18605.text);
    });

    setFilterOptions({
      roles: Array.from(roles).map((r) => ({ label: r, value: r })),
      projects: Array.from(projects).map((p) => ({ label: p, value: p })),
      departments: Array.from(departments).map((d) => ({ label: d, value: d })),
      employees: Array.from(employees).map((e) => ({ label: e, value: e })),
      statuses: Array.from(statuses).map((s) => ({ label: s, value: s })),
    });
  };

  // 本地过滤原始数据
  const filterRawDataLocally = (
    rawData: any[],
    filters: FilterState
  ): any[] => {
    if (!rawData || rawData.length === 0) return [];

    return rawData.filter((item) => {
      const data = item.data || item;

      // 项目角色过滤
      if (filters.roles.length > 0) {
        const role = data.KQM18986?.text || "";
        if (!filters.roles.includes(role)) return false;
      }

      // 项目过滤
      if (filters.projects.length > 0) {
        const project = data.KQM19158?.text || "";
        if (!filters.projects.includes(project)) return false;
      }

      // 部门过滤
      if (filters.departments.length > 0) {
        const department = data.KQM19194?.text || "";
        if (!filters.departments.includes(department)) return false;
      }

      // 人员过滤
      if (filters.employees.length > 0) {
        const employee = data.KQM18601?.text || "";
        if (!filters.employees.includes(employee)) return false;
      }

      // 状态过滤
      if (filters.statuses.length > 0) {
        const status = data.KQM18605?.text || "";
        if (!filters.statuses.includes(status)) return false;
      }

      // 时间过滤
      if (filters.startDate || filters.endDate) {
        const startTime = data.KQM18602?.text || "";
        if (startTime) {
          const itemDate = startTime.split(" ")[0]; // 获取日期部分

          if (filters.startDate && itemDate < filters.startDate) return false;
          if (filters.endDate && itemDate > filters.endDate) return false;
        }
      }

      return true;
    });
  };

  // 加载数据（初始加载，不使用过滤器）
  const loadData = async () => {
    setLoading(true);
    try {
      const rawData = await fetchAllOvertimeData(); // 不传递过滤器，获取所有数据
      setAllRawData(rawData);
      extractFilterOptions(rawData);

      const processed = processData(rawData);
      setProcessedData(processed);
      setStats(generateStats(processed));
    } catch (error) {
      console.error("加载数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  // 应用过滤器（本地筛选）
  const applyFilters = () => {
    setLoading(true);
    try {
      // 对原始数据进行本地过滤
      const filteredRawData = filterRawDataLocally(allRawData, filters);
      const processed = processData(filteredRawData);
      setProcessedData(processed);
      setStats(generateStats(processed));
    } catch (error) {
      console.error("应用过滤器失败:", error);
    } finally {
      setLoading(false);
    }
  };

  // 重置过滤器
  const resetFilters = () => {
    const emptyFilters = {
      roles: [],
      projects: [],
      departments: [],
      employees: [],
      statuses: [],
      startDate: "",
      endDate: "",
    };
    setFilters(emptyFilters);

    // 重置后显示所有数据
    const processed = processData(allRawData);
    setProcessedData(processed);
    setStats(generateStats(processed));
  };

  // 初始加载数据
  useEffect(() => {
    loadData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <Spin size="large" tip="数据加载中...">
          <div className="w-32 h-32"></div>
        </Spin>
      </div>
    );
  }

  return (
    <div className="p-4 mx-auto max-w-7xl min-h-screen bg-gray-50">
      {/* 头部 */}
      <Card className="mb-6 text-white bg-gradient-to-r from-blue-500 to-purple-600 border-0">
        <div className="text-center">
          <AntTitle level={1} className="!text-white !mb-2">
            加班数据分析报告
          </AntTitle>
          <Text className="block mb-2 text-lg text-white">
            基于员工加班记录的多维度数据分析
          </Text>
          <Text className="text-white">
            报告生成时间：{new Date().toLocaleString("zh-CN")}
          </Text>
        </div>
      </Card>

      {/* 过滤器区域 */}
      <FilterPanel
        filters={filters}
        filterOptions={filterOptions}
        loading={loading}
        onFiltersChange={setFilters}
        onApplyFilters={applyFilters}
        onResetFilters={resetFilters}
      />

      {/* 统计卡片 */}
      <StatsCards stats={stats} />

      {/* 数据说明 */}
      <Alert
        message="📊 数据说明"
        description={
          <div>
            <p>数据来源：实时从加班管理系统API获取</p>
            <p>
              已自动过滤掉"已作废"和"审批驳回"状态的加班记录，仅显示有效的加班数据。
            </p>
            <p>
              支持多维度过滤：项目角色、项目、项目类型、部门、人员、时间、状态等。
            </p>
            <p>
              项目类型说明：商机项目（KQM19158字段）、产品项目（KQM19159字段）
            </p>
          </div>
        }
        type="info"
        showIcon
        className="mb-6"
      />

      {/* 分析图表 */}
      <AnalysisCharts data={processedData} />
      {/* 加班总时长列表 - 新功能 */}
      <OvertimeList data={processedData} />
    </div>
  );
};

export default New;
