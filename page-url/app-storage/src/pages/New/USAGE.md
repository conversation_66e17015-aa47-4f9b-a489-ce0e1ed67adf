# 加班数据分析系统使用说明

## 功能概述

本系统已从静态JSON文件数据源升级为实时API数据源，支持多维度数据过滤和分析。

## 主要改进

### 1. 数据源升级
- ✅ 从静态JSON文件改为实时API获取
- ✅ 支持分页获取所有数据（pageSize=500，自动获取所有页面）
- ✅ 移除了Tab切换功能，直接显示所有数据

### 2. 多维度过滤器
支持以下过滤维度：

#### 项目角色过滤
- 字段：`KQM18986`
- 支持多选：项目组长、销售协调人、交付协调人、交付人员、研发人员等

#### 项目过滤
- 字段：`KQM19158`
- 支持多选：按所属商机项目过滤

#### 部门过滤
- 字段：`KQM19194`
- 支持多选：按部门过滤

#### 人员过滤
- 字段：`KQM18601`
- 支持多选：按具体人员过滤

#### 审批状态过滤
- 字段：`KQM18605`
- 支持多选：审批通过、审批中、已作废、审批驳回等

#### 时间过滤
- 字段：`KQM18602`
- 支持日期范围：开始时间和结束时间

## 使用方法

### 1. 基本使用
1. 页面加载时自动获取所有加班数据
2. 系统自动过滤掉"已作废"和"审批驳回"状态的记录
3. 显示统计卡片和各种分析图表

### 2. 使用过滤器
1. 在过滤器区域选择需要的过滤条件
2. 支持多选（按住Ctrl/Cmd键多选）
3. 点击"应用过滤"按钮获取过滤后的数据
4. 点击"重置过滤"按钮清除所有过滤条件

### 3. 数据分析
系统提供以下分析维度：
- 部门加班分析（时长统计、人数统计）
- 商机项目分析
- 角色分析（时长分布、平均时长）
- 时间趋势分析
- 个人统计（排行榜）

## API接口说明

### 接口地址
```
POST https://pms.tongbaninfo.com:8888/app/api/standardList/front/getListData
```

### 请求参数
```json
{
  "requestType": "code",
  "layoutId": "coordinate_KQM4479",
  "resourceType": "menu",
  "resourceId": "KQM1652",
  "standardCollectionId": "KQM1833",
  "pageNo": 1,
  "pageSize": 500,
  "filterBoxDataMap": {
    "columnFilterObj": null,
    "filterObj": null,
    "listViewFilterObj": null,
    "searchFilterObj": null,
    "viewId": "all_KQM1833"
  }
}
```

### 过滤条件格式
```json
{
  "filterBoxDataMap": {
    "columnFilterObj": {
      "expression": "1",
      "conditions": [
        {
          "fieldCode": "KQM18986",
          "fieldType": "RADIO",
          "fieldName": "项目角色",
          "operator": "CONTAIN",
          "text": {
            "dataConfigMap": {"masterStyle": {}},
            "text": ["研发人员"],
            "value": ["研发人员"]
          },
          "value": ["研发人员"],
          "valueName": "研发人员",
          "type": null,
          "serialNumber": 1
        }
      ],
      "isAdvanced": true
    }
  }
}
```

## 字段映射

| 显示名称 | 字段代码 | 说明 |
|---------|---------|------|
| 姓名 | KQM18601 | 员工姓名 |
| 部门 | KQM19194 | 所属部门 |
| 项目角色 | KQM18986 | 在项目中的角色 |
| 所属商机 | KQM19158 | 商机项目名称 |
| 所属产品 | KQM19159 | 产品名称 |
| 开始时间 | KQM18602 | 加班开始时间 |
| 结束时间 | KQM18603 | 加班结束时间 |
| 加班时长 | KQM19197 | 加班小时数 |
| 审批状态 | KQM18605 | 当前审批状态 |

## 注意事项

1. **认证要求**：需要登录系统并具有相应权限
2. **跨域处理**：使用`credentials: 'include'`处理Cookie认证
3. **数据过滤**：系统自动过滤无效状态的记录
4. **性能优化**：支持分页获取，避免一次性加载过多数据
5. **错误处理**：包含完整的错误处理和用户提示

## 测试

可以使用 `test.html` 文件测试API连接和过滤功能：
1. 打开 `test.html` 文件
2. 点击"测试获取第一页数据"验证基本API调用
3. 点击"测试带过滤条件"验证过滤功能

## 技术栈

- React + TypeScript
- Chart.js (图表库)
- Less (样式)
- Fetch API (数据获取) 