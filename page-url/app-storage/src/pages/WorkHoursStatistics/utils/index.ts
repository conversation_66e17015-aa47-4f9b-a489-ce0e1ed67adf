import React from 'react';
import { Tag, Typography } from 'antd';
import { WorkHoursFilterConditions, StandardListParams } from '../../../api';
import { FilterState, FilterOptions, WorkHoursRecord, ChartData, AnalysisData } from '../types';

const { Text } = Typography;

// 图表颜色配置
export const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

// 字段代码映射 - 根据实际API数据结构
export const FIELD_CODES = {
  workDate: 'HW21967',        // 工时日期
  department: 'KQM19098',     // 部门 (KQM1909)
  employee: 'KQM19036',       // 填写人员 (KQM19036)
  workType: 'HW23543',        // 工时类型 (HW23543)
  approvalStatus: 'HW23575',  // 审批状态 (HW23575)
  opportunityProject: 'KQM17348', // 所属商机项目小组 (KQM17348)
  productProject: 'KQM18553',    // 所属产品项目 (KQM18553)
  consultingTask: 'KQM20885',    // 所属咨询任务 (KQM20885)
  contractProject: 'KQM19356',   // 所属合同项目 (KQM19356)
  workHours: 'KQM17303',         // 工时小时 (KQM17303)
};

// 构建标准列表查询参数
export const buildStandardListParams = (
  filters: FilterState,
  filterOptions: FilterOptions,
  pageNo: number = 1,
  pageSize: number = 500
): StandardListParams => {
  const conditions: any[] = [];
  let serialNumber = 1;

  // 日期范围筛选
  if (filters.dateRange.length === 2) {
    conditions.push({
      fieldCode: FIELD_CODES.workDate,
      fieldType: "DATE",
      fieldName: "工时日期",
      operator: "RANGE",
      text: {
        value: [filters.dateRange[0], filters.dateRange[1]]
      },
      value: [filters.dateRange[0], filters.dateRange[1]],
      type: null,
      serialNumber: serialNumber++
    });
  }

  // 部门筛选
  if (filters.departments.length > 0) {
    const departmentOptions = filters.departments.map(deptValue => {
      const option = filterOptions.departments.find(opt => opt.value === deptValue);
      return {
        text: option?.label || deptValue,
        value: deptValue,
        icon: null,
        positionStatus: null
      };
    });

    conditions.push({
      fieldCode: FIELD_CODES.department,
      fieldType: "MULTITOONE",
      fieldName: "部门",
      operator: "CONTAIN",
      text: {
        icon: departmentOptions.map(() => null),
        text: departmentOptions.map(opt => opt.text),
        value: departmentOptions.map(opt => opt.value),
        positionStatus: departmentOptions.map(() => null)
      },
      value: departmentOptions.map(opt => opt.value),
      type: null,
      serialNumber: serialNumber++
    });
  }

  // 员工筛选
  if (filters.employees.length > 0) {
    const employeeOptions = filters.employees.map(empValue => {
      const option = filterOptions.employees.find(opt => opt.value === empValue);
      return {
        text: option?.label || empValue,
        value: empValue,
        icon: null,
        positionStatus: null
      };
    });

    conditions.push({
      fieldCode: FIELD_CODES.employee,
      fieldType: "IDRELATIONSHIP",
      fieldName: "员工",
      operator: "CONTAIN",
      text: {
        icon: employeeOptions.map(() => null),
        text: employeeOptions.map(opt => opt.text),
        value: employeeOptions.map(opt => opt.value),
        positionStatus: employeeOptions.map(() => null)
      },
      value: employeeOptions.map(opt => opt.value),
      type: null,
      serialNumber: serialNumber++
    });
  }

  // 工时类型筛选
  if (filters.workTypeValues.length > 0) {
    const workTypeOptions = filters.workTypeValues.map(typeValue => {
      const option = filterOptions.workTypes.find(opt => opt.value === typeValue);
      return {
        text: option?.label || typeValue,
        value: typeValue
      };
    });

    conditions.push({
      fieldCode: FIELD_CODES.workType,
      fieldType: "RADIO",
      fieldName: "工时类型",
      operator: "CONTAIN",
      text: {
        dataConfigMap: {
          masterStyle: {}
        },
        text: workTypeOptions.map(opt => opt.text),
        value: workTypeOptions.map(opt => opt.value)
      },
      value: workTypeOptions.map(opt => opt.value),
      valueName: workTypeOptions.length === 1 ? workTypeOptions[0].text : undefined,
      type: null,
      serialNumber: serialNumber++
    });
  }

  // 审批状态筛选
  if (filters.approvalStatuses.length > 0) {
    const statusOptions = filters.approvalStatuses.map(statusValue => {
      const option = filterOptions.approvalStatuses.find(opt => opt.value === statusValue);
      return {
        text: option?.label || statusValue,
        value: statusValue
      };
    });

    conditions.push({
      fieldCode: FIELD_CODES.approvalStatus,
      fieldType: "RADIO",
      fieldName: "审批状态",
      operator: "CONTAIN",
      text: {
        dataConfigMap: {
          masterStyle: {}
        },
        text: statusOptions.map(opt => opt.text),
        value: statusOptions.map(opt => opt.value)
      },
      value: statusOptions.map(opt => opt.value),
      valueName: statusOptions.length === 1 ? statusOptions[0].text : undefined,
      type: null,
      serialNumber: serialNumber++
    });
  }

  // 商机项目筛选
  if (filters.opportunityProjects.length > 0) {
    const projectOptions = filters.opportunityProjects.map(projectValue => {
      const option = filterOptions.projects.find(opt => opt.value === projectValue);
      return {
        text: option?.label || projectValue,
        value: projectValue,
        icon: null,
        positionStatus: null
      };
    });

    conditions.push({
      fieldCode: FIELD_CODES.opportunityProject,
      fieldType: "MULTITOONE",
      fieldName: "所属商机交付小组",
      operator: "CONTAIN",
      text: {
        icon: projectOptions.map(() => null),
        text: projectOptions.map(opt => opt.text),
        value: projectOptions.map(opt => opt.value),
        positionStatus: projectOptions.map(() => null)
      },
      value: projectOptions.map(opt => opt.value),
      type: null,
      serialNumber: serialNumber++
    });
  }

  // 产品项目筛选
  if (filters.productProjects.length > 0) {
    const projectOptions = filters.productProjects.map(projectValue => {
      const option = filterOptions.projects.find(opt => opt.value === projectValue);
      return {
        text: option?.label || projectValue,
        value: projectValue,
        icon: null,
        positionStatus: null
      };
    });

    conditions.push({
      fieldCode: FIELD_CODES.productProject,
      fieldType: "MULTITOONE",
      fieldName: "所属产品项目交付小组",
      operator: "CONTAIN",
      text: {
        icon: projectOptions.map(() => null),
        text: projectOptions.map(opt => opt.text),
        value: projectOptions.map(opt => opt.value),
        positionStatus: projectOptions.map(() => null)
      },
      value: projectOptions.map(opt => opt.value),
      type: null,
      serialNumber: serialNumber++
    });
  }

  // 咨询任务筛选
  if (filters.consultingTasks.length > 0) {
    const taskOptions = filters.consultingTasks.map(taskValue => {
      const option = filterOptions.projects.find(opt => opt.value === taskValue);
      return {
        text: option?.label || taskValue,
        value: taskValue,
        icon: null,
        positionStatus: null
      };
    });

    conditions.push({
      fieldCode: FIELD_CODES.consultingTask,
      fieldType: "MULTITOONE",
      fieldName: "咨询任务",
      operator: "CONTAIN",
      text: {
        icon: taskOptions.map(() => null),
        text: taskOptions.map(opt => opt.text),
        value: taskOptions.map(opt => opt.value),
        positionStatus: taskOptions.map(() => null)
      },
      value: taskOptions.map(opt => opt.value),
      type: null,
      serialNumber: serialNumber++
    });
  }

  // 构建表达式（支持复杂的AND连接）
  const expression = conditions.length > 0 ? 
    conditions.map((_, index) => (index + 1).toString()).join(' AND ') : "1";

  return {
    requestType: "code",
    layoutId: "coordinate_HW4218",
    resourceType: "menu",
    resourceId: "HW929",
    standardCollectionId: "HW760",
    pageNo: pageNo,
    pageSize: pageSize,
    tempFilterBoxDataMap: null,
    showModelType: "list",
    filterBoxDataMap: {
      columnFilterObj: conditions.length > 0 ? {
        expression,
        conditions,
        isAdvanced: true
      } : undefined,
      filterObj: null,
      listViewFilterObj: null,
      searchFilterObj: null,
      viewId: "all_HW760"
    },
    needToRecordLastViewId: true,
    lastViewId: "all_HW760",
    filterBoxData: null,
    _crumb: "********************************"
  };
};

// 构建API筛选条件（保持向后兼容）
export const buildApiFilters = (
  filters: FilterState,
  filterOptions: FilterOptions
): WorkHoursFilterConditions => {
  const apiFilters: WorkHoursFilterConditions = {};
  
  // 部门筛选
  if (filters.departments.length > 0) {
    apiFilters.departments = filters.departments.map(deptValue => {
      const option = filterOptions.departments.find(opt => opt.value === deptValue);
      return { text: option?.label || deptValue, value: deptValue };
    });
  }
  
  // 员工筛选
  if (filters.employees.length > 0) {
    apiFilters.employees = filters.employees.map(empValue => {
      const option = filterOptions.employees.find(opt => opt.value === empValue);
      return { text: option?.label || empValue, value: empValue };
    });
  }
  
  // 工时类型筛选
  if (filters.workTypeValues.length > 0) {
    apiFilters.workTypes = filters.workTypeValues.map(typeValue => {
      const option = filterOptions.workTypes.find(opt => opt.value === typeValue);
      return { text: option?.label || typeValue, value: typeValue };
    });
  }
  
  // 审批状态筛选
  if (filters.approvalStatuses.length > 0) {
    apiFilters.approvalStatuses = filters.approvalStatuses.map(statusValue => {
      const option = filterOptions.approvalStatuses.find(opt => opt.value === statusValue);
      return { text: option?.label || statusValue, value: statusValue };
    });
  }
  
  // 商机项目筛选
  if (filters.opportunityProjects.length > 0) {
    apiFilters.opportunityProjects = filters.opportunityProjects.map(projectValue => {
      const option = filterOptions.projects.find(opt => opt.value === projectValue);
      return { text: option?.label || projectValue, value: projectValue };
    });
  }
  
  // 产品项目筛选
  if (filters.productProjects.length > 0) {
    apiFilters.productProjects = filters.productProjects.map(projectValue => {
      const option = filterOptions.projects.find(opt => opt.value === projectValue);
      return { text: option?.label || projectValue, value: projectValue };
    });
  }
  
  // 咨询任务筛选
  if (filters.consultingTasks.length > 0) {
    apiFilters.consultingTasks = filters.consultingTasks.map(taskValue => {
      const option = filterOptions.projects.find(opt => opt.value === taskValue);
      return { text: option?.label || taskValue, value: taskValue };
    });
  }
  
  // 日期范围筛选
  if (filters.dateRange.length === 2) {
    apiFilters.dateRange = [filters.dateRange[0], filters.dateRange[1]];
  }

  return apiFilters;
};

// 本地过滤器（用于前端筛选）
export const applyLocalFilters = (data: WorkHoursRecord[], filters: FilterState): WorkHoursRecord[] => {
  return data.filter((item) => {
    // 工时类型过滤（按显示名称）
    if (filters.workTypes.length > 0 && !filters.workTypes.includes(item.workType)) {
      return false;
    }
    
    // 项目类型过滤
    if (filters.projectTypes.length > 0 && item.projectType && !filters.projectTypes.includes(item.projectType)) {
      return false;
    }
    
    // 项目过滤
    if (filters.projects.length > 0) {
      const projectName = item.project || item.consultingTask || item.contractProject || "未分配项目(请假、出差等)";
      if (!filters.projects.includes(projectName)) {
        return false;
      }
    }
    
    return true;
  });
};

// 生成图表数据
export const generateChartData = (analysisData: AnalysisData): ChartData => {
  const departmentChartData = {
    labels: analysisData.departmentAnalysis.slice(0, 10).map(d => d.department),
    datasets: [
      {
        label: '总工时',
        data: analysisData.departmentAnalysis.slice(0, 10).map(d => d.totalHours),
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
      },
    ],
  };

  const workTypeChartData = {
    labels: analysisData.workTypeAnalysis.map(w => w.workType),
    datasets: [
      {
        data: analysisData.workTypeAnalysis.map(w => w.totalHours),
        backgroundColor: COLORS,
        borderWidth: 1,
      },
    ],
  };

  const timeChartData = {
    labels: analysisData.timeAnalysis.slice(-30).map(t => t.date),
    datasets: [
      {
        label: '每日工时',
        data: analysisData.timeAnalysis.slice(-30).map(t => t.totalHours),
        borderColor: 'rgba(255, 99, 132, 1)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        tension: 0.1,
      },
    ],
  };

  const projectChartData = {
    labels: analysisData.projectAnalysis.slice(0, 10).map(p => 
      p.project.length > 20 ? p.project.substring(0, 20) + '...' : p.project
    ),
    datasets: [
      {
        label: '总工时',
        data: analysisData.projectAnalysis.slice(0, 10).map(p => p.totalHours),
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
      },
      {
        label: '记录数',
        data: analysisData.projectAnalysis.slice(0, 10).map(p => p.recordCount),
        backgroundColor: 'rgba(255, 99, 132, 0.6)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1,
      },
      {
        label: '参与员工数',
        data: analysisData.projectAnalysis.slice(0, 10).map(p => p.employeeCount),
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
      },
    ],
  };

  return { departmentChartData, workTypeChartData, timeChartData, projectChartData };
};

// 图表配置选项
export const chartOptions = {
  responsive: true,
  plugins: {
    legend: {
      position: 'top' as const,
    },
  },
};

// 表格列定义
export const getDetailTableColumns = () => [
  {
    title: "员工",
    dataIndex: "employee",
    key: "employee",
    width: 100,
  },
  {
    title: "部门",
    dataIndex: "department",
    key: "department",
    width: 120,
  },
  {
    title: "工时类型",
    dataIndex: "workType",
    key: "workType",
    width: 100,
    render: (text: string) => React.createElement(Tag, { color: "blue" }, text),
  },
  {
    title: "工时日期",
    dataIndex: "workDate",
    key: "workDate",
    width: 120,
  },
  {
    title: "工时(小时)",
    dataIndex: "hours",
    key: "hours",
    width: 100,
    render: (hours: number) => React.createElement(Text, { strong: true }, hours.toFixed(1)),
  },
  {
    title: "工作内容",
    dataIndex: "description",
    key: "description",
    ellipsis: true,
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status",
    width: 100,
    render: (status: string) => React.createElement(Tag, { 
      color: status === "审批通过" ? "green" : "orange" 
    }, status),
  },
];

// 测试API调用参数构建
export const testApiCall = () => {
  console.log("=== 工时统计API参数构建测试 ===");
  
  // 模拟筛选条件
  const mockFilters = {
    departments: ["DEPT001"],
    employees: ["EMP001", "EMP002"],
    workTypes: [],
    workTypeValues: ["TYPE001"],
    approvalStatuses: ["STATUS001"],
    projectTypes: [],
    projects: [],
    opportunityProjects: ["PROJ001"],
    productProjects: [],
    consultingTasks: [],
    dateRange: ["2025-06-06", "2025-06-09"],
  };

  const mockFilterOptions = {
    departments: [{ label: "技术部", value: "DEPT001" }],
    employees: [
      { label: "张三", value: "EMP001" },
      { label: "李四", value: "EMP002" }
    ],
    workTypes: [{ label: "研发工时", value: "TYPE001" }],
    approvalStatuses: [{ label: "已审批", value: "STATUS001" }],
    projects: [{ label: "项目A", value: "PROJ001" }],
    opportunityProjects: [{ label: "商机项目A", value: "OPP001" }],
    productProjects: [{ label: "产品项目A", value: "PROD001" }],
    consultingTasks: [{ label: "咨询任务A", value: "CONS001" }],
  };

  // 测试新的标准列表参数构建
  const standardParams = buildStandardListParams(mockFilters, mockFilterOptions);
  console.log("标准列表API参数:", JSON.stringify(standardParams, null, 2));
  
  // 测试旧的API参数构建（向后兼容）
  const oldApiFilters = buildApiFilters(mockFilters, mockFilterOptions);
  console.log("旧版API筛选条件:", JSON.stringify(oldApiFilters, null, 2));
  
  console.log("=== 测试完成 ===");
};

// 测试标准列表参数构建（专门用于调试）
export const testStandardListParams = () => {
  console.log("=== 标准列表参数构建测试 ===");
  
  // 你提供的实际参数示例
  const exampleParams = {
    "requestType": "code",
    "layoutId": "coordinate_HW4218",
    "resourceType": "menu",
    "resourceId": "HW929",
    "standardCollectionId": "HW760",
    "pageNo": 1,
    "pageSize": 15,
    "tempFilterBoxDataMap": null,
    "showModelType": "list",
    "filterBoxDataMap": {
      "columnFilterObj": {
        "expression": "1",
        "conditions": [
          {
            "fieldCode": "HW21967",
            "fieldType": "DATE",
            "fieldName": "工时日期",
            "operator": "RANGE",
            "text": {"value": ["2025-06-06", "2025-06-09"]},
            "value": ["2025-06-06", "2025-06-09"],
            "type": null,
            "serialNumber": 1
          }
        ],
        "isAdvanced": true
      },
      "filterObj": null,
      "listViewFilterObj": null,
      "searchFilterObj": null,
      "viewId": "all_HW760"
    },
    "needToRecordLastViewId": true,
    "lastViewId": "all_HW760",
    "filterBoxData": null,
    "_crumb": "********************************"
  };
  
  console.log("实际API参数示例:", JSON.stringify(exampleParams, null, 2));
  
  // 测试我们的参数构建函数
  const testFilters = {
    departments: [],
    employees: [],
    workTypes: [],
    workTypeValues: [],
    approvalStatuses: [],
    projectTypes: [],
    projects: [],
    opportunityProjects: [],
    productProjects: [],
    consultingTasks: [],
    dateRange: ["2025-06-06", "2025-06-09"],
  };

  const testFilterOptions = {
    departments: [],
    employees: [],
    workTypes: [],
    approvalStatuses: [],
    projects: [],
    opportunityProjects: [],
    productProjects: [],
    consultingTasks: [],
  };

  const generatedParams = buildStandardListParams(testFilters, testFilterOptions);
  console.log("我们生成的参数:", JSON.stringify(generatedParams, null, 2));
  
  console.log("=== 参数对比完成 ===");
};

// 测试复杂筛选条件组合
export const testComplexFilters = () => {
  console.log("=== 复杂筛选条件测试 ===");
  
  // 模拟复杂的筛选条件
  const complexFilters: FilterState = {
    departments: ["HW133", "HW132"], // 研发一部、研发二部
    employees: ["KQM2259418"], // 张艳华
    workTypes: ["交付工时", "休假工时"],
    workTypeValues: ["HW2380", "HW2374"], // 交付工时、休假工时
    approvalStatuses: ["HW2376"], // 审批通过
    projectTypes: [],
    projects: [],
    opportunityProjects: [],
    productProjects: ["KQM2488070"], // 2024年通办信息管理系统V1.0产品项目
    consultingTasks: [],
    dateRange: ["2025-06-01", "2025-06-30"], // 整个6月
  };

  const mockFilterOptions: FilterOptions = {
    departments: [
      { label: "研发一部", value: "HW133" },
      { label: "研发二部", value: "HW132" }
    ],
    employees: [
      { label: "张艳华", value: "KQM2259418" }
    ],
    workTypes: [
      { label: "交付工时", value: "HW2380" },
      { label: "休假工时", value: "HW2374" }
    ],
    approvalStatuses: [
      { label: "审批通过", value: "HW2376" }
    ],
    projects: [
      { label: "2024年通办信息管理系统V1.0产品项目", value: "KQM2488070" }
    ],
    opportunityProjects: [],
    productProjects: [
      { label: "2024年通办信息管理系统V1.0产品项目", value: "KQM2488070" }
    ],
    consultingTasks: []
  };

  // 生成复杂筛选参数
  const complexParams = buildStandardListParams(complexFilters, mockFilterOptions, 1, 500);
  console.log("复杂筛选API参数:", JSON.stringify(complexParams, null, 2));
  
  // 验证参数结构
  const filterBoxData = complexParams.filterBoxDataMap;
  if (filterBoxData?.columnFilterObj) {
    console.log("筛选条件数量:", filterBoxData.columnFilterObj.conditions?.length);
    console.log("表达式:", filterBoxData.columnFilterObj.expression);
    console.log("是否为高级筛选:", filterBoxData.columnFilterObj.isAdvanced);
    
    // 验证每个条件
    filterBoxData.columnFilterObj.conditions?.forEach((condition, index) => {
      console.log(`条件 ${index + 1}:`, {
        字段: condition.fieldName,
        字段代码: condition.fieldCode,
        操作符: condition.operator,
        值: condition.value,
        序号: condition.serialNumber
      });
    });
  }
  
  // 测试工时类型筛选格式（按照你提供的示例）
  const workTypeOnlyFilters: FilterState = {
    departments: [],
    employees: [],
    workTypes: [],
    workTypeValues: ["HW2374"], // 只筛选休假工时
    approvalStatuses: [],
    projectTypes: [],
    projects: [],
    opportunityProjects: [],
    productProjects: [],
    consultingTasks: [],
    dateRange: [],
  };

  const workTypeParams = buildStandardListParams(workTypeOnlyFilters, mockFilterOptions, 1, 100);
  console.log("工时类型筛选参数 (对照你的示例):", JSON.stringify(workTypeParams, null, 2));
  
  console.log("=== 测试完成 ===");
}; 