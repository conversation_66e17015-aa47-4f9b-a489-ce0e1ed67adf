// 工时统计相关类型定义

// 工时记录数据类型
export interface WorkHoursRecord {
  refShangjiTeam: string | undefined;
  id: string;
  employee: string;
  department: string;
  workType: string;
  workTypeValue: string;
  workDate: string;
  hours: number;
  description: string;
  status: string;
  statusValue: string;
  project?: string;
  consultingTask?: string;
  contractProject?: string;
  projectType?: string;
}

// 统计数据类型
export interface Statistics {
  totalRecords: number;
  totalHours: number;
  uniqueEmployees: number;
  uniqueDepartments: number;
  avgHours: string;
  workingDays: number;
}

// 分析数据类型
export interface AnalysisData {
  departmentAnalysis: Array<{
    department: string;
    totalHours: number;
    recordCount: number;
    employeeCount: number;
    avgHours: string;
  }>;
  employeeAnalysis: Array<{
    employee: string;
    department: string;
    totalHours: number;
    recordCount: number;
    avgHours: string;
  }>;
  workTypeAnalysis: Array<{
    workType: string;
    totalHours: number;
    recordCount: number;
    percentage: number;
  }>;
  timeAnalysis: Array<{
    date: string;
    totalHours: number;
    recordCount: number;
  }>;
  projectAnalysis: Array<{
    project: string;
    totalHours: number;
    recordCount: number;
    employeeCount: number;
  }>;
}

// 筛选器状态接口
export interface FilterState {
  departments: string[];
  employees: string[];
  workTypes: string[];
  workTypeValues: string[];
  approvalStatuses: string[];
  projectTypes: string[];
  projects: string[];
  opportunityProjects: string[];
  productProjects: string[];
  consultingTasks: string[];
  dateRange: string[];
}

// 筛选器选项接口
export interface FilterOptions {
  departments: Array<{ label: string; value: string }>;
  employees: Array<{ label: string; value: string }>;
  workTypes: Array<{ label: string; value: string }>;
  approvalStatuses: Array<{ label: string; value: string }>;
  projects: Array<{ label: string; value: string }>;
  opportunityProjects: Array<{ label: string; value: string }>;
  productProjects: Array<{ label: string; value: string }>;
  consultingTasks: Array<{ label: string; value: string }>;
}

// 图表数据类型
export interface ChartData {
  departmentChartData: any;
  workTypeChartData: any;
  timeChartData: any;
  projectChartData: any;
}

// 表格列配置类型
export interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  ellipsis?: boolean;
  render?: (value: any, record: any) => React.ReactNode;
  sorter?: (a: any, b: any) => number;
} 