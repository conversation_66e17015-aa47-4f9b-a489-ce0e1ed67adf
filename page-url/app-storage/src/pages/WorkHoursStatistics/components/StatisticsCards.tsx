import React from 'react';
import { Row, Col, Card, Statistic } from 'antd';
import { Statistics } from '../types';

interface StatisticsCardsProps {
  statistics: Statistics;
}

const StatisticsCards: React.FC<StatisticsCardsProps> = ({ statistics }) => {
  const statisticsConfig = [
    {
      title: "工时记录数",
      value: statistics.totalRecords,
      valueStyle: { color: "#3f8600" },
    },
    {
      title: "总工时(小时)",
      value: statistics.totalHours,
      valueStyle: { color: "#cf1322" },
    },
    {
      title: "员工数",
      value: statistics.uniqueEmployees,
      valueStyle: { color: "#1890ff" },
    },
    {
      title: "部门数",
      value: statistics.uniqueDepartments,
      valueStyle: { color: "#722ed1" },
    },
    {
      title: "平均工时",
      value: statistics.avgHours,
      suffix: "小时",
      valueStyle: { color: "#fa8c16" },
    },
    {
      title: "工作天数",
      value: statistics.workingDays,
      valueStyle: { color: "#52c41a" },
    },
  ];

  return (
    <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
      {statisticsConfig.map((config, index) => (
        <Col xs={12} sm={8} md={4} key={index}>
          <Card>
            <Statistic
              title={config.title}
              value={config.value}
              suffix={config.suffix}
              valueStyle={config.valueStyle}
            />
          </Card>
        </Col>
      ))}
    </Row>
  );
};

export default StatisticsCards; 