import React from 'react';
import { Tabs, Row, Col, Card, Table, Typography, Progress } from 'antd';
import { Bar, Pie, Line } from 'react-chartjs-2';
import { AnalysisData, WorkHoursRecord, ChartData } from '../types';

const { TabPane } = Tabs;
const { Text } = Typography;

interface AnalysisChartsProps {
  analysisData: AnalysisData;
  chartData: ChartData;
  chartOptions: any;
  detailColumns: any[];
  filteredData: WorkHoursRecord[];
}

const AnalysisCharts: React.FC<AnalysisChartsProps> = ({
  analysisData,
  chartData,
  chartOptions,
  detailColumns,
  filteredData,
}) => {
  return (
    <Tabs defaultActiveKey="department">
      {/* 部门分析 */}
      <TabPane tab="部门分析" key="department">
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="部门工时分布">
              <div style={{ height: '300px' }}>
                <Bar data={chartData.departmentChartData} options={chartOptions} />
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="部门详细统计">
              <Table
                dataSource={analysisData.departmentAnalysis}
                columns={[
                  { title: "部门", dataIndex: "department", key: "department" },
                  { title: "总工时", dataIndex: "totalHours", key: "totalHours" },
                  { title: "记录数", dataIndex: "recordCount", key: "recordCount" },
                  { title: "员工数", dataIndex: "employeeCount", key: "employeeCount" },
                  { title: "平均工时", dataIndex: "avgHours", key: "avgHours" },
                ]}
                pagination={{ pageSize: 10 }}
                size="small"
              />
            </Card>
          </Col>
        </Row>
      </TabPane>

      {/* 员工分析 */}
      <TabPane tab="员工分析" key="employee">
        <Card title="员工工时排行">
          <Table
            dataSource={analysisData.employeeAnalysis}
            columns={[
              { title: "员工", dataIndex: "employee", key: "employee" },
              { title: "部门", dataIndex: "department", key: "department" },
              { 
                title: "总工时", 
                dataIndex: "totalHours", 
                key: "totalHours", 
                sorter: (a, b) => a.totalHours - b.totalHours 
              },
              { title: "记录数", dataIndex: "recordCount", key: "recordCount" },
              { title: "平均工时", dataIndex: "avgHours", key: "avgHours" },
            ]}
            pagination={{ pageSize: 15 }}
          />
        </Card>
      </TabPane>

      {/* 工时类型分析 */}
      <TabPane tab="工时类型分析" key="workType">
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="工时类型分布">
              <div style={{ height: '300px' }}>
                <Pie data={chartData.workTypeChartData} options={chartOptions} />
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="工时类型统计">
              <Table
                dataSource={analysisData.workTypeAnalysis}
                columns={[
                  { title: "工时类型", dataIndex: "workType", key: "workType" },
                  { title: "总工时", dataIndex: "totalHours", key: "totalHours" },
                  { title: "记录数", dataIndex: "recordCount", key: "recordCount" },
                  { 
                    title: "占比", 
                    dataIndex: "percentage", 
                    key: "percentage",
                    render: (percentage: number) => (
                      <div>
                        <Progress percent={percentage} size="small" />
                        <Text>{percentage}%</Text>
                      </div>
                    )
                  },
                ]}
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        </Row>
      </TabPane>

      {/* 时间趋势 */}
      <TabPane tab="时间趋势" key="time">
        <Card title="工时时间趋势">
          <div style={{ height: '400px' }}>
            <Line data={chartData.timeChartData} options={chartOptions} />
          </div>
        </Card>
      </TabPane>

      {/* 项目分析 */}
      <TabPane tab="项目分析" key="project">
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="项目工时分布">
              <div style={{ height: '300px' }}>
                <Bar 
                  data={{
                    labels: analysisData.projectAnalysis.slice(0, 10).map(p => 
                      p.project.length > 20 ? p.project.substring(0, 20) + '...' : p.project
                    ),
                    datasets: [
                      {
                        label: '总工时',
                        data: analysisData.projectAnalysis.slice(0, 10).map(p => p.totalHours),
                        backgroundColor: 'rgba(54, 162, 235, 0.6)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1,
                        yAxisID: 'y',
                      },
                    ],
                  }}
                  options={{
                    ...chartOptions,
                    scales: {
                      x: {
                        ticks: {
                          maxRotation: 45,
                          minRotation: 0,
                        },
                      },
                      y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                          display: true,
                          text: '工时 (小时)',
                        },
                      },
                    },
                  }} 
                />
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="项目参与度分析">
              <div style={{ height: '300px' }}>
                <Bar 
                  data={{
                    labels: analysisData.projectAnalysis.slice(0, 10).map(p => 
                      p.project.length > 20 ? p.project.substring(0, 20) + '...' : p.project
                    ),
                    datasets: [
                      {
                        label: '记录数',
                        data: analysisData.projectAnalysis.slice(0, 10).map(p => p.recordCount),
                        backgroundColor: 'rgba(255, 99, 132, 0.6)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1,
                        yAxisID: 'y',
                      },
                      {
                        label: '参与员工数',
                        data: analysisData.projectAnalysis.slice(0, 10).map(p => p.employeeCount),
                        backgroundColor: 'rgba(75, 192, 192, 0.6)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1,
                        yAxisID: 'y1',
                      },
                    ],
                  }}
                  options={{
                    ...chartOptions,
                    interaction: {
                      mode: 'index' as const,
                      intersect: false,
                    },
                    scales: {
                      x: {
                        ticks: {
                          maxRotation: 45,
                          minRotation: 0,
                        },
                      },
                      y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                          display: true,
                          text: '记录数',
                        },
                      },
                      y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                          display: true,
                          text: '员工数',
                        },
                        grid: {
                          drawOnChartArea: false,
                        },
                      },
                    },
                  }} 
                />
              </div>
            </Card>
          </Col>
        </Row>
        <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
          <Col xs={24}>
            <Card title="项目工时统计表">
              <Table
                dataSource={analysisData.projectAnalysis}
                columns={[
                  { title: "项目", dataIndex: "project", key: "project", ellipsis: true },
                  { 
                    title: "总工时", 
                    dataIndex: "totalHours", 
                    key: "totalHours", 
                    sorter: (a, b) => a.totalHours - b.totalHours,
                    render: (value: number) => `${value} 小时`
                  },
                  { 
                    title: "记录数", 
                    dataIndex: "recordCount", 
                    key: "recordCount",
                    sorter: (a, b) => a.recordCount - b.recordCount
                  },
                  { 
                    title: "参与员工数", 
                    dataIndex: "employeeCount", 
                    key: "employeeCount",
                    sorter: (a, b) => a.employeeCount - b.employeeCount
                  },
                  {
                    title: "平均工时/人",
                    key: "avgHoursPerEmployee",
                    render: (_, record) => `${(record.totalHours / record.employeeCount).toFixed(1)} 小时`,
                    sorter: (a, b) => (a.totalHours / a.employeeCount) - (b.totalHours / b.employeeCount)
                  },
                ]}
                pagination={{ pageSize: 15, showSizeChanger: true }}
                scroll={{ x: 800 }}
              />
            </Card>
          </Col>
        </Row>
      </TabPane>

      {/* 详细数据 */}
      <TabPane tab="详细数据" key="detail">
        <Card title="工时详细记录">
          <Table
            dataSource={filteredData}
            columns={detailColumns}
            pagination={{ pageSize: 20, showSizeChanger: true }}
            scroll={{ x: 1000 }}
          />
        </Card>
      </TabPane>
    </Tabs>
  );
};

export default AnalysisCharts; 