import React from "react";
import {
  Card,
  Row,
  Col,
  Select,
  DatePicker,
  Button,
  Space,
  Typography,
  Alert,
} from "antd";
import { FilterState, FilterOptions } from "../types";
import "moment/locale/zh-cn";
import locale from "antd/es/date-picker/locale/zh_CN";
const { Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface FilterPanelProps {
  filters: FilterState;
  filterOptions: FilterOptions;
  optionsLoading: boolean;
  loading: boolean;
  hasData: boolean;
  onFiltersChange: (filters: FilterState) => void;
  onApplyFilters: () => void;
  onResetFilters: () => void;
  onRefreshData: () => void;
}

// 单个筛选器组件
interface FilterItemProps {
  label: string;
  value: string[];
  options: Array<{ label: string; value: string }>;
  placeholder: string;
  loading?: boolean;
  onChange: (value: string[]) => void;
  showSearch?: boolean;
}

const FilterItem: React.FC<FilterItemProps> = ({
  label,
  value,
  options,
  placeholder,
  loading = false,
  onChange,
  showSearch = false,
}) => (
  <Col xs={24} sm={12} md={8} lg={6}>
    <div style={{ marginBottom: "8px" }}>
      <Text strong>{label}：</Text>
    </div>
    <Select
      mode="multiple"
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      style={{ width: "100%" }}
      maxTagCount="responsive"
      allowClear
      showSearch={showSearch}
      loading={loading}
      options={options}
      filterOption={(input, option) =>
        (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
      }
    />
  </Col>
);

// 项目类型筛选器组件
interface ProjectTypeFilterProps {
  value: string[];
  onChange: (value: string[]) => void;
}

const ProjectTypeFilter: React.FC<ProjectTypeFilterProps> = ({
  value,
  onChange,
}) => (
  <Col xs={24} sm={12} md={8} lg={6}>
    <div style={{ marginBottom: "8px" }}>
      <Text strong>项目类型：</Text>
    </div>
    <Select
      mode="multiple"
      placeholder="选择项目类型"
      value={value}
      onChange={onChange}
      style={{ width: "100%" }}
      maxTagCount="responsive"
      allowClear
    >
      <Option value="产品项目">产品项目</Option>
      <Option value="咨询任务">咨询任务</Option>
      <Option value="合同项目">合同项目</Option>
    </Select>
  </Col>
);

// 日期范围筛选器组件
interface DateRangeFilterProps {
  value: string[];
  onChange: (dateRange: string[]) => void;
}

const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  value,
  onChange,
}) => (
  <Col xs={24} sm={12} md={8} lg={6}>
    <div style={{ marginBottom: "8px" }}>
      <Text strong>工时日期：</Text>
    </div>
    <RangePicker
      locale={locale}
      placeholder={["开始日期", "结束日期"]}
      onChange={(dates) => {
        if (dates && dates.length === 2 && dates[0] && dates[1]) {
          onChange([
            dates[0]!.format("YYYY-MM-DD"),
            dates[1]!.format("YYYY-MM-DD"),
          ]);
        } else {
          onChange([]);
        }
      }}
      style={{ width: "100%" }}
      format="YYYY-MM-DD"
      allowClear
    />
  </Col>
);

const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  filterOptions,
  optionsLoading,
  loading,
  hasData,
  onFiltersChange,
  onApplyFilters,
  onResetFilters,
  onRefreshData,
}) => {
  const updateFilter = (key: keyof FilterState, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  return (
    <Card title="🔍 数据筛选器（点击查询生成报告）" style={{ marginBottom: "24px" }}>
      {/* 操作按钮 */}
      <div style={{ marginBottom: "16px" }}>
        <Space>
          <Button type="primary" onClick={onApplyFilters} loading={loading}>
            查询数据
          </Button>
          <Button onClick={onResetFilters}>重置</Button>
        </Space>
      </div>

      {/* 筛选器网格 */}
      <Row gutter={[16, 16]}>
        {/* 服务端筛选器 */}
        <FilterItem
          label="部门"
          value={filters.departments}
          options={filterOptions.departments}
          placeholder="选择部门"
          showSearch={true}
          onChange={(value) => updateFilter("departments", value)}
        />

        <FilterItem
          label="员工"
          value={filters.employees}
          options={filterOptions.employees}
          placeholder="选择员工"
          showSearch={true}
          onChange={(value) => updateFilter("employees", value)}
        />

        <FilterItem
          label="工时类型"
          value={filters.workTypeValues}
          options={filterOptions.workTypes}
          placeholder="选择工时类型"
          loading={optionsLoading}
          showSearch={true}
          onChange={(value) => updateFilter("workTypeValues", value)}
        />

        <FilterItem
          label="审批状态"
          value={filters.approvalStatuses}
          options={filterOptions.approvalStatuses}
          placeholder="选择审批状态"
          loading={optionsLoading}
          onChange={(value) => updateFilter("approvalStatuses", value)}
        />

        {/* <ProjectTypeFilter
          value={filters.projectTypes}
          onChange={(value) => updateFilter("projectTypes", value)}
        /> */}

        <FilterItem
          label="商机项目交付小组"
          value={filters.opportunityProjects}
          options={filterOptions.opportunityProjects}
          placeholder="选择商机项目"
          loading={optionsLoading}
          showSearch={true}
          onChange={(value) => updateFilter("opportunityProjects", value)}
        />

        <FilterItem
          label="产品项目交付小组"
          value={filters.productProjects}
          options={filterOptions.productProjects}
          placeholder="选择产品项目"
          showSearch={true}
          loading={optionsLoading}
          onChange={(value) => updateFilter("productProjects", value)}
        />

        <FilterItem
          label="咨询任务"
          value={filters.consultingTasks}
          options={filterOptions.consultingTasks}
          placeholder="选择咨询任务"
          loading={optionsLoading}
          showSearch={true}
          onChange={(value) => updateFilter("consultingTasks", value)}
        />

        <DateRangeFilter
          value={filters.dateRange}
          onChange={(value) => updateFilter("dateRange", value)}
        />
      </Row>

      {/* 筛选说明 */}
      <Alert
        message="📋 筛选说明"
        description={
          <div>
            <p>
              <strong>服务端筛选（推荐）：</strong>
            </p>
            <ul>
              <li> <strong>筛选说明：</strong>只能查到自己权限看到的数据</li>
              <li>
                <strong>部门/员工：</strong>直接从服务端筛选，性能更好
              </li>
              <li>
                <strong>工时类型：</strong>
                使用预定义的标准工时类型进行服务端筛选
              </li>
              <li>
                <strong>审批状态：</strong>服务端筛选审批状态，支持多选
              </li>
              <li>
                <strong>商机项目/产品项目/咨询任务：</strong>
                按具体项目类型进行服务端筛选
              </li>
              <li>
                <strong>工时日期：</strong>按日期范围进行服务端筛选
              </li>
            </ul>
          </div>
        }
        type="info"
        showIcon
        style={{ marginTop: "16px" }}
      />
    </Card>
  );
};

export default FilterPanel;
