### 工时统计分析
```bash
#这是获取基本工时数据的接口
curl 'https://pms.tongbaninfo.com:8888/app/api/standardList/front/getListData' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -b '_currency=null; cp-hub-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjJmNWNmZDk1LTY1ZTUtMTFlYi1iM2M3LTAyMjZjYTViMThhYSIsInVzZXJuYW1lIjoiYWRtaW4iLCJlbWFpbCI6IiIsImV4cCI6NjE3NDcxMjg1NjcsImlhdCI6MTc0NzEyODYyN30.LK4YFgg89pjJ4FLdUc-W2LLUBS98SUlTo3ksm7fAY2k; envList=[{%22userTag%22:%22beta%22%2C%22feTag%22:%22beta%22%2C%22id%22:%22mjxklah2r%22}]; _timezone=UTC%2B08%3A00; tokenId=71b9f52a62cebd425e57079721262f71; _lang=zh_CN; tongbaninfo.com_watch_currentTime=2025-06-09T14:44:05+08:00' \
  -H 'Origin: https://pms.tongbaninfo.com:8888' \
  -H 'Referer: https://pms.tongbaninfo.com:8888/app' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36' \
  -H '_lang: zh_CN' \
  -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sw6: 1-MS44ODQ2MTY2NzEuMTc0OTQ1MTQ0Nzk3NjAwMDE=-MS44ODQ2MTY2NzEuMTc0OTQ1MTQ0Nzk3NjAwMDE=-0-1-1-IzEyNy4wLjAuMTo4MA==-LTE=-LTE=' \
  --data-raw '{"requestType":"code","layoutId":"coordinate_HW4218","resourceType":"menu","resourceId":"HW929","standardCollectionId":"HW760","pageNo":1,"pageSize":100,"tempFilterBoxDataMap":null,"showModelType":"list","filterBoxDataMap":{"columnFilterObj":null,"filterObj":null,"listViewFilterObj":null,"searchFilterObj":null,"viewId":"all_HW760","needToRecordLastViewId":true,"lastViewId":"all_HW760","filterBoxData":null,"_crumb":"********************************"}'
```

```json
// 这个是接口返回值的示例   HW23543工时类型  KQM1909是部门  KQM19036填写人员  HW23575 审批状态  KQM18553所属产品项目  KQM20885所属咨询任务 KQM18553所属商机项目 KQM19356所属合同项目
//  KQM18553所属产品项目  KQM20885所属咨询任务 KQM18553所属商机项目 KQM19356所属合同项目 这四个只有一个值存在，其他都是null
// HW21967工时日期  KQM17303工时小时
{
    "code": 200,
    "requestTime": null,
    "appName": null,
    "msg": "服务器成功返回请求的数据",
    "subCode": null,
    "subMsg": null,
    "subMsgType": "success",
    "data": {
        "body": [
            {
                "id": "KQM3135231",
                "data": {
                    "HW21969": {
                        "text": "咨询任务工时提测，冒烟测试，流程联调",
                        "value": "咨询任务工时提测，冒烟测试，流程联调"
                    },
                    "HW21967": {
                        "text": "2025-06-06",
                        "value": "2025-06-06 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3135231",
                        "value": "KQM3135231"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-06-07 10:28:47",
                        "value": "2025-06-07 10:28:47"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "KQM18553": {
                        "text": "2024年通办信息管理系统V1.0产品项目",
                        "value": "KQM2488070"
                    },
                    "HW22789": {
                        "text": "张艳华-工时122597",
                        "value": "张艳华-工时122597"
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "交付工时",
                        "value": "HW2380",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2380": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-06-07 10:25:16",
                        "value": "2025-06-07 10:25:16"
                    },
                    "KQM18969": {
                        "text": null,
                        "value": null
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "工作日",
                        "value": "KQM918",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM918": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时122597",
                        "value": "张艳华-工时122597"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3135231"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3130404",
                "data": {
                    "HW21969": {
                        "text": "咨询任务基本逻辑处理，包含多条工时校验，多个任务选择，每日总工时校验",
                        "value": "咨询任务基本逻辑处理，包含多条工时校验，多个任务选择，每日总工时校验"
                    },
                    "HW21967": {
                        "text": "2025-06-04",
                        "value": "2025-06-04 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3130404",
                        "value": "KQM3130404"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-06-05 20:41:58",
                        "value": "2025-06-05 20:41:58"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "KQM18553": {
                        "text": "2024年通办信息管理系统V1.0产品项目",
                        "value": "KQM2488070"
                    },
                    "HW22789": {
                        "text": "张艳华-工时121559",
                        "value": "张艳华-工时121559"
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "交付工时",
                        "value": "HW2380",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2380": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-06-05 19:40:07",
                        "value": "2025-06-05 19:40:07"
                    },
                    "KQM18969": {
                        "text": null,
                        "value": null
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "工作日",
                        "value": "KQM918",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM918": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时121559",
                        "value": "张艳华-工时121559"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3130404"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3130403",
                "data": {
                    "HW21969": {
                        "text": "咨询任务工时接口联调，相关逻辑处理",
                        "value": "咨询任务工时接口联调，相关逻辑处理"
                    },
                    "HW21967": {
                        "text": "2025-06-05",
                        "value": "2025-06-05 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3130403",
                        "value": "KQM3130403"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-06-05 20:41:58",
                        "value": "2025-06-05 20:41:58"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "KQM18553": {
                        "text": "2024年通办信息管理系统V1.0产品项目",
                        "value": "KQM2488070"
                    },
                    "HW22789": {
                        "text": "张艳华-工时121558",
                        "value": "张艳华-工时121558"
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "交付工时",
                        "value": "HW2380",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2380": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-06-05 19:39:28",
                        "value": "2025-06-05 19:39:28"
                    },
                    "KQM18969": {
                        "text": null,
                        "value": null
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "工作日",
                        "value": "KQM918",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM918": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时121558",
                        "value": "张艳华-工时121558"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3130403"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3108700",
                "data": {
                    "HW21969": {
                        "text": "回老家因高速暴雨，不能及时返沪，故请假一天",
                        "value": "回老家因高速暴雨，不能及时返沪，故请假一天"
                    },
                    "HW21967": {
                        "text": "2025-06-03",
                        "value": "2025-06-03 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3108700",
                        "value": "KQM3108700"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-06-03 10:29:13",
                        "value": "2025-06-03 10:29:13"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "HW22789": {
                        "text": "张艳华-工时120054",
                        "value": "张艳华-工时120054"
                    },
                    "KQM18553": {
                        "text": null,
                        "value": null
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "休假工时",
                        "value": "HW2374",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2374": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-06-03 10:29:13",
                        "value": "2025-06-03 10:29:13"
                    },
                    "KQM18969": {
                        "text": "KQM3107573",
                        "value": "KQM3107573"
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "请假",
                        "value": "KQM919",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM919": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时120054",
                        "value": "张艳华-工时120054"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3108700"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3103234",
                "data": {
                    "HW21969": {
                        "text": "生产异常数据监控，处理\n低代码统计功能研究",
                        "value": "生产异常数据监控，处理\n低代码统计功能研究"
                    },
                    "HW21967": {
                        "text": "2025-05-30",
                        "value": "2025-05-30 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3103234",
                        "value": "KQM3103234"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-05-31 15:29:54",
                        "value": "2025-05-31 15:29:54"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "KQM18553": {
                        "text": "2024年通办信息管理系统V1.0产品项目",
                        "value": "KQM2488070"
                    },
                    "HW22789": {
                        "text": "张艳华-工时119306",
                        "value": "张艳华-工时119306"
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "交付工时",
                        "value": "HW2380",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2380": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-05-30 18:18:04",
                        "value": "2025-05-30 18:18:04"
                    },
                    "KQM18969": {
                        "text": null,
                        "value": null
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "工作日",
                        "value": "KQM918",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM918": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时119306",
                        "value": "张艳华-工时119306"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3103234"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3090435",
                "data": {
                    "HW21969": {
                        "text": "上线准备\n新建商机项目小组流程回归\n生产环境审批流切换",
                        "value": "上线准备\n新建商机项目小组流程回归\n生产环境审批流切换"
                    },
                    "HW21967": {
                        "text": "2025-05-29",
                        "value": "2025-05-29 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3090435",
                        "value": "KQM3090435"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-05-31 15:29:54",
                        "value": "2025-05-31 15:29:54"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "KQM18553": {
                        "text": "2024年通办信息管理系统V1.0产品项目",
                        "value": "KQM2488070"
                    },
                    "HW22789": {
                        "text": "张艳华-工时119302",
                        "value": "张艳华-工时119302"
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "交付工时",
                        "value": "HW2380",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2380": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-05-30 18:17:14",
                        "value": "2025-05-30 18:17:14"
                    },
                    "KQM18969": {
                        "text": null,
                        "value": null
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "工作日",
                        "value": "KQM918",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM918": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时119302",
                        "value": "张艳华-工时119302"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3090435"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3094881",
                "data": {
                    "HW21969": {
                        "text": "处理自定义iframe 用户信息获取，自定义单位实施成本基本页面开发",
                        "value": "处理自定义iframe 用户信息获取，自定义单位实施成本基本页面开发"
                    },
                    "HW21967": {
                        "text": "2025-05-27",
                        "value": "2025-05-27 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3094881",
                        "value": "KQM3094881"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-05-30 09:38:11",
                        "value": "2025-05-30 09:38:11"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "KQM18553": {
                        "text": "2024年通办信息管理系统V1.0产品项目",
                        "value": "KQM2488070"
                    },
                    "HW22789": {
                        "text": "张艳华-工时118102",
                        "value": "张艳华-工时118102"
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "交付工时",
                        "value": "HW2380",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2380": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-05-29 10:36:00",
                        "value": "2025-05-29 10:36:00"
                    },
                    "KQM18969": {
                        "text": null,
                        "value": null
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "工作日",
                        "value": "KQM918",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM918": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时118102",
                        "value": "张艳华-工时118102"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3094881"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3082487",
                "data": {
                    "HW21969": {
                        "text": "处理商机项目小组 立项审批审批流，智能总监审核调整自定义输入部门成本",
                        "value": "处理商机项目小组 立项审批审批流，智能总监审核调整自定义输入部门成本"
                    },
                    "HW21967": {
                        "text": "2025-05-28",
                        "value": "2025-05-28 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3082487",
                        "value": "KQM3082487"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": null,
                        "value": null
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "KQM18553": {
                        "text": "2024年通办信息管理系统V1.0产品项目",
                        "value": "KQM2488070"
                    },
                    "HW22789": {
                        "text": "张艳华-工时118097",
                        "value": "张艳华-工时118097"
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "交付工时",
                        "value": "HW2380",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2380": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-05-29 10:33:24",
                        "value": "2025-05-29 10:33:24"
                    },
                    "KQM18969": {
                        "text": null,
                        "value": null
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "工作日",
                        "value": "KQM918",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM918": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时118097",
                        "value": "张艳华-工时118097"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3082487"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3067665",
                "data": {
                    "HW21969": {
                        "text": "商机项目小组相关审批流程了解，特殊审批流程处理，相关代码处理",
                        "value": "商机项目小组相关审批流程了解，特殊审批流程处理，相关代码处理"
                    },
                    "HW21967": {
                        "text": "2025-05-23",
                        "value": "2025-05-23 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3067665",
                        "value": "KQM3067665"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-05-27 20:44:23",
                        "value": "2025-05-27 20:44:23"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "KQM18553": {
                        "text": "2024年通办信息管理系统V1.0产品项目",
                        "value": "KQM2488070"
                    },
                    "HW22789": {
                        "text": "张艳华-工时116579",
                        "value": "张艳华-工时116579"
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "交付工时",
                        "value": "HW2380",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2380": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-05-26 18:06:19",
                        "value": "2025-05-26 18:06:19"
                    },
                    "KQM18969": {
                        "text": null,
                        "value": null
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "工作日",
                        "value": "KQM918",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM918": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时116579",
                        "value": "张艳华-工时116579"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3067665"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3067639",
                "data": {
                    "HW21969": {
                        "text": "异常加班数据处理\n异常工时处理\n自定义预计实施成本填写功能开发",
                        "value": "异常加班数据处理\n异常工时处理\n自定义预计实施成本填写功能开发"
                    },
                    "HW21967": {
                        "text": "2025-05-26",
                        "value": "2025-05-26 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3067639",
                        "value": "KQM3067639"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-05-27 20:44:24",
                        "value": "2025-05-27 20:44:24"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "KQM18553": {
                        "text": "2024年通办信息管理系统V1.0产品项目",
                        "value": "KQM2488070"
                    },
                    "HW22789": {
                        "text": "张艳华-工时116573",
                        "value": "张艳华-工时116573"
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "交付工时",
                        "value": "HW2380",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2380": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-05-26 18:05:23",
                        "value": "2025-05-26 18:05:23"
                    },
                    "KQM18969": {
                        "text": null,
                        "value": null
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "工作日",
                        "value": "KQM918",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM918": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时116573",
                        "value": "张艳华-工时116573"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3067639"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3070712",
                "data": {
                    "HW21969": {
                        "text": "上线准备，上线权限配制，相关功能开发，生产错误监控处理",
                        "value": "上线准备，上线权限配制，相关功能开发，生产错误监控处理"
                    },
                    "HW21967": {
                        "text": "2025-05-21",
                        "value": "2025-05-21 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3070712",
                        "value": "KQM3070712"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-05-24 14:27:28",
                        "value": "2025-05-24 14:27:28"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "KQM18553": {
                        "text": "2024年通办信息管理系统V1.0产品项目",
                        "value": "KQM2488070"
                    },
                    "HW22789": {
                        "text": "张艳华-工时114801",
                        "value": "张艳华-工时114801"
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "交付工时",
                        "value": "HW2380",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2380": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-05-22 18:31:54",
                        "value": "2025-05-22 18:31:54"
                    },
                    "KQM18969": {
                        "text": null,
                        "value": null
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "工作日",
                        "value": "KQM918",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM918": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时114801",
                        "value": "张艳华-工时114801"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3070712"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3070711",
                "data": {
                    "HW21969": {
                        "text": "上线准备，上线权限配制，暂存功能开发",
                        "value": "上线准备，上线权限配制，暂存功能开发"
                    },
                    "HW21967": {
                        "text": "2025-05-22",
                        "value": "2025-05-22 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3070711",
                        "value": "KQM3070711"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-05-24 14:27:28",
                        "value": "2025-05-24 14:27:28"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "KQM18553": {
                        "text": "2024年通办信息管理系统V1.0产品项目",
                        "value": "KQM2488070"
                    },
                    "HW22789": {
                        "text": "张艳华-工时114800",
                        "value": "张艳华-工时114800"
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "交付工时",
                        "value": "HW2380",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2380": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-05-22 18:31:20",
                        "value": "2025-05-22 18:31:20"
                    },
                    "KQM18969": {
                        "text": null,
                        "value": null
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "工作日",
                        "value": "KQM918",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM918": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时114800",
                        "value": "张艳华-工时114800"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3070711"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3062616",
                "data": {
                    "HW21969": {
                        "text": "商机项目编辑，新建测试联调，生产工时问题监控工具接口开发",
                        "value": "商机项目编辑，新建测试联调，生产工时问题监控工具接口开发"
                    },
                    "HW21967": {
                        "text": "2025-05-15",
                        "value": "2025-05-15 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3062616",
                        "value": "KQM3062616"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-05-16 13:04:50",
                        "value": "2025-05-16 13:04:50"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "KQM18553": {
                        "text": "2024年通办信息管理系统V1.0产品项目",
                        "value": "KQM2488070"
                    },
                    "HW22789": {
                        "text": "张艳华-工时111347",
                        "value": "张艳华-工时111347"
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "交付工时",
                        "value": "HW2380",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2380": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-05-15 18:20:59",
                        "value": "2025-05-15 18:20:59"
                    },
                    "KQM18969": {
                        "text": null,
                        "value": null
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "工作日",
                        "value": "KQM918",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM918": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时111347",
                        "value": "张艳华-工时111347"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3062616"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3049156",
                "data": {
                    "HW21969": {
                        "text": "回老家一趟",
                        "value": "回老家一趟"
                    },
                    "HW21967": {
                        "text": "2025-05-20",
                        "value": "2025-05-20 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3049156",
                        "value": "KQM3049156"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-05-15 14:28:56",
                        "value": "2025-05-15 14:28:56"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "HW22789": {
                        "text": "张艳华-工时111127",
                        "value": "张艳华-工时111127"
                    },
                    "KQM18553": {
                        "text": null,
                        "value": null
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "休假工时",
                        "value": "HW2374",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2374": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-05-15 14:28:56",
                        "value": "2025-05-15 14:28:56"
                    },
                    "KQM18969": {
                        "text": "KQM3047837",
                        "value": "KQM3047837"
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "请假",
                        "value": "KQM919",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM919": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时111127",
                        "value": "张艳华-工时111127"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3049156"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3049155",
                "data": {
                    "HW21969": {
                        "text": "回老家一趟",
                        "value": "回老家一趟"
                    },
                    "HW21967": {
                        "text": "2025-05-19",
                        "value": "2025-05-19 00:00:00"
                    },
                    "HW22759": {
                        "text": "KQM3049155",
                        "value": "KQM3049155"
                    },
                    "KQM19098": {
                        "text": "研发一部",
                        "value": "KQM2259056"
                    },
                    "KQM17301": {
                        "text": "2025-05-15 14:28:56",
                        "value": "2025-05-15 14:28:56"
                    },
                    "HW22788": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "HW22789": {
                        "text": "张艳华-工时111126",
                        "value": "张艳华-工时111126"
                    },
                    "KQM18553": {
                        "text": null,
                        "value": null
                    },
                    "KQM19036": {
                        "text": "张艳华",
                        "value": "KQM2259418"
                    },
                    "KQM17348": {
                        "text": null,
                        "value": null
                    },
                    "HW23545": {
                        "text": null,
                        "value": null
                    },
                    "KQM18655": {
                        "text": null,
                        "value": null
                    },
                    "HW23543": {
                        "text": "休假工时",
                        "value": "HW2374",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2374": {}
                            }
                        }
                    },
                    "KQM17303": {
                        "text": 8.0,
                        "value": 8.0
                    },
                    "HW22760": {
                        "text": "2025-05-15 14:28:56",
                        "value": "2025-05-15 14:28:56"
                    },
                    "KQM18969": {
                        "text": "KQM3047837",
                        "value": "KQM3047837"
                    },
                    "HW23575": {
                        "text": "审批通过",
                        "value": "HW2376",
                        "dataConfigMap": {
                            "masterStyle": {
                                "HW2376": {}
                            }
                        }
                    },
                    "KQM17603": {
                        "text": "张艳华",
                        "value": "KQM924"
                    },
                    "HW23572": {
                        "text": null,
                        "value": null
                    },
                    "KQM18923": {
                        "text": "请假",
                        "value": "KQM919",
                        "dataConfigMap": {
                            "masterStyle": {
                                "KQM919": {}
                            }
                        }
                    },
                    "HW760:HW22789": {
                        "text": "张艳华-工时111126",
                        "value": "张艳华-工时111126"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3049155"
                    }
                },
                "children": null
            }
        ],
        "pagination": {
            "current": 1,
            "pageSize": 15,
            "total": 122,
            "pages": 9,
            "hasMorePage": false,
            "dimTotal": "122"
        },
        "viewId": null,
        "viewName": null,
        "filterBoxData": null,
        "columnFilterBoxData": null,
        "buttonMap": null,
        "listShowModel": "list"
    },
    "tid": null,
    "originalTid": null
}
```

```bash
#这是获取部门的
curl 'https://pms.tongbaninfo.com:8888/app/api/standardList/front/getSimpleSearchListDataBySetField' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -b '_currency=null; cp-hub-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjJmNWNmZDk1LTY1ZTUtMTFlYi1iM2M3LTAyMjZjYTViMThhYSIsInVzZXJuYW1lIjoiYWRtaW4iLCJlbWFpbCI6IiIsImV4cCI6NjE3NDcxMjg1NjcsImlhdCI6MTc0NzEyODYyN30.LK4YFgg89pjJ4FLdUc-W2LLUBS98SUlTo3ksm7fAY2k; envList=[{%22userTag%22:%22beta%22%2C%22feTag%22:%22beta%22%2C%22id%22:%22mjxklah2r%22}]; _timezone=UTC%2B08%3A00; tokenId=bd8b798250bbd574af7c2a85371b0641; _lang=zh_CN; tongbaninfo.com_watch_currentTime=2025-06-09T15:20:42+08:00' \
  -H 'Origin: https://pms.tongbaninfo.com:8888' \
  -H 'Referer: https://pms.tongbaninfo.com:8888/app' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36' \
  -H '_lang: zh_CN' \
  -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sw6: 1-MS44ODQ2MTY3MTUuMTc0OTQ1MzY0MjU3NTAwMDE=-MS44ODQ2MTY3MTUuMTc0OTQ1MzY0MjU3NTAwMDE=-0-1-1-IzEyNy4wLjAuMTo4MA==-LTE=-LTE=' \
  --data-raw '{"keyword":"","pageNo":1,"pageSize":50,"standardCollectionId":"KQM1700","parentMetadataId":"HW760","resourceType":"standard","requestType":"code","queryFieldId":"KQM19098","deviceType":"web","parentLayoutId":"coordinate_HW4218","_crumb":"********************************"}'
```
```json
// 这是部门的返回值
{
    "code": 200,
    "requestTime": null,
    "appName": null,
    "msg": "服务器成功返回请求的数据",
    "subCode": null,
    "subMsg": null,
    "subMsgType": "success",
    "data": {
        "openTableHeader": null,
        "nameFieldInfo": {
            "KQM2296066": {
                "text": "北京交付组",
                "value": "KQM458"
            },
            "KQM2296067": {
                "text": "国办交付组",
                "value": "KQM459"
            },
            "KQM2296068": {
                "text": "JD交付组",
                "value": "KQM460"
            },
            "KQM2253731": {
                "text": "产品创新部",
                "value": "HW163"
            },
            "KQM2309222": {
                "text": "人事行政组",
                "value": "KQM648"
            },
            "KQM2309221": {
                "text": "一码通测试组",
                "value": "KQM645"
            },
            "KQM2253737": {
                "text": "总经办",
                "value": "HW35"
            },
            "KQM2253735": {
                "text": "研发三部",
                "value": "KQM452"
            },
            "KQM2253736": {
                "text": "质量保障部",
                "value": "KQM453"
            },
            "KQM2303004": {
                "text": "前端研发一组",
                "value": "KQM486"
            },
            "KQM2303007": {
                "text": "前端研发二组",
                "value": "KQM512"
            },
            "KQM2970878": {
                "text": "总监助理",
                "value": "KQM734"
            },
            "KQM2822117": {
                "text": "上海营销组",
                "value": "KQM547"
            },
            "KQM2303009": {
                "text": "前端研发三组",
                "value": "KQM514"
            },
            "KQM2309193": {
                "text": "后端研发二组",
                "value": "KQM613"
            },
            "KQM2309192": {
                "text": "后端研发一组",
                "value": "KQM520"
            },
            "KQM2309197": {
                "text": "智办产品测试组",
                "value": "KQM454"
            },
            "KQM2304061": {
                "text": "东北营销组",
                "value": "KQM660"
            },
            "KQM2963701": {
                "text": "营销咨询中心",
                "value": "KQM541"
            },
            "KQM2309195": {
                "text": "后端研发三组",
                "value": "KQM619"
            },
            "KQM2299163": {
                "text": "智办产品创新组",
                "value": "KQM516"
            },
            "KQM2970665": {
                "text": "运维实施组",
                "value": "KQM731"
            },
            "KQM2967656": {
                "text": "改进发展组",
                "value": "KQM545"
            },
            "KQM2302971": {
                "text": "后端研发三组",
                "value": "KQM642"
            },
            "KQM2306618": {
                "text": "研发二组",
                "value": "KQM511"
            },
            "KQM2298180": {
                "text": "西南营销组",
                "value": "KQM659"
            },
            "KQM2975638": {
                "text": "架构师",
                "value": "KQM733"
            },
            "KQM2975637": {
                "text": "运维实施组",
                "value": "KQM732"
            },
            "KQM2951501": {
                "text": "改进发展部",
                "value": "KQM544"
            },
            "KQM2259055": {
                "text": "研发二部",
                "value": "HW132"
            },
            "KQM2259057": {
                "text": "交付实施二部",
                "value": "HW134"
            },
            "KQM2259056": {
                "text": "研发一部",
                "value": "HW133"
            },
            "KQM2304063": {
                "text": "东南营销组",
                "value": "KQM582"
            },
            "KQM2298179": {
                "text": "华北营销组",
                "value": "KQM658"
            },
            "KQM2304062": {
                "text": "西北营销组",
                "value": "KQM581"
            },
            "KQM2304065": {
                "text": "湖北营销组",
                "value": "KQM584"
            },
            "KQM2495738": {
                "text": "产品一组(调试)",
                "value": "KQM678"
            },
            "KQM2304064": {
                "text": "华南营销组",
                "value": "KQM583"
            },
            "KQM2309199": {
                "text": "空间产品测试组",
                "value": "KQM643"
            },
            "KQM2495732": {
                "text": "销售一组(调试)",
                "value": "KQM676"
            },
            "KQM2304067": {
                "text": "上海一组",
                "value": "KQM586"
            },
            "KQM2304068": {
                "text": "南方组",
                "value": "KQM587"
            },
            "KQM2975604": {
                "text": "IMS系统研发组",
                "value": "KQM550"
            },
            "KQM2301568": {
                "text": "后端研发一组",
                "value": "KQM618"
            },
            "KQM2301567": {
                "text": "前端研发二组",
                "value": "KQM617"
            },
            "KQM2301564": {
                "text": "码组",
                "value": "KQM612"
            },
            "KQM2301563": {
                "text": "研发三组",
                "value": "KQM515"
            },
            "KQM2301562": {
                "text": "研发四组",
                "value": "KQM513"
            },
            "KQM2304070": {
                "text": "招投标组",
                "value": "KQM589"
            },
            "KQM2967713": {
                "text": "交付实施一部",
                "value": "KQM548"
            }
        },
        "current": 1,
        "total": 103,
        "queryFieldMap": [
            {
                "displayLabel": "部门名称",
                "id": "KQM18246"
            }
        ],
        "pages": 3,
        "listhead": [
            {
                "fieldId": null,
                "fieldName": null,
                "type": null,
                "formuleType": null,
                "fieldCode": null,
                "readOnly": null,
                "updateable": null,
                "required": null,
                "objId": null,
                "objName": null,
                "example": null,
                "columnType": null,
                "masterDataStyle": null,
                "masterDataShowType": null,
                "dataSource": null,
                "dataSourceType": null,
                "cascadingStandardMetaId": null,
                "cascadingStandardFieldId": null,
                "cascadingStandardMetaName": null,
                "cascadingStandardFieldName": null,
                "defaultInternationalAddressData": null,
                "apiName": "name",
                "parentObj": false,
                "parentObjName": null,
                "parentObjId": null,
                "storageMode": null,
                "child": {
                    "compType": "MULTITOONE",
                    "sendRequest": {
                        "layoutUpdate": {
                            "enable": false
                        },
                        "formulaFieldsCalc": {
                            "reloadRelatedList": [],
                            "enable": false
                        },
                        "externalFieldsCalc": {
                            "reloadRelatedList": [],
                            "enable": false
                        },
                        "relyFieldsCalc": {
                            "enable": false
                        },
                        "componentsUpdate": {
                            "enable": false
                        }
                    },
                    "cascading": false,
                    "i18nAddressMap": {},
                    "addressType": "RADIO",
                    "addressSelectLevel": 4,
                    "customProps": null,
                    "collectType": null,
                    "addressVer": null,
                    "realType": null,
                    "dataStyle": null
                },
                "alonePossess": null,
                "treeFilterable": null,
                "asynchronousTreeData": null,
                "fieldDefaultValue": null
            }
        ],
        "data": [
            {
                "id": "KQM2970665",
                "data": {
                    "KQM18246": {
                        "text": "运维实施组",
                        "value": "KQM731"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2975637",
                "data": {
                    "KQM18246": {
                        "text": "运维实施组",
                        "value": "KQM732"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2304061",
                "data": {
                    "KQM18246": {
                        "text": "东北营销组",
                        "value": "KQM660"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2298180",
                "data": {
                    "KQM18246": {
                        "text": "西南营销组",
                        "value": "KQM659"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2822117",
                "data": {
                    "KQM18246": {
                        "text": "上海营销组",
                        "value": "KQM547"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2304064",
                "data": {
                    "KQM18246": {
                        "text": "华南营销组",
                        "value": "KQM583"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2304065",
                "data": {
                    "KQM18246": {
                        "text": "湖北营销组",
                        "value": "KQM584"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2304062",
                "data": {
                    "KQM18246": {
                        "text": "西北营销组",
                        "value": "KQM581"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2304063",
                "data": {
                    "KQM18246": {
                        "text": "东南营销组",
                        "value": "KQM582"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2298179",
                "data": {
                    "KQM18246": {
                        "text": "华北营销组",
                        "value": "KQM658"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2495732",
                "data": {
                    "KQM18246": {
                        "text": "销售一组(调试)",
                        "value": "KQM676"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2495738",
                "data": {
                    "KQM18246": {
                        "text": "产品一组(调试)",
                        "value": "KQM678"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2296066",
                "data": {
                    "KQM18246": {
                        "text": "北京交付组",
                        "value": "KQM458"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2296067",
                "data": {
                    "KQM18246": {
                        "text": "国办交付组",
                        "value": "KQM459"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2296068",
                "data": {
                    "KQM18246": {
                        "text": "JD交付组",
                        "value": "KQM460"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2967713",
                "data": {
                    "KQM18246": {
                        "text": "交付实施一部",
                        "value": "KQM548"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2253736",
                "data": {
                    "KQM18246": {
                        "text": "质量保障部",
                        "value": "KQM453"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2253735",
                "data": {
                    "KQM18246": {
                        "text": "研发三部",
                        "value": "KQM452"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2259056",
                "data": {
                    "KQM18246": {
                        "text": "研发一部",
                        "value": "HW133"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2253731",
                "data": {
                    "KQM18246": {
                        "text": "产品创新部",
                        "value": "HW163"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2259057",
                "data": {
                    "KQM18246": {
                        "text": "交付实施二部",
                        "value": "HW134"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2259055",
                "data": {
                    "KQM18246": {
                        "text": "研发二部",
                        "value": "HW132"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2951501",
                "data": {
                    "KQM18246": {
                        "text": "改进发展部",
                        "value": "KQM544"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2963701",
                "data": {
                    "KQM18246": {
                        "text": "营销咨询中心",
                        "value": "KQM541"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2253737",
                "data": {
                    "KQM18246": {
                        "text": "总经办",
                        "value": "HW35"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2303007",
                "data": {
                    "KQM18246": {
                        "text": "前端研发二组",
                        "value": "KQM512"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2309195",
                "data": {
                    "KQM18246": {
                        "text": "后端研发三组",
                        "value": "KQM619"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2309193",
                "data": {
                    "KQM18246": {
                        "text": "后端研发二组",
                        "value": "KQM613"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2309192",
                "data": {
                    "KQM18246": {
                        "text": "后端研发一组",
                        "value": "KQM520"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2303004",
                "data": {
                    "KQM18246": {
                        "text": "前端研发一组",
                        "value": "KQM486"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2970878",
                "data": {
                    "KQM18246": {
                        "text": "总监助理",
                        "value": "KQM734"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2301567",
                "data": {
                    "KQM18246": {
                        "text": "前端研发二组",
                        "value": "KQM617"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2309221",
                "data": {
                    "KQM18246": {
                        "text": "一码通测试组",
                        "value": "KQM645"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2303009",
                "data": {
                    "KQM18246": {
                        "text": "前端研发三组",
                        "value": "KQM514"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2967656",
                "data": {
                    "KQM18246": {
                        "text": "改进发展组",
                        "value": "KQM545"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2301568",
                "data": {
                    "KQM18246": {
                        "text": "后端研发一组",
                        "value": "KQM618"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2975638",
                "data": {
                    "KQM18246": {
                        "text": "架构师",
                        "value": "KQM733"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2302971",
                "data": {
                    "KQM18246": {
                        "text": "后端研发三组",
                        "value": "KQM642"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2301564",
                "data": {
                    "KQM18246": {
                        "text": "码组",
                        "value": "KQM612"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2975604",
                "data": {
                    "KQM18246": {
                        "text": "IMS系统研发组",
                        "value": "KQM550"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2306618",
                "data": {
                    "KQM18246": {
                        "text": "研发二组",
                        "value": "KQM511"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2301563",
                "data": {
                    "KQM18246": {
                        "text": "研发三组",
                        "value": "KQM515"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2309222",
                "data": {
                    "KQM18246": {
                        "text": "人事行政组",
                        "value": "KQM648"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2309199",
                "data": {
                    "KQM18246": {
                        "text": "空间产品测试组",
                        "value": "KQM643"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2304070",
                "data": {
                    "KQM18246": {
                        "text": "招投标组",
                        "value": "KQM589"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2301562",
                "data": {
                    "KQM18246": {
                        "text": "研发四组",
                        "value": "KQM513"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2304068",
                "data": {
                    "KQM18246": {
                        "text": "南方组",
                        "value": "KQM587"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2309197",
                "data": {
                    "KQM18246": {
                        "text": "智办产品测试组",
                        "value": "KQM454"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2299163",
                "data": {
                    "KQM18246": {
                        "text": "智办产品创新组",
                        "value": "KQM516"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2304067",
                "data": {
                    "KQM18246": {
                        "text": "上海一组",
                        "value": "KQM586"
                    }
                },
                "children": null
            }
        ],
        "headerTree": [
            {
                "id": "KQM18246",
                "name": "name",
                "objId": "KQM1700",
                "sort": 1,
                "title": "部门名称",
                "type": "field",
                "children": null,
                "width": null
            }
        ],
        "nameFieldType": "MULTITOONE",
        "pageSize": 50,
        "canQuery": false
    },
    "tid": null,
    "originalTid": null
}
```

```bash
# 这是获取所属商机项目
curl 'https://pms.tongbaninfo.com:8888/app/api/standardList/front/getSimpleSearchListDataBySetField' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -b '_currency=null; cp-hub-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjJmNWNmZDk1LTY1ZTUtMTFlYi1iM2M3LTAyMjZjYTViMThhYSIsInVzZXJuYW1lIjoiYWRtaW4iLCJlbWFpbCI6IiIsImV4cCI6NjE3NDcxMjg1NjcsImlhdCI6MTc0NzEyODYyN30.LK4YFgg89pjJ4FLdUc-W2LLUBS98SUlTo3ksm7fAY2k; envList=[{%22userTag%22:%22beta%22%2C%22feTag%22:%22beta%22%2C%22id%22:%22mjxklah2r%22}]; _timezone=UTC%2B08%3A00; tokenId=bd8b798250bbd574af7c2a85371b0641; _lang=zh_CN; tongbaninfo.com_watch_currentTime=2025-06-09T15:22:05+08:00' \
  -H 'Origin: https://pms.tongbaninfo.com:8888' \
  -H 'Referer: https://pms.tongbaninfo.com:8888/app' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36' \
  -H '_lang: zh_CN' \
  -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sw6: 1-MS44ODQ2MTY3MTUuMTc0OTQ1MzcyNjA0MTAwMDE=-MS44ODQ2MTY3MTUuMTc0OTQ1MzcyNjA0MTAwMDE=-0-1-1-IzEyNy4wLjAuMTo4MA==-LTE=-LTE=' \
  --data-raw '{"keyword":"","pageNo":1,"pageSize":50,"standardCollectionId":"HW888","parentMetadataId":"HW760","resourceType":"standard","requestType":"code","queryFieldId":"HW23572","deviceType":"web","parentLayoutId":"coordinate_HW4218","_crumb":"********************************"}'
```
```json
// 这是所属商机项目的返回值
{
    "code": 200,
    "requestTime": null,
    "appName": null,
    "msg": "服务器成功返回请求的数据",
    "subCode": null,
    "subMsg": null,
    "subMsgType": "success",
    "data": {
        "openTableHeader": null,
        "nameFieldInfo": {
            "KQM2255648": {
                "text": "2023年上海市徐汇区一网统管商机项目",
                "value": "2023年上海市徐汇区一网统管商机项目"
            },
            "KQM2247672": {
                "text": "2022年上海市奉贤区产教融合信息服务平台建设商机项目",
                "value": "2022年上海市奉贤区产教融合信息服务平台建设商机项目"
            },
            "KQM2251586": {
                "text": "2024年上海市松江区企业专属网页四期商机项目",
                "value": "2024年上海市松江区企业专属网页四期商机项目"
            },
            "KQM2251973": {
                "text": "2025年上海市静安区“一网通办”服务能力提升建设商机项目",
                "value": "2025年上海市静安区“一网通办”服务能力提升建设商机项目"
            },
            "KQM2251588": {
                "text": "2023年上海市松江区深化”两页“建设(企业专属网页)商机项目",
                "value": "2023年上海市松江区深化”两页“建设(企业专属网页)商机项目"
            },
            "KQM2251920": {
                "text": "2024年上海市普陀区一网通办运营商机项目",
                "value": "2024年上海市普陀区一网通办运营商机项目"
            },
            "KQM2251978": {
                "text": "2024年上海市静安区政策匹配交付商机项目",
                "value": "2024年上海市静安区政策匹配交付商机项目"
            },
            "KQM2246359": {
                "text": "2024年上海市浦东新区政务服务中心一网通办运营服务商机项目",
                "value": "2024年上海市浦东新区政务服务中心一网通办运营服务商机项目"
            },
            "KQM2251917": {
                "text": "2023年上海市普陀区企业专属网页商机项目",
                "value": "2023年上海市普陀区企业专属网页商机项目"
            },
            "KQM2251708": {
                "text": "2024年上海市浦东新区（浦策通）空间+智能客服交付商机项目",
                "value": "2024年上海市浦东新区（浦策通）空间+智能客服交付商机项目"
            },
            "KQM2251980": {
                "text": "2025年上海市中国(上海)自贸区企业专属网页运维商机项目",
                "value": "2025年上海市中国(上海)自贸区企业专属网页运维商机项目"
            }
        },
        "current": 1,
        "total": 11,
        "queryFieldMap": [
            {
                "displayLabel": "项目名称",
                "id": "HW20948"
            }
        ],
        "pages": 1,
        "listhead": [
            {
                "fieldId": null,
                "fieldName": null,
                "type": null,
                "formuleType": null,
                "fieldCode": null,
                "readOnly": null,
                "updateable": null,
                "required": null,
                "objId": null,
                "objName": null,
                "example": null,
                "columnType": null,
                "masterDataStyle": null,
                "masterDataShowType": null,
                "dataSource": null,
                "dataSourceType": null,
                "cascadingStandardMetaId": null,
                "cascadingStandardFieldId": null,
                "cascadingStandardMetaName": null,
                "cascadingStandardFieldName": null,
                "defaultInternationalAddressData": null,
                "apiName": "name",
                "parentObj": false,
                "parentObjName": null,
                "parentObjId": null,
                "storageMode": null,
                "child": {
                    "compType": "TEXT",
                    "sendRequest": {
                        "layoutUpdate": {
                            "enable": false
                        },
                        "formulaFieldsCalc": {
                            "reloadRelatedList": [],
                            "enable": false
                        },
                        "externalFieldsCalc": {
                            "reloadRelatedList": [],
                            "enable": false
                        },
                        "relyFieldsCalc": {
                            "enable": false
                        },
                        "componentsUpdate": {
                            "enable": false
                        }
                    },
                    "cascading": false,
                    "i18nAddressMap": {},
                    "addressType": "RADIO",
                    "addressSelectLevel": 4,
                    "buildQRCode": false,
                    "customProps": null,
                    "collectType": null,
                    "addressVer": null,
                    "realType": null,
                    "dataStyle": null
                },
                "alonePossess": null,
                "treeFilterable": null,
                "asynchronousTreeData": null,
                "fieldDefaultValue": null
            }
        ],
        "data": [
            {
                "id": "KQM2247672",
                "data": {
                    "HW20948": {
                        "text": "2022年上海市奉贤区产教融合信息服务平台建设商机项目",
                        "value": "2022年上海市奉贤区产教融合信息服务平台建设商机项目"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2251917",
                "data": {
                    "HW20948": {
                        "text": "2023年上海市普陀区企业专属网页商机项目",
                        "value": "2023年上海市普陀区企业专属网页商机项目"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2251586",
                "data": {
                    "HW20948": {
                        "text": "2024年上海市松江区企业专属网页四期商机项目",
                        "value": "2024年上海市松江区企业专属网页四期商机项目"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2251980",
                "data": {
                    "HW20948": {
                        "text": "2025年上海市中国(上海)自贸区企业专属网页运维商机项目",
                        "value": "2025年上海市中国(上海)自贸区企业专属网页运维商机项目"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2251920",
                "data": {
                    "HW20948": {
                        "text": "2024年上海市普陀区一网通办运营商机项目",
                        "value": "2024年上海市普陀区一网通办运营商机项目"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2246359",
                "data": {
                    "HW20948": {
                        "text": "2024年上海市浦东新区政务服务中心一网通办运营服务商机项目",
                        "value": "2024年上海市浦东新区政务服务中心一网通办运营服务商机项目"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2251708",
                "data": {
                    "HW20948": {
                        "text": "2024年上海市浦东新区（浦策通）空间+智能客服交付商机项目",
                        "value": "2024年上海市浦东新区（浦策通）空间+智能客服交付商机项目"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2251973",
                "data": {
                    "HW20948": {
                        "text": "2025年上海市静安区“一网通办”服务能力提升建设商机项目",
                        "value": "2025年上海市静安区“一网通办”服务能力提升建设商机项目"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2251978",
                "data": {
                    "HW20948": {
                        "text": "2024年上海市静安区政策匹配交付商机项目",
                        "value": "2024年上海市静安区政策匹配交付商机项目"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2255648",
                "data": {
                    "HW20948": {
                        "text": "2023年上海市徐汇区一网统管商机项目",
                        "value": "2023年上海市徐汇区一网统管商机项目"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2251588",
                "data": {
                    "HW20948": {
                        "text": "2023年上海市松江区深化”两页“建设(企业专属网页)商机项目",
                        "value": "2023年上海市松江区深化”两页“建设(企业专属网页)商机项目"
                    }
                },
                "children": null
            }
        ],
        "headerTree": [
            {
                "id": "HW20948",
                "name": "name",
                "objId": "HW888",
                "sort": 1,
                "title": "项目名称",
                "type": "field",
                "children": null,
                "width": null
            }
        ],
        "nameFieldType": "TEXT",
        "pageSize": 50,
        "canQuery": true
    },
    "tid": null,
    "originalTid": null
}
```

```bash
# 这是获取所属产品项目的接口
curl 'https://pms.tongbaninfo.com:8888/app/api/standardList/front/getSimpleSearchListDataBySetField' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -b '_currency=null; cp-hub-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjJmNWNmZDk1LTY1ZTUtMTFlYi1iM2M3LTAyMjZjYTViMThhYSIsInVzZXJuYW1lIjoiYWRtaW4iLCJlbWFpbCI6IiIsImV4cCI6NjE3NDcxMjg1NjcsImlhdCI6MTc0NzEyODYyN30.LK4YFgg89pjJ4FLdUc-W2LLUBS98SUlTo3ksm7fAY2k; envList=[{%22userTag%22:%22beta%22%2C%22feTag%22:%22beta%22%2C%22id%22:%22mjxklah2r%22}]; _timezone=UTC%2B08%3A00; tokenId=bd8b798250bbd574af7c2a85371b0641; _lang=zh_CN; tongbaninfo.com_watch_currentTime=2025-06-09T15:25:17+08:00' \
  -H 'Origin: https://pms.tongbaninfo.com:8888' \
  -H 'Referer: https://pms.tongbaninfo.com:8888/app' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36' \
  -H '_lang: zh_CN' \
  -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sw6: 1-MS44ODQ2MTY3MTUuMTc0OTQ1MzkxNzgwMTAwMDE=-MS44ODQ2MTY3MTUuMTc0OTQ1MzkxNzgwMTAwMDE=-0-1-1-IzEyNy4wLjAuMTo4MA==-LTE=-LTE=' \
  --data-raw '{"keyword":"","pageNo":1,"pageSize":50,"standardCollectionId":"KQM1744","parentMetadataId":"HW760","resourceType":"standard","requestType":"code","queryFieldId":"KQM18553","deviceType":"web","parentLayoutId":"coordinate_HW4218","_crumb":"********************************"}'
```
```json
// 这是所属产品项目的返回值
{
    "code": 200,
    "requestTime": null,
    "appName": null,
    "msg": "服务器成功返回请求的数据",
    "subCode": null,
    "subMsg": null,
    "subMsgType": "success",
    "data": {
        "openTableHeader": null,
        "nameFieldInfo": {
            "KQM2488070": {
                "text": "2024年通办信息管理系统V1.0产品项目",
                "value": "2024年通办信息管理系统V1.0产品项目"
            }
        },
        "current": 1,
        "total": 1,
        "queryFieldMap": [
            {
                "displayLabel": "产品项目名称",
                "id": "KQM18079"
            }
        ],
        "pages": 1,
        "listhead": [
            {
                "fieldId": null,
                "fieldName": null,
                "type": null,
                "formuleType": null,
                "fieldCode": null,
                "readOnly": null,
                "updateable": null,
                "required": null,
                "objId": null,
                "objName": null,
                "example": null,
                "columnType": null,
                "masterDataStyle": null,
                "masterDataShowType": null,
                "dataSource": null,
                "dataSourceType": null,
                "cascadingStandardMetaId": null,
                "cascadingStandardFieldId": null,
                "cascadingStandardMetaName": null,
                "cascadingStandardFieldName": null,
                "defaultInternationalAddressData": null,
                "apiName": "name",
                "parentObj": false,
                "parentObjName": null,
                "parentObjId": null,
                "storageMode": null,
                "child": {
                    "compType": "TEXT",
                    "sendRequest": {
                        "layoutUpdate": {
                            "enable": false
                        },
                        "formulaFieldsCalc": {
                            "reloadRelatedList": [],
                            "enable": false
                        },
                        "externalFieldsCalc": {
                            "reloadRelatedList": [],
                            "enable": false
                        },
                        "relyFieldsCalc": {
                            "enable": false
                        },
                        "componentsUpdate": {
                            "enable": false
                        }
                    },
                    "cascading": false,
                    "i18nAddressMap": {},
                    "addressType": "RADIO",
                    "addressSelectLevel": 4,
                    "buildQRCode": false,
                    "customProps": null,
                    "collectType": null,
                    "addressVer": null,
                    "realType": null,
                    "dataStyle": null
                },
                "alonePossess": null,
                "treeFilterable": null,
                "asynchronousTreeData": null,
                "fieldDefaultValue": null
            }
        ],
        "data": [
            {
                "id": "KQM2488070",
                "data": {
                    "KQM18079": {
                        "text": "2024年通办信息管理系统V1.0产品项目",
                        "value": "2024年通办信息管理系统V1.0产品项目"
                    }
                },
                "children": null
            }
        ],
        "headerTree": [
            {
                "id": "KQM18079",
                "name": "name",
                "objId": "KQM1744",
                "sort": 1,
                "title": "产品项目名称",
                "type": "field",
                "children": null,
                "width": null
            }
        ],
        "nameFieldType": "TEXT",
        "pageSize": 50,
        "canQuery": true
    },
    "tid": null,
    "originalTid": null
}
```

```bash
# 这是获取审批状态的接口
curl 'https://pms.tongbaninfo.com:8888/app/api/masterData/front/queryPage' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -b '_currency=null; cp-hub-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjJmNWNmZDk1LTY1ZTUtMTFlYi1iM2M3LTAyMjZjYTViMThhYSIsInVzZXJuYW1lIjoiYWRtaW4iLCJlbWFpbCI6IiIsImV4cCI6NjE3NDcxMjg1NjcsImlhdCI6MTc0NzEyODYyN30.LK4YFgg89pjJ4FLdUc-W2LLUBS98SUlTo3ksm7fAY2k; envList=[{%22userTag%22:%22beta%22%2C%22feTag%22:%22beta%22%2C%22id%22:%22mjxklah2r%22}]; _timezone=UTC%2B08%3A00; tokenId=bd8b798250bbd574af7c2a85371b0641; _lang=zh_CN; tongbaninfo.com_watch_currentTime=2025-06-09T15:26:27+08:00' \
  -H 'Origin: https://pms.tongbaninfo.com:8888' \
  -H 'Referer: https://pms.tongbaninfo.com:8888/app' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36' \
  -H '_lang: zh_CN' \
  -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sw6: 1-MS44ODQ2MTY3MTUuMTc0OTQ1Mzk4NzUyMzAwMDE=-MS44ODQ2MTY3MTUuMTc0OTQ1Mzk4NzUyMzAwMDE=-0-1-1-IzEyNy4wLjAuMTo4MA==-LTE=-LTE=' \
  --data-raw '{"pageNo":1,"pageSize":50,"apiId":"HW2375","standardCollectionId":"HW760","fieldId":"HW23575","_crumb":"********************************"}'
```

```json
// 这是审批状态的返回值
{
    "code": 200,
    "requestTime": null,
    "appName": null,
    "msg": "服务器成功返回请求的数据",
    "subCode": null,
    "subMsg": null,
    "subMsgType": "success",
    "data": {
        "total": 10,
        "list": [
            {
                "title": "已提交",
                "value": "KQM695",
                "parentTitle": "商机项目-工时审批",
                "parentPathTitle": "商机项目-工时审批",
                "sortNumber": 1,
                "level": 2,
                "fullPath": "|HW2375|KQM695|",
                "disabled": null,
                "style": null,
                "icon": null
            },
            {
                "title": "直属上级审核",
                "value": "HW2378",
                "parentTitle": "商机项目-工时审批",
                "parentPathTitle": "商机项目-工时审批",
                "sortNumber": 2,
                "level": 2,
                "fullPath": "|HW2375|HW2378|",
                "disabled": null,
                "style": null,
                "icon": null
            },
            {
                "title": "项目组长审核",
                "value": "KQM696",
                "parentTitle": "商机项目-工时审批",
                "parentPathTitle": "商机项目-工时审批",
                "sortNumber": 3,
                "level": 2,
                "fullPath": "|HW2375|KQM696|",
                "disabled": null,
                "style": null,
                "icon": null
            },
            {
                "title": "审批通过",
                "value": "HW2376",
                "parentTitle": "商机项目-工时审批",
                "parentPathTitle": "商机项目-工时审批",
                "sortNumber": 4,
                "level": 2,
                "fullPath": "|HW2375|HW2376|",
                "disabled": null,
                "style": null,
                "icon": null
            },
            {
                "title": "审批驳回",
                "value": "HW2377",
                "parentTitle": "商机项目-工时审批",
                "parentPathTitle": "商机项目-工时审批",
                "sortNumber": 5,
                "level": 2,
                "fullPath": "|HW2375|HW2377|",
                "disabled": null,
                "style": null,
                "icon": null
            },
            {
                "title": "协调人审核",
                "value": "KQM886",
                "parentTitle": "商机项目-工时审批",
                "parentPathTitle": "商机项目-工时审批",
                "sortNumber": 6,
                "level": 2,
                "fullPath": "|HW2375|KQM886|",
                "disabled": null,
                "style": null,
                "icon": null
            },
            {
                "title": "客户负责人审核",
                "value": "KQM887",
                "parentTitle": "商机项目-工时审批",
                "parentPathTitle": "商机项目-工时审批",
                "sortNumber": 7,
                "level": 2,
                "fullPath": "|HW2375|KQM887|",
                "disabled": null,
                "style": null,
                "icon": null
            },
            {
                "title": "任务指派人审核",
                "value": "KQM1265",
                "parentTitle": "商机项目-工时审批",
                "parentPathTitle": "商机项目-工时审批",
                "sortNumber": 8,
                "level": 2,
                "fullPath": "|HW2375|KQM1265|",
                "disabled": null,
                "style": null,
                "icon": null
            },
            {
                "title": "任务副组长审核",
                "value": "KQM1266",
                "parentTitle": "商机项目-工时审批",
                "parentPathTitle": "商机项目-工时审批",
                "sortNumber": 9,
                "level": 2,
                "fullPath": "|HW2375|KQM1266|",
                "disabled": null,
                "style": null,
                "icon": null
            },
            {
                "title": "任务组长审核",
                "value": "KQM1267",
                "parentTitle": "商机项目-工时审批",
                "parentPathTitle": "商机项目-工时审批",
                "sortNumber": 10,
                "level": 2,
                "fullPath": "|HW2375|KQM1267|",
                "disabled": null,
                "style": null,
                "icon": null
            }
        ],
        "pageNum": 1,
        "pageSize": 50,
        "size": 0,
        "startRow": 0,
        "endRow": 0,
        "pages": 0,
        "prePage": 0,
        "nextPage": 0,
        "isFirstPage": false,
        "isLastPage": false,
        "hasPreviousPage": false,
        "hasNextPage": false,
        "navigatePages": 0,
        "navigatepageNums": null,
        "navigateFirstPage": 0,
        "navigateLastPage": 0,
        "dimTotal": null,
        "hasMorePage": false,
        "matchColor": false,
        "matchIcon": false,
        "optionStyle": "iconAndText"
    },
    "tid": null,
    "originalTid": null
}
```

```bash
# 这是获取所属咨询任务的接口
curl 'https://pms.tongbaninfo.com:8888/app/api/standardList/front/getSimpleSearchListDataBySetField' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -b '_currency=null; cp-hub-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjJmNWNmZDk1LTY1ZTUtMTFlYi1iM2M3LTAyMjZjYTViMThhYSIsInVzZXJuYW1lIjoiYWRtaW4iLCJlbWFpbCI6IiIsImV4cCI6NjE3NDcxMjg1NjcsImlhdCI6MTc0NzEyODYyN30.LK4YFgg89pjJ4FLdUc-W2LLUBS98SUlTo3ksm7fAY2k; envList=[{%22userTag%22:%22beta%22%2C%22feTag%22:%22beta%22%2C%22id%22:%22mjxklah2r%22}]; _timezone=UTC%2B08%3A00; tokenId=bd8b798250bbd574af7c2a85371b0641; _lang=zh_CN; tongbaninfo.com_watch_currentTime=2025-06-09T15:28:02+08:00' \
  -H 'Origin: https://pms.tongbaninfo.com:8888' \
  -H 'Referer: https://pms.tongbaninfo.com:8888/app' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36' \
  -H '_lang: zh_CN' \
  -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sw6: 1-MS44ODQ2MTY3MTUuMTc0OTQ1NDA4MjM5MjAwMDE=-MS44ODQ2MTY3MTUuMTc0OTQ1NDA4MjM5MjAwMDE=-0-1-1-IzEyNy4wLjAuMTo4MA==-LTE=-LTE=' \
  --data-raw '{"keyword":"","pageNo":1,"pageSize":50,"standardCollectionId":"KQM1892","parentMetadataId":"HW760","resourceType":"standard","requestType":"code","queryFieldId":"KQM20885","deviceType":"web","parentLayoutId":"coordinate_HW4218","_crumb":"********************************"}'
```
```json
// 这是所属咨询任务的返回值
{
    "code": 200,
    "requestTime": null,
    "appName": null,
    "msg": "服务器成功返回请求的数据",
    "subCode": null,
    "subMsg": null,
    "subMsgType": "success",
    "data": {
        "openTableHeader": null,
        "nameFieldInfo": {
            "KQM2931076": {
                "text": "文档编写",
                "value": "文档编写"
            },
            "KQM2931074": {
                "text": "客户对接",
                "value": "客户对接"
            },
            "KQM2944271": {
                "text": "无锡涉企平台智能化汇报",
                "value": "无锡涉企平台智能化汇报"
            },
            "KQM3127606": {
                "text": "子任务2",
                "value": "子任务2"
            },
            "KQM3127605": {
                "text": "子任务1",
                "value": "子任务1"
            },
            "KQM2938349": {
                "text": "测试河北省一码通产品咨询",
                "value": "测试河北省一码通产品咨询"
            },
            "KQM2938470": {
                "text": "2025年“数字中国”峰会展台内容谋划",
                "value": "2025年“数字中国”峰会展台内容谋划"
            },
            "KQM2943133": {
                "text": "无",
                "value": "无"
            },
            "KQM2941271": {
                "text": "调试暂存001",
                "value": "调试暂存001"
            },
            "KQM2942085": {
                "text": "测试调试暂存002咨询",
                "value": "测试调试暂存002咨询"
            },
            "KQM2943132": {
                "text": "无",
                "value": "无"
            },
            "KQM3045916": {
                "text": "2026年浦东新区企业专属网页（浦东办小程序）运维项目申报工作相关任务",
                "value": "2026年浦东新区企业专属网页（浦东办小程序）运维项目申报工作相关任务"
            },
            "KQM2922197": {
                "text": "2025年“数字中国”峰会展台内容谋划",
                "value": "2025年“数字中国”峰会展台内容谋划"
            },
            "KQM2955095": {
                "text": "测试暂存调试002",
                "value": "测试暂存调试002"
            },
            "KQM2928611": {
                "text": "项目跟踪",
                "value": "项目跟踪"
            },
            "KQM2928612": {
                "text": "简案审核",
                "value": "简案审核"
            },
            "KQM2953330": {
                "text": "事项精细化梳理方案修改",
                "value": "事项精细化梳理方案修改"
            },
            "KQM2956960": {
                "text": "无锡涉企平台智能化汇报",
                "value": "无锡涉企平台智能化汇报"
            },
            "KQM2959557": {
                "text": "区级政策平台技术方案",
                "value": "区级政策平台技术方案"
            },
            "KQM2959434": {
                "text": "测试调试暂存002咨询",
                "value": "测试调试暂存002咨询"
            },
            "KQM2948286": {
                "text": "无锡涉企平台智能化汇报",
                "value": "无锡涉企平台智能化汇报"
            },
            "KQM3138953": {
                "text": "三级子任务0609",
                "value": "三级子任务0609"
            },
            "KQM2922705": {
                "text": "大模型赋能图谱动态更新及拆解",
                "value": "大模型赋能图谱动态更新及拆解"
            },
            "KQM2942537": {
                "text": "测试测试测试",
                "value": "测试测试测试"
            },
            "KQM2942536": {
                "text": "测试测试测试",
                "value": "测试测试测试"
            },
            "KQM2935537": {
                "text": "做demo",
                "value": "做demo"
            },
            "KQM2935536": {
                "text": "客户交流",
                "value": "客户交流"
            },
            "KQM2959943": {
                "text": "测试暂存调试003",
                "value": "测试暂存调试003"
            },
            "KQM2938408": {
                "text": "测试河北省一码通产品咨询",
                "value": "测试河北省一码通产品咨询"
            },
            "KQM2941419": {
                "text": "简案编制",
                "value": "简案编制"
            },
            "KQM2933714": {
                "text": "沪府办发【2025】7号文及随申码常态化运营推广工作要点文件解读及谋划会",
                "value": "沪府办发【2025】7号文及随申码常态化运营推广工作要点文件解读及谋划会"
            },
            "KQM2941418": {
                "text": "简案审核",
                "value": "简案审核"
            },
            "KQM2956958": {
                "text": "无锡涉企平台智能化汇报",
                "value": "无锡涉企平台智能化汇报"
            },
            "KQM2956957": {
                "text": "无锡涉企平台智能化汇报",
                "value": "无锡涉企平台智能化汇报"
            },
            "KQM2938685": {
                "text": "临港随申码子平台建设方案 （简案）编写及汇报",
                "value": "临港随申码子平台建设方案 （简案）编写及汇报"
            },
            "KQM2956959": {
                "text": "无锡涉企平台智能化汇报",
                "value": "无锡涉企平台智能化汇报"
            },
            "KQM2935535": {
                "text": "客户交流",
                "value": "客户交流"
            },
            "KQM2943795": {
                "text": "1",
                "value": "1"
            },
            "KQM2935061": {
                "text": "无锡涉企平台智能化汇报",
                "value": "无锡涉企平台智能化汇报"
            },
            "KQM2941417": {
                "text": "客户对接",
                "value": "客户对接"
            },
            "KQM2944600": {
                "text": "“沪府办发【2025】7号文”及“随申码常态化运营推广工作要点”文件的解读及谋划",
                "value": "“沪府办发【2025】7号文”及“随申码常态化运营推广工作要点”文件的解读及谋划"
            },
            "KQM2943798": {
                "text": "3",
                "value": "3"
            },
            "KQM3138895": {
                "text": "【测试】三级任务异常测试",
                "value": "【测试】三级任务异常测试"
            },
            "KQM2953355": {
                "text": "智能中枢系统部署及事项精细化梳理成本预估",
                "value": "智能中枢系统部署及事项精细化梳理成本预估"
            },
            "KQM3138537": {
                "text": "三级子任务01",
                "value": "三级子任务01"
            },
            "KQM3061647": {
                "text": "对接客户、协调内部资源",
                "value": "对接客户、协调内部资源"
            },
            "KQM2938379": {
                "text": "测试河北省一码通产品咨询",
                "value": "测试河北省一码通产品咨询"
            },
            "KQM2945468": {
                "text": "长宁区惠企政策全流程平台汇报ppt",
                "value": "长宁区惠企政策全流程平台汇报ppt"
            },
            "KQM2941420": {
                "text": "材料提供",
                "value": "材料提供"
            },
            "KQM2945223": {
                "text": "苏州新建元数字科技关于人工智能+政务服务汇报交流",
                "value": "苏州新建元数字科技关于人工智能+政务服务汇报交流"
            }
        },
        "current": 1,
        "total": 340,
        "queryFieldMap": [
            {
                "displayLabel": "名称",
                "id": "KQM19471"
            }
        ],
        "pages": 7,
        "listhead": [
            {
                "fieldId": null,
                "fieldName": null,
                "type": null,
                "formuleType": null,
                "fieldCode": null,
                "readOnly": null,
                "updateable": null,
                "required": null,
                "objId": null,
                "objName": null,
                "example": null,
                "columnType": null,
                "masterDataStyle": null,
                "masterDataShowType": null,
                "dataSource": null,
                "dataSourceType": null,
                "cascadingStandardMetaId": null,
                "cascadingStandardFieldId": null,
                "cascadingStandardMetaName": null,
                "cascadingStandardFieldName": null,
                "defaultInternationalAddressData": null,
                "apiName": "name",
                "parentObj": false,
                "parentObjName": null,
                "parentObjId": null,
                "storageMode": null,
                "child": {
                    "compType": "AUTONUMBER",
                    "sendRequest": {
                        "layoutUpdate": {
                            "enable": false
                        },
                        "formulaFieldsCalc": {
                            "reloadRelatedList": [],
                            "enable": false
                        },
                        "externalFieldsCalc": {
                            "reloadRelatedList": [],
                            "enable": false
                        },
                        "relyFieldsCalc": {
                            "enable": false
                        },
                        "componentsUpdate": {
                            "enable": false
                        }
                    },
                    "cascading": false,
                    "i18nAddressMap": {},
                    "addressType": "RADIO",
                    "addressSelectLevel": 4,
                    "buildQRCode": false,
                    "customProps": null,
                    "collectType": null,
                    "addressVer": null,
                    "realType": null,
                    "dataStyle": null
                },
                "alonePossess": null,
                "treeFilterable": null,
                "asynchronousTreeData": null,
                "fieldDefaultValue": null
            }
        ],
        "data": [
            {
                "id": "KQM3138953",
                "data": {
                    "KQM19471": {
                        "text": "三级子任务0609",
                        "value": "三级子任务0609"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3138895",
                "data": {
                    "KQM19471": {
                        "text": "【测试】三级任务异常测试",
                        "value": "【测试】三级任务异常测试"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3127606",
                "data": {
                    "KQM19471": {
                        "text": "子任务2",
                        "value": "子任务2"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3127605",
                "data": {
                    "KQM19471": {
                        "text": "子任务1",
                        "value": "子任务1"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3138537",
                "data": {
                    "KQM19471": {
                        "text": "三级子任务01",
                        "value": "三级子任务01"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3045916",
                "data": {
                    "KQM19471": {
                        "text": "2026年浦东新区企业专属网页（浦东办小程序）运维项目申报工作相关任务",
                        "value": "2026年浦东新区企业专属网页（浦东办小程序）运维项目申报工作相关任务"
                    }
                },
                "children": null
            },
            {
                "id": "KQM3061647",
                "data": {
                    "KQM19471": {
                        "text": "对接客户、协调内部资源",
                        "value": "对接客户、协调内部资源"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2938349",
                "data": {
                    "KQM19471": {
                        "text": "测试河北省一码通产品咨询",
                        "value": "测试河北省一码通产品咨询"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2938379",
                "data": {
                    "KQM19471": {
                        "text": "测试河北省一码通产品咨询",
                        "value": "测试河北省一码通产品咨询"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2938408",
                "data": {
                    "KQM19471": {
                        "text": "测试河北省一码通产品咨询",
                        "value": "测试河北省一码通产品咨询"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2938470",
                "data": {
                    "KQM19471": {
                        "text": "2025年“数字中国”峰会展台内容谋划",
                        "value": "2025年“数字中国”峰会展台内容谋划"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2922197",
                "data": {
                    "KQM19471": {
                        "text": "2025年“数字中国”峰会展台内容谋划",
                        "value": "2025年“数字中国”峰会展台内容谋划"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2922705",
                "data": {
                    "KQM19471": {
                        "text": "大模型赋能图谱动态更新及拆解",
                        "value": "大模型赋能图谱动态更新及拆解"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2935535",
                "data": {
                    "KQM19471": {
                        "text": "客户交流",
                        "value": "客户交流"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2935536",
                "data": {
                    "KQM19471": {
                        "text": "客户交流",
                        "value": "客户交流"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2935537",
                "data": {
                    "KQM19471": {
                        "text": "做demo",
                        "value": "做demo"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2938685",
                "data": {
                    "KQM19471": {
                        "text": "临港随申码子平台建设方案 （简案）编写及汇报",
                        "value": "临港随申码子平台建设方案 （简案）编写及汇报"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2941417",
                "data": {
                    "KQM19471": {
                        "text": "客户对接",
                        "value": "客户对接"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2941418",
                "data": {
                    "KQM19471": {
                        "text": "简案审核",
                        "value": "简案审核"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2941419",
                "data": {
                    "KQM19471": {
                        "text": "简案编制",
                        "value": "简案编制"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2941420",
                "data": {
                    "KQM19471": {
                        "text": "材料提供",
                        "value": "材料提供"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2928611",
                "data": {
                    "KQM19471": {
                        "text": "项目跟踪",
                        "value": "项目跟踪"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2928612",
                "data": {
                    "KQM19471": {
                        "text": "简案审核",
                        "value": "简案审核"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2931074",
                "data": {
                    "KQM19471": {
                        "text": "客户对接",
                        "value": "客户对接"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2931076",
                "data": {
                    "KQM19471": {
                        "text": "文档编写",
                        "value": "文档编写"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2944600",
                "data": {
                    "KQM19471": {
                        "text": "“沪府办发【2025】7号文”及“随申码常态化运营推广工作要点”文件的解读及谋划",
                        "value": "“沪府办发【2025】7号文”及“随申码常态化运营推广工作要点”文件的解读及谋划"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2945223",
                "data": {
                    "KQM19471": {
                        "text": "苏州新建元数字科技关于人工智能+政务服务汇报交流",
                        "value": "苏州新建元数字科技关于人工智能+政务服务汇报交流"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2945468",
                "data": {
                    "KQM19471": {
                        "text": "长宁区惠企政策全流程平台汇报ppt",
                        "value": "长宁区惠企政策全流程平台汇报ppt"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2933714",
                "data": {
                    "KQM19471": {
                        "text": "沪府办发【2025】7号文及随申码常态化运营推广工作要点文件解读及谋划会",
                        "value": "沪府办发【2025】7号文及随申码常态化运营推广工作要点文件解读及谋划会"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2948286",
                "data": {
                    "KQM19471": {
                        "text": "无锡涉企平台智能化汇报",
                        "value": "无锡涉企平台智能化汇报"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2935061",
                "data": {
                    "KQM19471": {
                        "text": "无锡涉企平台智能化汇报",
                        "value": "无锡涉企平台智能化汇报"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2953330",
                "data": {
                    "KQM19471": {
                        "text": "事项精细化梳理方案修改",
                        "value": "事项精细化梳理方案修改"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2953355",
                "data": {
                    "KQM19471": {
                        "text": "智能中枢系统部署及事项精细化梳理成本预估",
                        "value": "智能中枢系统部署及事项精细化梳理成本预估"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2941271",
                "data": {
                    "KQM19471": {
                        "text": "调试暂存001",
                        "value": "调试暂存001"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2942085",
                "data": {
                    "KQM19471": {
                        "text": "测试调试暂存002咨询",
                        "value": "测试调试暂存002咨询"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2955095",
                "data": {
                    "KQM19471": {
                        "text": "测试暂存调试002",
                        "value": "测试暂存调试002"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2959434",
                "data": {
                    "KQM19471": {
                        "text": "测试调试暂存002咨询",
                        "value": "测试调试暂存002咨询"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2942536",
                "data": {
                    "KQM19471": {
                        "text": "测试测试测试",
                        "value": "测试测试测试"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2942537",
                "data": {
                    "KQM19471": {
                        "text": "测试测试测试",
                        "value": "测试测试测试"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2959557",
                "data": {
                    "KQM19471": {
                        "text": "区级政策平台技术方案",
                        "value": "区级政策平台技术方案"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2959943",
                "data": {
                    "KQM19471": {
                        "text": "测试暂存调试003",
                        "value": "测试暂存调试003"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2943132",
                "data": {
                    "KQM19471": {
                        "text": "无",
                        "value": "无"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2943133",
                "data": {
                    "KQM19471": {
                        "text": "无",
                        "value": "无"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2943795",
                "data": {
                    "KQM19471": {
                        "text": "1",
                        "value": "1"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2943798",
                "data": {
                    "KQM19471": {
                        "text": "3",
                        "value": "3"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2956957",
                "data": {
                    "KQM19471": {
                        "text": "无锡涉企平台智能化汇报",
                        "value": "无锡涉企平台智能化汇报"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2956958",
                "data": {
                    "KQM19471": {
                        "text": "无锡涉企平台智能化汇报",
                        "value": "无锡涉企平台智能化汇报"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2956959",
                "data": {
                    "KQM19471": {
                        "text": "无锡涉企平台智能化汇报",
                        "value": "无锡涉企平台智能化汇报"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2956960",
                "data": {
                    "KQM19471": {
                        "text": "无锡涉企平台智能化汇报",
                        "value": "无锡涉企平台智能化汇报"
                    }
                },
                "children": null
            },
            {
                "id": "KQM2944271",
                "data": {
                    "KQM19471": {
                        "text": "无锡涉企平台智能化汇报",
                        "value": "无锡涉企平台智能化汇报"
                    }
                },
                "children": null
            }
        ],
        "headerTree": [
            {
                "id": "KQM19471",
                "name": "name",
                "objId": "KQM1892",
                "sort": 1,
                "title": "名称",
                "type": "field",
                "children": null,
                "width": null
            }
        ],
        "nameFieldType": "AUTONUMBER",
        "pageSize": 50,
        "canQuery": true
    },
    "tid": null,
    "originalTid": null
}
```