import React, { useState, useEffect } from "react";
import { Typography, Spin, Alert, Empty, Card, Button } from "antd";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title as ChartTitle,
  Tooltip as ChartTooltip,
  Legend as ChartLegend,
  ArcElement,
  PointElement,
  LineElement,
} from "chart.js";

// 组件导入
import { FilterPanel, StatisticsCards, AnalysisCharts } from "./components";

// Hook导入
import { useWorkHoursData, useFilterOptions } from "./hooks";

// 类型导入
import { FilterState } from "./types";

// 工具函数导入
import {
  buildApiFilters,
  buildStandardListParams,
  applyLocalFilters,
  generateChartData,
  chartOptions,
  getDetailTableColumns,
  testApiCall,
  testStandardListParams,
  testComplexFilters,
} from "./utils";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ChartTitle,
  ChartTooltip,
  ChartLegend,
  ArcElement,
  PointElement,
  LineElement
);

const { Title } = Typography;

const WorkHoursStatistics = () => {
  // 使用自定义Hook
  const {
    loading,
    processedData,
    statistics,
    analysisData,
    hasData,
    fetchData,
    applyFilters,
    applyStandardFilters,
    updateAnalysis,
  } = useWorkHoursData();

  const { optionsLoading, filterOptions } = useFilterOptions();

  // 筛选器状态
  const [filters, setFilters] = useState<FilterState>({
    departments: [],
    employees: [],
    workTypes: [],
    workTypeValues: [],
    approvalStatuses: [],
    projectTypes: [],
    projects: [],
    opportunityProjects: [],
    productProjects: [],
    consultingTasks: [],
    dateRange: [],
  });

  // 应用筛选
  const handleApplyFilters = async () => {
    // 使用新的标准列表API
    await applyStandardFilters(filters, filterOptions);

    // 如果需要，也可以回退到旧的API
    // const apiFilters = buildApiFilters(filters, filterOptions);
    // await applyFilters(apiFilters);
  };

  // 重置过滤器
  const resetFilters = () => {
    setFilters({
      departments: [],
      employees: [],
      workTypes: [],
      workTypeValues: [],
      approvalStatuses: [],
      projectTypes: [],
      projects: [],
      opportunityProjects: [],
      productProjects: [],
      consultingTasks: [],
      dateRange: [],
    });
  };

  // 当过滤器变化时重新分析数据（前端筛选）
  useEffect(() => {
    if (processedData.length > 0) {
      const filteredData = applyLocalFilters(processedData, filters);
      updateAnalysis(filteredData);
    }
  }, [filters, processedData, updateAnalysis]);

  // 生成图表数据
  const chartData = generateChartData(analysisData);

  // 获取表格列配置
  const detailColumns = getDetailTableColumns();

  // 应用本地筛选的数据
  const filteredData = applyLocalFilters(processedData, filters);

  return (
    <div className="p-4 mx-auto max-w-7xl min-h-screen bg-gray-50">
      <Spin spinning={loading}>
        {/* 头部 */}
        <Card className="mb-6 text-white bg-gradient-to-r from-blue-500 to-purple-600 border-0">
          <div className="text-center">
            <Title level={1} className="!text-white !mb-2">
              工时统计分析报告
            </Title>
            <Typography.Text className="block mb-2 text-lg text-white">
              基于员工工时记录的多维度数据分析
            </Typography.Text>
          </div>
        </Card>

        {/* 筛选器面板 */}
        <FilterPanel
          filters={filters}
          filterOptions={filterOptions}
          optionsLoading={optionsLoading}
          loading={loading}
          hasData={hasData}
          onFiltersChange={setFilters}
          onApplyFilters={handleApplyFilters}
          onResetFilters={resetFilters}
          onRefreshData={fetchData}
        />

        {/* 加载状态 */}
        {loading && (
          <div style={{ textAlign: "center", padding: "100px 0" }}>
            <Spin
              size="large"
              tip="正在获取工时数据，如数据量较大可能需要较长时间..."
            >
              <div style={{ width: "200px", height: "100px" }}></div>
            </Spin>
          </div>
        )}

        {/* 无数据状态 */}
        {!loading && !hasData && (
          <Card>
            <Empty
              description="暂无数据，请设置筛选条件后点击【应用筛选】开始查询"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </Card>
        )}

        {/* 有数据时显示统计和图表 */}
        {!loading && hasData && (
          <>
            {/* 统计卡片 */}
            <StatisticsCards statistics={statistics} />

            {/* 分析图表 */}
            <AnalysisCharts
              analysisData={analysisData}
              chartData={chartData}
              chartOptions={chartOptions}
              detailColumns={detailColumns}
              filteredData={filteredData}
            />
          </>
        )}
      </Spin>
    </div>
  );
};

export default WorkHoursStatistics;
