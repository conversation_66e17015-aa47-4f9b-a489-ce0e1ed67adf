import { useState, useCallback } from "react";
import { Modal } from "antd";
import {
  getWorkHoursStatisticsData,
  getWorkHoursStatisticsDataWithFilters,
  getStandardListData,
  WorkHoursFilterConditions,
  StandardListParams,
} from "../../../api";
import { buildStandardListParams } from "../utils";
import {
  WorkHoursRecord,
  Statistics,
  AnalysisData,
  FilterOptions,
  FilterState,
} from "../types";

// 数据处理函数
export const processWorkHoursData = (jsonData: any[]): WorkHoursRecord[] => {
  if (!jsonData || jsonData.length === 0) {
    return [];
  }
  console.log('jsonData', jsonData);
  return jsonData.map((item) => {
    const data = item.data || item;

    // 确定项目类型和项目名称
    let projectType = "";

    if (data.KQM18553?.text) {
      projectType = "产品项目";
    } else if (data.KQM20885?.text) {
      projectType = "咨询任务";
    } else if (data.KQM19356?.text) {
      projectType = "合同项目";
    } else if (data.KQM17348?.text) {
      projectType = "商机项目小组";
    } else {
      projectType = "未分配项目(请假、出差等)";
    }

    return {
      id: item.id || Math.random().toString(),
      employee: data.KQM19036?.text || "未知员工",
      department: data.KQM19098?.text || "未知部门",
      workType: data.HW23543?.text || "未知类型",
      workTypeValue: data.HW23543?.value || "",
      workDate: data.HW21967?.text || "",
      hours: data.KQM17303?.value || data.KQM17303?.text || 0,
      description: data.HW21969?.text || "",
      status: data.HW23575?.text || "未知状态",
      statusValue: data.HW23575?.value || "",
      project: data.KQM18553?.text || null,
      consultingTask: data.KQM20885?.text || null,
      contractProject: data.KQM19356?.text || null,
      projectType,
      refShangjiTeam: data.KQM17348?.text || null,
    };
  }).filter((item) => !item.department.includes('未知部门'));
};

// 生成统计数据
export const generateStatistics = (data: WorkHoursRecord[]): Statistics => {
  const totalRecords = data.length;
  const totalHours = data.reduce((sum, item) => sum + item.hours, 0);
  const uniqueEmployees = new Set(data.map((item) => item.employee)).size;
  const uniqueDepartments = new Set(data.map((item) => item.department)).size;
  const uniqueDates = new Set(data.map((item) => item.workDate)).size;
  const avgHours = totalRecords > 0 ? totalHours / totalRecords : 0;

  return {
    totalRecords,
    totalHours: Math.round(totalHours * 10) / 10,
    uniqueEmployees,
    uniqueDepartments,
    avgHours: avgHours.toFixed(1),
    workingDays: uniqueDates,
  };
};

// 生成分析数据
export const generateAnalysisData = (data: WorkHoursRecord[]): AnalysisData => {
  // 部门分析
  const deptStats: {
    [key: string]: { hours: number; count: number; employees: Set<string> };
  } = {};
  data.forEach((item) => {
    if (!deptStats[item.department]) {
      deptStats[item.department] = { hours: 0, count: 0, employees: new Set() };
    }
    deptStats[item.department].hours += item.hours;
    deptStats[item.department].count += 1;
    deptStats[item.department].employees.add(item.employee);
  });

  const departmentAnalysis = Object.entries(deptStats)
    .map(([dept, stats]) => ({
      department: dept,
      totalHours: Math.round(stats.hours * 10) / 10,
      recordCount: stats.count,
      employeeCount: stats.employees.size,
      avgHours: (stats.hours / stats.count).toFixed(1),
    }))
    .sort((a, b) => b.totalHours - a.totalHours);

  // 员工分析
  const empStats: {
    [key: string]: { hours: number; count: number; department: string };
  } = {};
  data.forEach((item) => {
    if (!empStats[item.employee]) {
      empStats[item.employee] = {
        hours: 0,
        count: 0,
        department: item.department,
      };
    }
    empStats[item.employee].hours += item.hours;
    empStats[item.employee].count += 1;
  });

  const employeeAnalysis = Object.entries(empStats)
    .map(([emp, stats]) => ({
      employee: emp,
      department: stats.department,
      totalHours: Math.round(stats.hours * 10) / 10,
      recordCount: stats.count,
      avgHours: (stats.hours / stats.count).toFixed(1),
    }))
    .sort((a, b) => b.totalHours - a.totalHours);

  // 工时类型分析
  const typeStats: { [key: string]: { hours: number; count: number } } = {};
  data.forEach((item) => {
    if (!typeStats[item.workType]) {
      typeStats[item.workType] = { hours: 0, count: 0 };
    }
    typeStats[item.workType].hours += item.hours;
    typeStats[item.workType].count += 1;
  });

  const totalHours = data.reduce((sum, item) => sum + item.hours, 0);
  const workTypeAnalysis = Object.entries(typeStats)
    .map(([type, stats]) => ({
      workType: type,
      totalHours: Math.round(stats.hours * 10) / 10,
      recordCount: stats.count,
      percentage: Math.round((stats.hours / totalHours) * 100 * 10) / 10,
    }))
    .sort((a, b) => b.totalHours - a.totalHours);

  // 时间趋势分析
  const timeStats: { [key: string]: { hours: number; count: number } } = {};
  data.forEach((item) => {
    if (item.workDate) {
      if (!timeStats[item.workDate]) {
        timeStats[item.workDate] = { hours: 0, count: 0 };
      }
      timeStats[item.workDate].hours += item.hours;
      timeStats[item.workDate].count += 1;
    }
  });

  const timeAnalysis = Object.entries(timeStats)
    .map(([date, stats]) => ({
      date,
      totalHours: Math.round(stats.hours * 10) / 10,
      recordCount: stats.count,
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  // 项目分析
  const projectStats: {
    [key: string]: { hours: number; count: number; employees: Set<string> };
  } = {};
  data.forEach((item) => {
    const project =
      item.project ||
      item.consultingTask ||
      item.contractProject || 
      item.refShangjiTeam ||
      "未分配项目(请假、出差等)";
    if (!projectStats[project]) {
      projectStats[project] = { hours: 0, count: 0, employees: new Set() };
    }
    projectStats[project].hours += item.hours;
    projectStats[project].count += 1;
    projectStats[project].employees.add(item.employee);
  });

  const projectAnalysis = Object.entries(projectStats)
    .map(([project, stats]) => ({
      project,
      totalHours: Math.round(stats.hours * 10) / 10,
      recordCount: stats.count,
      employeeCount: stats.employees.size,
    }))
    .sort((a, b) => b.totalHours - a.totalHours);

  return {
    departmentAnalysis,
    employeeAnalysis,
    workTypeAnalysis,
    timeAnalysis,
    projectAnalysis,
  };
};

// 自定义Hook
export const useWorkHoursData = () => {
  const [loading, setLoading] = useState(false);
  const [rawData, setRawData] = useState<any[]>([]);
  const [processedData, setProcessedData] = useState<WorkHoursRecord[]>([]);
  const [statistics, setStatistics] = useState<Statistics>({
    totalRecords: 0,
    totalHours: 0,
    uniqueEmployees: 0,
    uniqueDepartments: 0,
    avgHours: "0",
    workingDays: 0,
  });
  const [analysisData, setAnalysisData] = useState<AnalysisData>({
    departmentAnalysis: [],
    employeeAnalysis: [],
    workTypeAnalysis: [],
    timeAnalysis: [],
    projectAnalysis: [],
  });
  const [hasData, setHasData] = useState(false);

  // 获取数据
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getWorkHoursStatisticsData();
      if (response.code === 200 && response.data?.body) {
        const processed = processWorkHoursData(response.data.body);
        setRawData(response.data.body);
        setProcessedData(processed);
        setHasData(true);

        // 生成统计和分析数据
        const stats = generateStatistics(processed);
        const analysis = generateAnalysisData(processed);
        setStatistics(stats);
        setAnalysisData(analysis);
      }
    } catch (error) {
      console.error("获取工时数据失败:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  // 应用筛选（使用旧的API）
  const applyFilters = useCallback(
    async (apiFilters: WorkHoursFilterConditions) => {
      setLoading(true);
      try {
        const response = await getWorkHoursStatisticsDataWithFilters(
          apiFilters
        );

        if (response.code === 200 && response.data?.body) {
          const processed = processWorkHoursData(response.data.body);
          setRawData(response.data.body);
          setProcessedData(processed);
          setHasData(true);

          // 生成统计和分析数据
          const stats = generateStatistics(processed);
          const analysis = generateAnalysisData(processed);
          setStatistics(stats);
          setAnalysisData(analysis);
        }
      } catch (error) {
        console.error("应用筛选失败:", error);
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 应用标准列表筛选（使用新的API）
  const applyStandardFilters = useCallback(
    async (filters: FilterState, filterOptions: FilterOptions) => {
      setLoading(true);
      try {
        let allData: any[] = [];
        let currentPage = 1;
        let totalPages = 1;
        const pageSize = 500;

        // 第一次请求
        const firstResponse = await getStandardListData(
          buildStandardListParams(filters, filterOptions, currentPage, pageSize)
        );

        if (firstResponse.code === 200 && firstResponse.data) {
          const { body, pagination } = firstResponse.data;

          if (body && Array.isArray(body)) {
            allData = [...body];
          }

          // 检查是否有分页信息
          if (pagination && pagination.total > pagination.pageSize) {
            totalPages =
              pagination.pages ||
              Math.ceil(pagination.total / pagination.pageSize);

            console.log(allData,
              `数据总量: ${pagination.total}, 总页数: ${totalPages}, 当前已获取: ${allData.length} 条`
            );

            // 如果数据总量大于4000条，弹出确认框
            if (pagination.total > 4000) {
              const confirmed = await new Promise<boolean>((resolve) => {
                Modal.confirm({
                  title: "⚠️ 大数据量提醒",
                  content: `检测到数据量较大，共 ${pagination.total} 条记录\n\n预计需要请求 ${totalPages} 页数据，可能需要较长时间\n\n💡 建议：您可以通过筛选条件（如日期范围、部门等）来减少数据量，提升查询效率`,
                  okText: "继续获取",
                  cancelText: "取消",
                  okType: "primary",
                  cancelButtonProps: { type: "default" },
                  width: 480,
                  centered: true,
                  onOk: () => resolve(true),
                  onCancel: () => resolve(false),
                });
              });

              if (!confirmed) {
                console.log("用户取消了大数据量查询");
                return;
              }
            }

            // 如果有多页数据，继续请求后续页面
            if (totalPages > 1) {
              const remainingRequests = [];

              for (let page = 2; page <= totalPages; page++) {
                remainingRequests.push(
                  getStandardListData(
                    buildStandardListParams(
                      filters,
                      filterOptions,
                      page,
                      pageSize
                    )
                  )
                );
              }

              console.log(`开始并发请求剩余 ${totalPages - 1} 页数据...`);

              // 并发请求剩余页面
              const remainingResponses = await Promise.all(remainingRequests);

              remainingResponses.forEach((response, index) => {
                if (response.code === 200 && response.data?.body) {
                  allData = allData.concat(response.data.body);
                  console.log(
                    `✅ 第 ${index + 2} 页数据已获取 (${
                      response.data.body.length
                    } 条), 累计: ${allData.length} 条`
                  );
                } else {
                  console.warn(`❌ 第 ${index + 2} 页数据获取失败`);
                }
              });
            }
          }
          console.log(`🎉 数据获取完成！总计: ${allData.length} 条记录`);

          if (allData.length > 0) {
            const processed = processWorkHoursData(allData);
            setRawData(allData);
            setProcessedData(processed);
            setHasData(true);

            // 生成统计和分析数据
            const stats = generateStatistics(processed);
            const analysis = generateAnalysisData(processed);
            setStatistics(stats);
            console.log('analysis', analysis);
            setAnalysisData(analysis);
          } else {
            // 没有数据时清空状态
            setRawData([]);
            setProcessedData([]);
            setHasData(false);
            setStatistics({
              totalRecords: 0,
              totalHours: 0,
              uniqueEmployees: 0,
              uniqueDepartments: 0,
              avgHours: "0",
              workingDays: 0,
            });
            setAnalysisData({
              departmentAnalysis: [],
              employeeAnalysis: [],
              workTypeAnalysis: [],
              timeAnalysis: [],
              projectAnalysis: [],
            });
          }
        }
      } catch (error) {
        console.error("应用标准筛选失败:", error);
        Modal.error({
          title: "数据获取失败",
          content: "获取工时数据时发生错误，请稍后重试或联系系统管理员。",
        });
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 更新分析数据（用于前端筛选）
  const updateAnalysis = useCallback((filteredData: WorkHoursRecord[]) => {
    const stats = generateStatistics(filteredData);
    const analysis = generateAnalysisData(filteredData);
    setStatistics(stats);
    setAnalysisData(analysis);
  }, []);

  return {
    loading,
    rawData,
    processedData,
    statistics,
    analysisData,
    hasData,
    fetchData,
    applyFilters,
    applyStandardFilters,
    updateAnalysis,
  };
};
