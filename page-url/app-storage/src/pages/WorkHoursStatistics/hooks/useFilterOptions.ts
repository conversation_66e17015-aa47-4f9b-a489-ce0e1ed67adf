import { useState, useEffect, useCallback } from "react";
import {
  getDepartmentOptions,
  getEmployeeOptions,
  getWorkTypeOptions,
  getApprovalStatusOptions,
  getProductProjectOptions,
  getConsultingTaskOptions,
  getOpportunityProjectOptions,
} from "../../../api";
import { FilterOptions } from "../types";

export const useFilterOptions = () => {
  const [optionsLoading, setOptionsLoading] = useState(false);
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    departments: [],
    employees: [],
    workTypes: [],
    approvalStatuses: [],
    projects: [],
    opportunityProjects: [],
    productProjects: [],
    consultingTasks: [],
  });

  // 加载筛选选项
  const loadFilterOptions = useCallback(async () => {
    setOptionsLoading(true);
    try {
      const [
        departmentRes,
        employeeRes,
        workTypeRes,
        approvalStatusRes,
        productProjectRes,
        consultingTaskRes,
        opportunityProjectRes,
      ] = await Promise.all([
        getDepartmentOptions(),
        getEmployeeOptions(),
        getWorkTypeOptions(),
        getApprovalStatusOptions(),
        getProductProjectOptions(),
        getConsultingTaskOptions(),
        getOpportunityProjectOptions(),
      ]);
      // 处理部门选项
      const departments =
        departmentRes.code === 200 && departmentRes.data?.data?.map((item: any) => ({
          label: item?.data?.KQM18246?.text,
          value: item.id,
        })).filter((item: any) =>item.label.includes('部'));

      // 处理员工选项
      const employees =
        employeeRes.code === 200 && employeeRes.data?.data?.map((item: any) => ({
          label: item?.data?.KQM18739?.text,
          value: item.id,
        }));

      // 处理工时类型选项
      const workTypes =
        workTypeRes.code === 200 && workTypeRes.data?.list
          ? workTypeRes.data.list.map((item: any) => ({
              label: item.title,
              value: item.value,
            }))
          : [];

      // 处理审批状态选项
      const approvalStatuses =
        approvalStatusRes.code === 200 && approvalStatusRes.data?.list
          ? approvalStatusRes.data.list.map((item: any) => ({
              label: item.title,
              value: item.value,
            }))
          : [];

      // 处理项目选项（分别处理不同项目类型）
      const projects: Array<{ label: string; value: string }> = [];
      const opportunityProjects: Array<{ label: string; value: string }> = [];
      const productProjects: Array<{ label: string; value: string }> = [];
      const consultingTasks: Array<{ label: string; value: string }> = [];

      // 处理商机项目
      if (
        opportunityProjectRes.code === 200 &&
        opportunityProjectRes.data?.nameFieldInfo
      ) {
        opportunityProjectRes.data?.data?.forEach((item: any) => {
          opportunityProjects.push({ label: item?.data?.KQM17331?.text, value: item.id });
          projects.push({ label: item?.data?.KQM17331?.text, value: item.id });
        })
      }

      // 处理产品项目
      if (
        productProjectRes.code === 200 &&
        productProjectRes.data?.nameFieldInfo
      ) {
        productProjectRes.data?.data?.forEach((item: any) => {
          productProjects.push({ label: item?.data?.KQM18079?.text, value: item.id });
          projects.push({ label: item?.data?.KQM18079?.text, value: item.id });
        })
      }

      // 处理咨询任务
      if (
        consultingTaskRes.code === 200 &&
        consultingTaskRes.data?.nameFieldInfo
      ) {
        consultingTaskRes.data?.data?.forEach((item: any) => {
          consultingTasks.push({ label: item?.data?.KQM19471?.text, value: item.id });
          projects.push({ label: item?.data?.KQM19471?.text, value: item.id });
        })
      }

      setFilterOptions({
        departments,
        employees,
        workTypes,
        approvalStatuses,
        projects,
        opportunityProjects,
        productProjects,
        consultingTasks,
      });
      console.log('ssssssssssssssssss',{
        departments,
        employees,
        workTypes,
        approvalStatuses,
        projects,
        opportunityProjects,
        productProjects,
        consultingTasks,
      });
    } catch (error) {
      console.error("加载筛选选项失败:", error);
    } finally {
      setOptionsLoading(false);
    }
  }, []);

  // 组件初始化时加载筛选选项
  useEffect(() => {
    loadFilterOptions();
  }, [loadFilterOptions]);

  return {
    optionsLoading,
    filterOptions,
    loadFilterOptions,
  };
};
