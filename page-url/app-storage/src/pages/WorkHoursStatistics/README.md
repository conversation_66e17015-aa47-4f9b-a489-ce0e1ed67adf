# 工时统计页面

## 📁 项目结构

```
src/pages/WorkHoursStatistics/
├── index.tsx                 # 主组件（重构后仅150行左右）
├── types.ts                  # 类型定义
├── utils/
│   └── index.ts             # 工具函数
├── hooks/
│   ├── index.ts             # Hook导出
│   ├── useWorkHoursData.ts  # 数据处理Hook
│   └── useFilterOptions.ts  # 筛选选项Hook
├── components/
│   ├── index.ts             # 组件导出
│   ├── FilterPanel.tsx      # 筛选器面板组件
│   ├── StatisticsCards.tsx  # 统计卡片组件
│   └── AnalysisCharts.tsx   # 图表分析组件
└── README.md                # 说明文档
```

## 🔧 重构内容

### 1. 组件拆分
- **主组件** (`index.tsx`): 从1303行缩减到约150行，仅负责状态管理和组件协调
- **筛选器面板** (`FilterPanel.tsx`): 独立的筛选器组件，包含所有筛选逻辑
- **统计卡片** (`StatisticsCards.tsx`): 统计数据展示组件
- **图表分析** (`AnalysisCharts.tsx`): 图表和表格展示组件

### 2. Hook抽取
- **useWorkHoursData**: 处理工时数据的获取、处理和分析
- **useFilterOptions**: 处理筛选选项的加载和管理

### 3. 工具函数
- **buildApiFilters**: 构建API筛选条件
- **applyLocalFilters**: 应用前端筛选
- **generateChartData**: 生成图表数据
- **getDetailTableColumns**: 获取表格列配置

### 4. 类型定义
- 所有TypeScript类型定义集中在 `types.ts` 文件中
- 提供完整的类型安全支持

## 🚀 使用方式

### 基本使用
```tsx
import WorkHoursStatistics from './pages/WorkHoursStatistics';

function App() {
  return <WorkHoursStatistics />;
}
```

### 组件独立使用
```tsx
import { FilterPanel, StatisticsCards } from './pages/WorkHoursStatistics/components';
import { useWorkHoursData } from './pages/WorkHoursStatistics/hooks';

function CustomPage() {
  const { statistics, loading } = useWorkHoursData();
  
  return (
    <div>
      <StatisticsCards statistics={statistics} />
    </div>
  );
}
```

## 📊 功能特性

### 筛选功能
- **服务端筛选**: 部门、员工、工时类型、审批状态、项目、日期范围
- **前端筛选**: 项目类型、具体项目的二次筛选
- **组合筛选**: 支持多种筛选条件的组合使用

### 数据分析
- **部门分析**: 部门工时分布图表和详细统计
- **员工分析**: 员工工时排行榜
- **工时类型分析**: 工时类型分布饼图和占比统计
- **时间趋势**: 工时时间趋势线图
- **项目分析**: 项目工时统计表格

### 数据展示
- **统计卡片**: 总记录数、总工时、员工数、部门数、平均工时、工作天数
- **图表展示**: 柱状图、饼图、折线图
- **表格展示**: 支持排序、分页、搜索

## 🔄 数据流

```
用户操作 → 筛选器状态更新 → API调用 → 数据处理 → 统计分析 → 图表更新
```

## 🛠️ 开发指南

### 添加新的筛选器
1. 在 `types.ts` 中添加新的筛选字段
2. 在 `FilterPanel.tsx` 中添加对应的UI组件
3. 在 `utils/index.ts` 中更新 `buildApiFilters` 函数
4. 在API层添加对应的处理逻辑

### 添加新的分析维度
1. 在 `types.ts` 中扩展 `AnalysisData` 接口
2. 在 `hooks/useWorkHoursData.ts` 中更新 `generateAnalysisData` 函数
3. 在 `AnalysisCharts.tsx` 中添加对应的图表组件

### 性能优化
- 使用 `useCallback` 和 `useMemo` 优化重复计算
- 组件懒加载和代码分割
- 数据虚拟化处理大量数据

## 📝 注意事项

1. **类型安全**: 所有组件都有完整的TypeScript类型定义
2. **错误处理**: 包含完善的错误处理和加载状态
3. **响应式设计**: 支持不同屏幕尺寸的自适应布局
4. **可扩展性**: 模块化设计便于功能扩展和维护
5. **测试友好**: 组件拆分后便于单元测试

## 🔗 相关文件

- API接口: `src/api/index.ts`
- 测试页面: `test-api.html`
- 功能说明: `工时统计筛选功能改进说明.md` 