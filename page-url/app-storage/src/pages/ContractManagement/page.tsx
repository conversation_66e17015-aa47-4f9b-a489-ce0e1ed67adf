import React, { useState, useCallback, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { DatePicker, Input, InputNumber, Tabs } from "antd";
import Contract from "./components/Contract";
import { ContractItem, TabKey } from "./types";
import { mockServiceScenarios } from "./components/mockData";
import {
  CascadeInitValue,
  DefaultColumns,
  ProjectBuildItem,
} from "click-plugin-utils/components/casader-project/cascade.type";
import Cascade from "click-plugin-utils/components/casader-project";
import { ColumnsType } from "antd/es/table";
import moment from "moment";
const { TabPane } = Tabs;

/**
 * 合同管理组件使用示例
 * 展示如何正确使用ContractManagement组件
 */
const ContractManagement: React.FC = () => {
  const navigate = useNavigate();

  // 模拟数据
  const mockIncomeContracts: ContractItem[] = [
    {
      id: "1",
      序号: 1,
      合同编号: "HT2025051800TSC",
      合同名称: "2025年四川省大府易享享数据平台合同",
      所属商机项目: "2025年四川省大府易享享数据平台商机项目",
      合同类型: "开发合同",
      合同税率: "13%",
      合同签约金额: 4.0,
      合同收入金额: 3539.82,
      甲方名称: "成都大数据中心",
      商机项目是否超出合同约定部分: "否",
      销售负责人: "李明",
      合同状态: "通过评审",
    },
    {
      id: "2",
      序号: 2,
      合同编号: "HT2025051800TSC",
      合同名称: "2025年四川省大府易享享数据平台合同",
      所属商机项目: "2025年四川省大府易享享数据平台商机项目",
      合同类型: "开发合同",
      合同税率: "13%",
      合同签约金额: 4.0,
      合同收入金额: 3539.82,
      甲方名称: "成都大数据中心",
      商机项目是否超出合同约定部分: "否",
      销售负责人: "李明",
      合同状态: "确认签约",
    },
  ];

  const mockCostContracts: ContractItem[] = [];

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [activeTab, setActiveTab] = useState("product-selection");
  const [projectBuildItems, setProjectBuildItems] = useState<any[]>([]);

  // 示例：设置默认选中状态
  const initValue: CascadeInitValue = {
    // 默认选中 "好办一件事" 和 "智能服务中枢" 场景
    selectedScenarios: ["scenario-1", "scenario-2"],
    // 为每个场景设置默认选中的可选产品
    selectedOptionalProducts: {
      "scenario-1": ["optional-1-1", "optional-1-2"], // 好办一件事：选中材料预审服务系统和电子证照系统
      "scenario-2": ["optional-2-1"], // 智能服务中枢：选中智能推荐引擎
    },
    // 自定义运营服务的数量和描述
    serviceSettings: {
      "service-1-1-1": { quantity: 5, description: "集成办服务-自定义描述" },
      "service-1-1-2": { quantity: 8, description: "跨域办服务-自定义描述" },
      "service-opt-1-1-1": {
        quantity: 2,
        description: "材料预审模型-自定义描述",
      },
    },
  };
  console.log("process.env.NODE_ENV=======", process.env.REQUEST_KEY);
  // 回调函数实现
  const handleTabChange = useCallback((tabKey: string) => {
    console.log("Tab changed to:", tabKey);
    setActiveTab(tabKey);
    setCurrentPage(1); // 切换tab时重置页码
  }, []);

  const handlePageChange = useCallback((page: number, size?: number) => {
    console.log("Page changed:", page, size);
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
    // 这里应该触发数据重新加载
  }, []);

  const handleView = useCallback((contractId: string) => {
    console.log("View contract:", contractId);
    // 这里应该跳转到合同详情页面
  }, []);

  const handleConfirmSign = useCallback(
    (contractId: string) => {
      console.log("Confirm sign contract:", contractId);
      // 跳转到确认签约页面
      navigate(`/contract-management/${contractId}/confirm`);
    },
    [navigate]
  );

  const handleSearch = useCallback((searchText: string) => {
    console.log("Search:", searchText);
    // 这里应该触发搜索
  }, []);

  const handleRefresh = useCallback(() => {
    console.log("Refresh data");
    setLoading(true);
    // 模拟数据加载
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  // 处理字段变化
  const handleFieldChange = useCallback((itemId: string, fieldName: string, value: any) => {
    console.log("字段变化:", itemId, fieldName, value);
    setProjectBuildItems(prevItems => 
      prevItems.map(item => 
        item.id === itemId 
          ? { ...item, [fieldName]: value }
          : item
      )
    );
  }, []);

  // 处理项目选择变化
  const handleSelectionChange = useCallback((items: any[]) => {
    console.log("选择变化:", items);
    setProjectBuildItems(items);
  }, []);

  interface DataType {
    id?: string;
    productCategory: string;
    productName: string;
    serviceContent: string;
    amount: number;
    buildTime: Date;
    manufacturer: string;
    onFieldChange?: (fieldName: string, value: any) => void;
  }

  const columns: ColumnsType<DataType> = useMemo(() => [
    {
      title: "产品类别",
      dataIndex: "productCategory",
      key: "productCategory",
      width: 120,
    },
    {
      title: "产品项目名称",
      dataIndex: "productName",
      key: "productName",
      width: 300,
      render: (text: string) => (
        <span className="font-medium text-gray-800">{text}</span>
      ),
    },
    {
      title: "运营服务内容",
      dataIndex: "serviceContent",
      key: "serviceContent",
      width: 150,
      render: (text: string) => (
        <span className="text-gray-600">{text || "-"}</span>
      ),
    },
    {
      title: "建设时间",
      key: "buildTime",
      width: 100,
      dataIndex: "buildTime",
      render: (_: any, record: DataType) => (
        <div className="flex items-center">
          <DatePicker
            value={record.buildTime ? moment(record.buildTime) : null}
            onChange={(value) => record.onFieldChange?.('buildTime', value?.toDate())}
            size="small"
          />
        </div>
      ),
    },
    {
      title: "厂商",
      key: "manufacturer",
      width: 100,
      dataIndex: "manufacturer",
      render: (_: any, record: DataType) => (
        <div className="flex items-center">
          <Input
            value={record.manufacturer}
            onChange={(e) => record.onFieldChange?.('manufacturer', e.target.value)}
            size="small"
          />
        </div>
      ),
    },
    {
      title: "金额(单位：万元)",
      key: "amount",
      width: 100,
      dataIndex: "amount",
      render: (_: any, record: DataType) => (
        <div className="flex items-center">
          <InputNumber
            min={0}
            value={record.amount}
            onChange={(value) => record.onFieldChange?.('amount', value)}
            size="small"
            className="w-20 rounded"
          />
        </div>
      ),
    },
  ], []);

  return (
    <div className="p-6">
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        size="large"
        className="bg-white rounded-lg shadow-sm"
      >
        <TabPane tab="产品选择" key="product-selection">
          <Cascade
            initialData={mockServiceScenarios}
            initValue={initValue}
            onSelectionChange={handleSelectionChange}
            onQuantityChange={(id, qty) => console.log("数量变化:", id, qty)}
            onDescriptionChange={(id, desc) =>
              console.log("描述变化:", id, desc)
            }
            onFieldChange={handleFieldChange}
            defaultColumns={columns}
          />
          
          {/* 调试面板 - 显示当前 projectBuildItems */}
          <div className="p-4 mt-4 bg-gray-100 rounded-lg">
            <h3 className="mb-2 text-lg font-semibold">调试：当前项目建设清单数据</h3>
            <pre className="overflow-auto max-h-64 text-sm">
              {JSON.stringify(projectBuildItems, null, 2)}
            </pre>
          </div>
        </TabPane>
        <TabPane tab="合同管理" key="contract-management">
          <Contract
            incomeContracts={mockIncomeContracts}
            costContracts={mockCostContracts}
            loading={loading}
            currentPage={currentPage}
            pageSize={pageSize}
            total={mockIncomeContracts.length}
            onTabChange={handleTabChange}
            onPageChange={handlePageChange}
            onView={handleView}
            onConfirmSign={handleConfirmSign}
            onSearch={handleSearch}
            onRefresh={handleRefresh}
          />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ContractManagement;
