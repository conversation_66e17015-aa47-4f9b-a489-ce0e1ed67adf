# 合同管理模块

这个模块实现了完整的产品选择和商机项目管理功能，基于图片需求设计了级联选择和数据同步机制。

## 功能概述

### 1. 产品选择功能
- **左侧服务场景选择**: 树形结构展示各种服务场景，支持单选
- **右侧产品选择**: 根据选中的服务场景显示对应产品，支持多选
- **产品详情展示**: 每个产品包含描述、运营服务清单等详细信息
- **必选产品控制**: 标记为必选的产品勾选后不允许取消

### 2. 商机项目管理
- **自动同步**: 勾选产品后自动添加到商机项目建设清单
- **数量编辑**: 运营服务明细的数量支持实时编辑
- **状态跟踪**: 记录每个商机项目的建设状态
- **数据联动**: 取消产品选择时自动从清单中移除对应项目

## 核心组件

### ProductSelection.tsx
主要的产品选择组件，包含：
- 服务场景树形选择器
- 产品展示和选择界面
- 商机项目建设清单表格
- 所有的交互逻辑和数据同步

### Cascade.tsx
级联选择的容器组件，负责：
- 集成ProductSelection组件
- 处理顶层数据状态管理
- 提供选择摘要信息

## 数据结构设计

### 服务场景 (ServiceScenario)
```typescript
interface ServiceScenario {
  id: string;
  name: string;        // 场景名称
  count: number;       // 包含的产品数量
  children?: ServiceScenario[];
}
```

### 产品信息 (Product)
```typescript
interface Product {
  id: string;
  name: string;        // 产品名称
  description: string; // 产品描述
  isRequired: boolean; // 是否为必选产品
  isSelected: boolean; // 是否已选中
  hasServices: boolean; // 是否有运营服务
  totalCount: number;  // 运营服务总数
  operationServices: OperationServiceItem[];
}
```

### 运营服务项 (OperationServiceItem)
```typescript
interface OperationServiceItem {
  id: string;
  type: string;        // 服务类型（如：集成办服务）
  period: string;      // 服务期间
  description: string; // 业务办理项（如：主题）
  quantity: number;    // 数量
  unit: string;        // 单位（如：个）
  isEditable: boolean; // 是否可编辑
}
```

### 商机项目 (BusinessOpportunityItem)
```typescript
interface BusinessOpportunityItem {
  id: string;
  projectName: string;           // 产品项目名称
  operationServiceContent: string; // 运营服务内容
  measurementMethod: string;     // 计量方式
  quantity: number;              // 数量
  buildStatus: string;           // 建设状态
  productId: string;             // 关联产品ID
  serviceId: string;             // 关联服务ID
}
```

## 核心交互逻辑

### 1. 级联选择逻辑
- 点击左侧服务场景 → 右侧显示对应产品
- 默认选中第一个服务场景
- 树形结构支持展开/收起

### 2. 产品选择逻辑
```typescript
// 处理产品勾选/取消
const handleProductSelect = (productId: string, checked: boolean) => {
  // 必选产品不允许取消勾选
  if (product.isRequired && !checked) {
    return;
  }
  
  if (checked && product.hasServices) {
    // 添加运营服务到商机项目列表
    addToBusinessOpportunity(product.operationServices);
  } else if (!checked) {
    // 从商机项目列表中移除
    removeFromBusinessOpportunity(productId);
  }
};
```

### 3. 数据同步机制
- **勾选产品** → 自动添加所有运营服务到商机项目清单
- **取消勾选** → 自动移除相关商机项目
- **编辑数量** → 实时更新商机项目数据
- **状态变更** → 通知父组件更新

## 特殊功能实现

### 必选产品控制
```typescript
// 必选产品标识
{product.isRequired && (
  <span className="ml-2 text-red-500 text-xs">【必选产品】</span>
)}

// 禁用取消勾选
<Checkbox
  checked={product.isSelected}
  disabled={product.isRequired && product.isSelected}
  onChange={(e) => handleProductSelect(product.id, e.target.checked)}
/>
```

### 运营服务清单展示
- 使用Ant Design Table组件展示运营服务详情
- 支持无数据时显示空状态
- 紧凑型表格适配产品卡片布局

### 商机项目数量编辑
```typescript
// 可编辑数量列
{
  title: '数量',
  render: (quantity: number, record: BusinessOpportunityItem) => (
    <InputNumber
      min={1}
      value={quantity}
      onChange={(value) => handleQuantityChange(record.id, value || 1)}
      className="w-full"
    />
  )
}
```

## 样式设计

### 响应式布局
- 使用Ant Design Grid系统实现左右分栏
- 卡片组件提供良好的视觉层次
- 表格支持横向滚动适配移动端

### 视觉标识
- 蓝色头部区域区分功能区域
- 必选产品红色标签提醒
- 运营服务图标增强识别度

### 交互反馈
- 勾选产品时显示成功消息
- 数量变更实时响应
- 选择摘要实时更新

## 扩展功能

### 数据持久化
- 可通过onBusinessOpportunityChange回调保存数据
- 支持与后端API集成
- 可添加数据验证和错误处理

### 高级筛选
- 可添加产品类型筛选
- 支持关键词搜索
- 添加批量操作功能

### 导出功能
- 支持导出商机项目清单
- 可生成PDF报告
- Excel格式数据导出

## 使用示例

```tsx
import { Cascade } from '@/pages/ContractManagement/components';

const MyPage = () => {
  return (
    <div>
      <Cascade />
    </div>
  );
};
```

## 技术栈

- **React 17** + **TypeScript** - 基础框架
- **Ant Design 4.x** - UI组件库
- **Tailwind CSS** - 样式框架
- **React Hooks** - 状态管理

## 性能优化

- 使用useCallback防止不必要的重渲染
- 合理使用useMemo缓存计算结果
- 表格虚拟滚动处理大数据量
- 组件懒加载减少初始包大小 