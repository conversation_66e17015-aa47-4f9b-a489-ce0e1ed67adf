import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import ContractDetail from "./components/ContractDetail";
import type {
  ContractDetailData,
  StagePaymentItem,
  PaymentDetailItem,
} from "./types";

/**
 * 合同详情页面
 * 路由页面，负责数据获取和状态管理
 */
const ContractDetailPage: React.FC = () => {
  const { contractId } = useParams<{ contractId: string }>();
  const navigate = useNavigate();

  // 状态管理
  const [contractData, setContractData] = useState<ContractDetailData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>();

  // 模拟数据获取函数
  const fetchContractDetail = useCallback(async (id: string): Promise<void> => {
    setLoading(true);
    setError(undefined);

    try {
      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 模拟合同详情数据
      const mockDetailData: ContractDetailData = {
        id: id,
        序号: 1,
        合同编号: "HT2025051800TSC",
        合同名称: "2025年四川省大府易享享数据平台合同",
        所属商机项目: "2025年四川省大府易享享数据平台商机项目",
        合同类型: "开发合同",
        合同税率: "13%",
        合同签约金额: 4.0,
        合同收入金额: 3539.82,
        甲方名称: "成都大数据中心",
        商机项目是否超出合同约定部分: "否",
        销售负责人: "李明",
        合同状态: "通过评审",

        // 合同周期信息
        合同开始日期: "2025-04-15",
        合同结束日期: "2025-05-22",
        结算方式: "分阶段结算",
        交付条件: "按阶段交付",

        // 分阶段回款数据
        分阶段回款列表: [
          {
            id: "1",
            阶段名称: "第一阶段（技术方案）",
            合同内容: "明确业务目标和技术方案",
            回款比例: "60%",
            交付条件: "确数据需求基础，条建采购计算机和存储的需求",
            实施内容和交付成果: "合同签约后结算，乙方提供服务，甲方在确收到相关服务后进行结算",
          },
          {
            id: "2",
            阶段名称: "第二阶段（实施验收第2阶段）",
            合同内容: "完成环境搭建并进行系统测试整合",
            回款比例: "30%",
            交付条件: "确数据需求基础，条建采购计算机和存储的需求",
            实施内容和交付成果: "乙方最后结算通过第二阶段验收，甲方在确收到相关服务后进行结算",
          },
          {
            id: "3",
            阶段名称: "第三阶段（验收期及服务期间）",
            合同内容: "完成最终验收并提供后续技术支持",
            回款比例: "10%",
            交付条件: "确数据需求基础，条建采购计算机和存储的需求",
            实施内容和交付成果: "乙方三个月正常运行期间通过验收，甲方在确收到相关服务后进行结算",
          },
        ] as StagePaymentItem[],

        // 项目回款明细数据
        项目回款明细列表: [
          {
            id: "1",
            回款时间: "2025-04-15",
            回款金额: 120,
            备注: "第一笔回款收款",
          },
          {
            id: "2",
            回款时间: "2025-05-10",
            回款金额: 80,
            备注: "分阶段确认回款并按2",
          },
          {
            id: "3",
            回款时间: "2025-05-22",
            回款金额: 100,
            备注: "最后阶段收款",
          },
        ] as PaymentDetailItem[],

        // 甲方信息
        甲方信息: {
          甲方名称: "成都大数据中心",
          甲方联系人: "李明",
          甲方联系人电话: "13612345678",
          甲方联系人邮箱: "<EMAIL>",
        },

        // 乙方信息
        乙方信息: {
          乙方名称: "上海合创属成服务有限公司",
          乙方联系人: "张伟",
          乙方联系人电话: "13987654321",
          乙方联系人邮箱: "<EMAIL>",
        },

        // 知识产权及其他条款
        知识产权及其他条款: `本产权归甲方享有协议开发成果应充满办技术方案的全部知识产权，乙方在提案服务过程中，使用甲方现有资源的，乙方应当严格保密，不得用于其他场所。
XXXX版。`,

        // 违约条款
        违约条款及违约: `如果一方未能按照约定履行本合同义务，违方须进行相应的赔付工作，受方有权要求违约方承担相应的违约责任。各项涉及的违约责任，合同双方将在合作前详细约定。
XXXX版。`,
      };

      setContractData(mockDetailData);
    } catch (err) {
      setError("获取合同详情失败，请稍后重试");
      console.error("获取合同详情失败:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 页面初始化时获取数据
  useEffect(() => {
    if (contractId) {
      fetchContractDetail(contractId);
    } else {
      setError("缺少合同ID参数");
    }
  }, [contractId, fetchContractDetail]);

  // 返回按钮处理
  const handleBack = useCallback((): void => {
    navigate("/contract-management");
  }, [navigate]);

  // 编辑按钮处理
  const handleEdit = useCallback((id: string): void => {
    console.log("编辑合同:", id);
    // 这里可以跳转到编辑页面
    // navigate(`/contract-management/edit/${id}`);
  }, []);

  // 刷新按钮处理
  const handleRefresh = useCallback((): void => {
    if (contractId) {
      fetchContractDetail(contractId);
    }
  }, [contractId, fetchContractDetail]);

  // 如果没有数据且不在加载中，显示错误信息
  if (!contractData && !loading && !error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <p className="mb-4 text-gray-500">未找到合同信息</p>
          <button
            onClick={handleBack}
            className="px-4 py-2 text-white bg-blue-500 rounded transition-colors hover:bg-blue-600"
          >
            返回列表
          </button>
        </div>
      </div>
    );
  }

  return (
    <ContractDetail
      contractData={contractData}
      loading={loading}
      error={error}
      onBack={handleBack}
      onEdit={handleEdit}
      onRefresh={handleRefresh}
    />
  );
};

export default ContractDetailPage;
