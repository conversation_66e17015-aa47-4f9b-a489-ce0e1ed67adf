import React from 'react';
import { Card, Form, DatePicker, Input } from 'antd';
import moment from 'moment';
import 'moment/locale/zh-cn';
import type { ContractDetailData } from '../types';

// 设置 moment 中文 locale
moment.locale('zh-cn');

interface ContractPeriodEditableProps {
  contractData: ContractDetailData;
}

/**
 * 可编辑的合同周期组件
 * 
 * 修复说明：
 * 1. 使用 moment 替代 dayjs，确保与 antd 4.x 兼容
 * 2. 正确使用 getValueFromEvent 和 getValueProps 处理日期值转换：
 *    - getValueFromEvent: 将 moment 对象转换为字符串存储在 form 中
 *    - getValueProps: 将字符串转换为 moment 对象传递给 DatePicker
 * 3. 添加严格的值验证，确保日期格式正确
 * 4. 设置中文 locale 支持
 */
const ContractPeriodEditable: React.FC<ContractPeriodEditableProps> = React.memo(({ contractData }) => {
  return (
    <Card title="合同周期" className="shadow-sm">
      <div className="grid grid-cols-1 gap-4">
        <div className="grid grid-cols-2 gap-4">
          <Form.Item
            label="合同开始日期"
            name="合同开始日期"
            rules={[{ required: true, message: '请选择合同开始日期' }]}
            getValueFromEvent={(date) => {
              if (!date || !moment.isMoment(date)) return undefined;
              return date.format('YYYY-MM-DD');
            }}
            getValueProps={(value) => ({
              value: value && typeof value === 'string' && moment(value, 'YYYY-MM-DD', true).isValid() 
                ? moment(value, 'YYYY-MM-DD') 
                : undefined
            })}
          >
            <DatePicker 
              className="w-full" 
              placeholder="请选择开始日期"
              format="YYYY-MM-DD"
            />
          </Form.Item>

          <Form.Item
            label="合同结束日期"
            name="合同结束日期"
            rules={[{ required: true, message: '请选择合同结束日期' }]}
            getValueFromEvent={(date) => {
              if (!date || !moment.isMoment(date)) return undefined;
              return date.format('YYYY-MM-DD');
            }}
            getValueProps={(value) => ({
              value: value && typeof value === 'string' && moment(value, 'YYYY-MM-DD', true).isValid() 
                ? moment(value, 'YYYY-MM-DD') 
                : undefined
            })}
          >
            <DatePicker 
              className="w-full" 
              placeholder="请选择结束日期"
              format="YYYY-MM-DD"
            />
          </Form.Item>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Form.Item
            label="结算方式"
            name="结算方式"
            rules={[{ required: true, message: '请输入结算方式' }]}
          >
            <Input placeholder="请输入结算方式" />
          </Form.Item>

          <Form.Item
            label="交付条件"
            name="交付条件"
            rules={[{ required: true, message: '请输入交付条件' }]}
          >
            <Input placeholder="请输入交付条件" />
          </Form.Item>
        </div>
      </div>
    </Card>
  );
});

ContractPeriodEditable.displayName = 'ContractPeriodEditable';

export default ContractPeriodEditable;