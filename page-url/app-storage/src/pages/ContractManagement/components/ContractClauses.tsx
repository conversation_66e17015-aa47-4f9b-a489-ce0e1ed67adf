import React from 'react';
import { Card } from 'antd';

interface ContractClausesProps {
  title: string;
  content?: string;
}

/**
 * 合同条款组件
 */
const ContractClauses: React.FC<ContractClausesProps> = React.memo(({ title, content }) => {
  const getDefaultContent = (title: string): string => {
    if (title.includes('知识产权')) {
      return '本产权归甲方享有协议开发成果应充满办技术方案的全部知识产权，乙方在提案服务过程中，使用甲方现有资源的，乙方应当严格保密，不得用于其他场所。\nXXXX版。';
    }
    if (title.includes('违约')) {
      return '如果一方未能按照约定履行本合同义务，违方须进行相应的赔付工作，受方有权要求违约方承担相应的违约责任。各项涉及的违约责任，合同双方将在合作前详细约定。\nXXXX版。';
    }
    return '';
  };

  return (
    <Card title={title} className="shadow-sm">
      <div className="p-4 bg-gray-50 rounded border min-h-[100px]">
        <div className="text-sm text-gray-700 whitespace-pre-wrap">
          {content || getDefaultContent(title)}
        </div>
      </div>
    </Card>
  );
});

ContractClauses.displayName = 'ContractClauses';

export default ContractClauses; 