import React from 'react';
import { Card, Descriptions } from 'antd';
import type { ContractDetailData } from '../types';

interface ContractInfoProps {
  contractData: ContractDetailData;
}

/**
 * 合同信息组件
 */
const ContractInfo: React.FC<ContractInfoProps> = React.memo(({ contractData }) => {
  return (
    <Card title="合同信息" className="shadow-sm">
      <Descriptions column={1} bordered size="small" className="bg-white">
        <Descriptions.Item label="合同税率">
          {contractData.合同税率}
        </Descriptions.Item>
        <Descriptions.Item label="合同签约金额(万元)">
          {contractData.合同签约金额?.toFixed(3) || '0.000'}
        </Descriptions.Item>
        <Descriptions.Item label="签约最高金额(万元)">
          {contractData.合同签约金额?.toFixed(3) || '0.000'}
        </Descriptions.Item>
        <Descriptions.Item label="合同收入金额(万元)">
          {contractData.合同收入金额?.toFixed(2) || '0.00'}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
});

ContractInfo.displayName = 'ContractInfo';

export default ContractInfo; 