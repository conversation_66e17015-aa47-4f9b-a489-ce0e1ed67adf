import { ServiceScenario } from 'click-plugin-utils/components/casader-project/cascade.type';

/**
 * 模拟数据 - 基于UI截图内容
 */
export const mockServiceScenarios: ServiceScenario[] = [
  {
    id: 'scenario-1',
    name: '好办一件事',
    count: 8,
    selected: false,
    description: '"好办"或"一件事"是近年来政府政务服务领域推出的便民服务产品，旨在通过整合多部门、多事项，实现"一次申请、一套材料、一网通办、一窗受理"的一体化办理模式，为企业和群众提供更高效、便捷的政务服务。',
    requiredProducts: [
      {
        id: 'product-1-1',
        name: '智能构建系统/智能业务运营系统',
        description: '"高效办成一件事"智能构建系统，通过流程梳理、智能推荐与组件复用，实现政务服务事项快速配置、智能生成和一体化办理，全面提升办事效率上线效率与群众办事体验。',
        selected: false,
        disabled: false,
        operationalServices: [
          {
            id: 'service-1-1-1',
            type: '集成办服务',
            description: 'XXXXXXXX',
            unit: '主题',
            quantity: 3,
            editable: true
          },
          {
            id: 'service-1-1-2',
            type: '跨域办服务',
            description: 'XXXXXXXX',
            unit: '事项',
            quantity: 3,
            editable: true
          },
          {
            id: 'service-1-1-3',
            type: '免申办服务',
            description: 'XXXXXXXX',
            unit: '政策',
            quantity: 3,
            editable: true
          },
          {
            id: 'service-1-1-4',
            type: '承诺办服务',
            description: 'XXXXXXXX',
            unit: '事项',
            quantity: 3,
            editable: true
          },
          {
            id: 'service-1-1-5',
            type: '一类事一站办服务',
            description: 'XXXXXXXX',
            unit: '主题',
            quantity: 3,
            editable: true
          },
          {
            id: 'service-1-1-6',
            type: '一业一证服务',
            description: 'XXXXXXXX',
            unit: '主题',
            quantity: 3,
            editable: true
          },
          {
            id: 'service-1-1-7',
            type: '智慧好办服务',
            description: 'XXXXXXXX',
            unit: '事项',
            quantity: 3,
            editable: true
          },
          {
            id: 'service-1-1-8',
            type: '政策申报服务',
            description: 'XXXXXXXX',
            unit: '申报项目',
            quantity: 3,
            editable: true
          }
        ]
      }
    ],
    optionalProducts: [
      {
        id: 'optional-1-1',
        name: '材料预审服务系统',
        description: '此产品为可选项，适用于【需要xxx服务】的用户，如您处于【xxx情况】，建议勾选。',
        selected: false,
        operationalServices: [
          {
            id: 'service-opt-1-1-1',
            type: '材料预审模型',
            description: 'XXXXXXXX',
            unit: '模型数量',
            quantity: 1,
            editable: true
          }
        ]
      },
      {
        id: 'optional-1-2',
        name: '电子证照系统',
        description: '构建统一的电子证照库，支持证照在线申领、验证和使用，提升办事便民化水平。',
        selected: false,
        operationalServices: [
          {
            id: 'service-opt-1-2-1',
            type: '证照管理服务',
            description: 'XXXXXXXX',
            unit: '证照类型',
            quantity: 10,
            editable: true
          },
          {
            id: 'service-opt-1-2-2',
            type: '证照验证服务',
            description: 'XXXXXXXX',
            unit: '验证次数',
            quantity: 1000,
            editable: true
          }
        ]
      },
      {
        id: 'optional-1-3',
        name: '智能客服系统',
        description: '基于AI技术的智能客服平台，提供7x24小时在线咨询服务，支持多轮对话和知识库管理。',
        selected: false,
        operationalServices: [
          {
            id: 'service-opt-1-3-1',
            type: '智能问答服务',
            description: 'XXXXXXXX',
            unit: '问答轮次',
            quantity: 1000,
            editable: true
          }
        ]
      }
    ]
  },
  {
    id: 'scenario-2',
    name: '智能服务中枢',
    count: 2,
    selected: false,
    description: '智能服务中枢是政务服务的核心平台，提供统一的服务入口和智能化的服务推荐。',
    requiredProducts: [
      {
        id: 'product-2-1',
        name: '用户行为调度系统',
        description: '用户行为库通过采集用户在政务服务平台上的行为数据，构建用户行为画像，支持精细化服务推荐、智能分析与产品优化决策，推动政务服务从"可用"向"好用、易用、智能化"演进。',
        selected: false,
        disabled: false,
        operationalServices: [] // 暂无运营服务
      },
      {
        id: 'product-2-2', 
        name: '智能问答系统',
        description: '智能图谱系统构建政务数据实体之间的知识图谱，融合政策、事项、材料、审批等多维度关系，支持语义理解与智能检索，提升智能客服、审批辅助与业务分析等场景的智能服务能力。',
        selected: false,
        disabled: false,
        operationalServices: [
          {
            id: 'service-2-2-1',
            type: '事项数据梳理服务',
            description: 'XXXXXXXX',
            unit: '业务办理项',
            quantity: 3,
            editable: true
          },
          {
            id: 'service-2-2-2',
            type: '办事指南校验服务',
            description: 'XXXXXXXX',
            unit: '业务办理项',
            quantity: 3,
            editable: true
          }
        ]
      }
    ],
    optionalProducts: [
      {
        id: 'optional-2-1',
        name: '智能推荐引擎',
        description: '基于用户行为和业务场景，提供个性化服务推荐，提升用户办事效率和满意度。',
        selected: false,
        operationalServices: [
          {
            id: 'service-opt-2-1-1',
            type: '推荐算法优化',
            description: 'XXXXXXXX',
            unit: '算法模型',
            quantity: 1,
            editable: true
          }
        ]
      },
      {
        id: 'optional-2-2',
        name: '多媒体交互系统',
        description: '支持语音、视频等多种交互方式，为用户提供更加便捷的服务体验。',
        selected: false,
        operationalServices: [
          {
            id: 'service-opt-2-2-1',
            type: '语音识别服务',
            description: 'XXXXXXXX',
            unit: '识别时长',
            quantity: 100,
            editable: true
          }
        ]
      }
    ]
  },
  {
    id: 'scenario-3',
    name: '事项梳理',
    count: 1,
    selected: false,
    description: '事项梳理服务帮助政府部门规范和优化政务服务事项。',
    requiredProducts: [],
    optionalProducts: [
      {
        id: 'optional-3-1',
        name: '流程建模工具',
        description: '可视化流程建模工具，支持事项流程的设计、优化和管理。',
        selected: false,
        operationalServices: [
          {
            id: 'service-opt-3-1-1',
            type: '流程设计服务',
            description: 'XXXXXXXX',
            unit: '流程数量',
            quantity: 5,
            editable: true
          }
        ]
      }
    ]
  },
  {
    id: 'scenario-4',
    name: '办事指南校验',
    count: 1,
    selected: false,
    description: '办事指南校验服务确保政务服务指南的准确性和完整性。',
    requiredProducts: [],
    optionalProducts: [
      {
        id: 'optional-4-1',
        name: '内容管理系统',
        description: '统一的内容管理平台，支持办事指南的创建、编辑、审核和发布。',
        selected: false,
        operationalServices: [
          {
            id: 'service-opt-4-1-1',
            type: '内容审核服务',
            description: 'XXXXXXXX',
            unit: '审核次数',
            quantity: 50,
            editable: true
          }
        ]
      }
    ]
  },
  {
    id: 'scenario-5',
    name: '材料预审',
    count: 4,
    selected: false,
    description: '材料预审服务提供智能化的材料审核和预处理功能。',
    requiredProducts: [],
    optionalProducts: [
      {
        id: 'optional-5-1',
        name: 'OCR识别系统',
        description: '智能文字识别系统，支持各类证件和文档的自动识别和信息提取。',
        selected: false,
        operationalServices: [
          {
            id: 'service-opt-5-1-1',
            type: 'OCR识别服务',
            description: 'XXXXXXXX',
            unit: '识别次数',
            quantity: 1000,
            editable: true
          }
        ]
      },
      {
        id: 'optional-5-2',
        name: '图像处理平台',
        description: '提供图像增强、格式转换等功能，确保材料的清晰度和规范性。',
        selected: false,
        operationalServices: [
          {
            id: 'service-opt-5-2-1',
            type: '图像处理服务',
            description: 'XXXXXXXX',
            unit: '处理次数',
            quantity: 500,
            editable: true
          }
        ]
      }
    ]
  }
];
