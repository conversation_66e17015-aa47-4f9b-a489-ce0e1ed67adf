import React from 'react';
import { Spin, Button } from 'antd';

interface LoadingDisplayProps {
  loading: boolean;
  onBack?: () => void;
}

/**
 * 加载显示组件
 */
const LoadingDisplay: React.FC<LoadingDisplayProps> = React.memo(({ loading, onBack }) => {
  return (
    <div className="p-6 min-h-screen bg-gray-50">
      <Spin spinning={loading}>
        <div className="flex justify-center items-center min-h-96">
          <div className="text-center">
            <p className="mb-4 text-gray-500">正在加载合同信息...</p>
            {onBack && (
              <Button onClick={onBack}>
                返回列表
              </Button>
            )}
          </div>
        </div>
      </Spin>
    </div>
  );
});

LoadingDisplay.displayName = 'LoadingDisplay';

export default LoadingDisplay; 