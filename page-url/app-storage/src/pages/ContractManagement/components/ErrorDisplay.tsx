import React from 'react';
import { <PERSON>, <PERSON><PERSON>, Button } from 'antd';

interface ErrorDisplayProps {
  error: string;
  onRefresh?: () => void;
}

/**
 * 错误显示组件
 */
const ErrorDisplay: React.FC<ErrorDisplayProps> = React.memo(({ error, onRefresh }) => {
  return (
    <div className="p-6 min-h-screen bg-gray-50">
      <Card className="mx-auto max-w-4xl">
        <Alert
          message="加载失败"
          description={error}
          type="error"
          action={
            onRefresh && (
              <Button type="primary" onClick={onRefresh}>
                重新加载
              </Button>
            )
          }
        />
      </Card>
    </div>
  );
});

ErrorDisplay.displayName = 'ErrorDisplay';

export default ErrorDisplay; 