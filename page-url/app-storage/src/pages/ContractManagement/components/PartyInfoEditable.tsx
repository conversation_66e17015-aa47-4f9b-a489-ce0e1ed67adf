import React from 'react';
import { Card, Form, Input } from 'antd';

interface PartyInfoData {
  甲方名称?: string;
  甲方联系人?: string;
  甲方联系人电话?: string;
  甲方联系人邮箱?: string;
  乙方名称?: string;
  乙方联系人?: string;
  乙方联系人电话?: string;
  乙方联系人邮箱?: string;
}

interface PartyInfoEditableProps {
  title: string;
  partyInfo: PartyInfoData;
  isPartyA: boolean;
}

/**
 * 可编辑的甲乙方信息组件
 */
const PartyInfoEditable: React.FC<PartyInfoEditableProps> = React.memo(({ title, partyInfo, isPartyA }) => {
  const prefix = isPartyA ? '甲方' : '乙方';
  
  return (
    <Card title={title} className="shadow-sm">
      <div className="grid grid-cols-1 gap-4">
        <Form.Item
          label={`${prefix}名称`}
          name={[`${prefix}信息`, `${prefix}名称`]}
          rules={[{ required: true, message: `请输入${prefix}名称` }]}
        >
          <Input placeholder={`请输入${prefix}名称`} />
        </Form.Item>

        <Form.Item
          label={`${prefix}联系人`}
          name={[`${prefix}信息`, `${prefix}联系人`]}
          rules={[{ required: true, message: `请输入${prefix}联系人` }]}
        >
          <Input placeholder={`请输入${prefix}联系人`} />
        </Form.Item>

        <Form.Item
          label={`${prefix}联系人电话`}
          name={[`${prefix}信息`, `${prefix}联系人电话`]}
          rules={[
            { required: true, message: `请输入${prefix}联系人电话` },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
          ]}
        >
          <Input placeholder={`请输入${prefix}联系人电话`} />
        </Form.Item>

        <Form.Item
          label={`${prefix}联系人邮箱`}
          name={[`${prefix}信息`, `${prefix}联系人邮箱`]}
          rules={[
            { required: true, message: `请输入${prefix}联系人邮箱` },
            { type: 'email', message: '请输入正确的邮箱地址' }
          ]}
        >
          <Input placeholder={`请输入${prefix}联系人邮箱`} />
        </Form.Item>
      </div>
    </Card>
  );
});

PartyInfoEditable.displayName = 'PartyInfoEditable';

export default PartyInfoEditable; 