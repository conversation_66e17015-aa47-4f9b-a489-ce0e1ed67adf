import React from "react";
import { Card, Descriptions } from "antd";

interface PartyInfoData {
  甲方名称?: string;
  甲方联系人?: string;
  甲方联系人电话?: string;
  甲方联系人邮箱?: string;
  乙方名称?: string;
  乙方联系人?: string;
  乙方联系人电话?: string;
  乙方联系人邮箱?: string;
}

interface PartyInfoProps {
  title: string;
  partyInfo: PartyInfoData;
}

/**
 * 甲乙方信息组件
 */
const PartyInfo: React.FC<PartyInfoProps> = React.memo(
  ({ title, partyInfo }) => {
    const isPartyA = title.includes("甲方");

    return (
      <Card title={title} className="shadow-sm">
        <Descriptions column={1} bordered size="small" className="bg-white">
          <Descriptions.Item label={`${isPartyA ? "甲方" : "乙方"}名称`}>
            {isPartyA ? partyInfo.甲方名称 : partyInfo.乙方名称}
          </Descriptions.Item>
          <Descriptions.Item label={`${isPartyA ? "甲方" : "乙方"}联系人`}>
            {isPartyA ? partyInfo.甲方联系人 : partyInfo.乙方联系人}
          </Descriptions.Item>
          <Descriptions.Item label={`${isPartyA ? "甲方" : "乙方"}联系人电话`}>
            {isPartyA ? partyInfo.甲方联系人电话 : partyInfo.乙方联系人电话}
          </Descriptions.Item>
          <Descriptions.Item label={`${isPartyA ? "甲方" : "乙方"}联系人邮箱`}>
            {isPartyA ? partyInfo.甲方联系人邮箱 : partyInfo.乙方联系人邮箱}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    );
  }
);

PartyInfo.displayName = "PartyInfo";

export default PartyInfo;
