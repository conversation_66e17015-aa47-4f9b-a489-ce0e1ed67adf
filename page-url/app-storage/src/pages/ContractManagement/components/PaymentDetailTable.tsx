import React, { useMemo } from 'react';
import { Card, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import type { PaymentDetailItem } from '../types';

interface PaymentDetailTableProps {
  paymentDetailList: PaymentDetailItem[];
}

/**
 * 项目回款明细表格组件
 */
const PaymentDetailTable: React.FC<PaymentDetailTableProps> = React.memo(({ paymentDetailList }) => {
  // 项目回款明细表格列配置
  const columns: ColumnsType<PaymentDetailItem> = useMemo(() => [
    {
      title: '回款时间',
      dataIndex: '回款时间',
      key: 'paymentTime',
      width: 150,
      align: 'center',
    },
    {
      title: '回款金额(万元)',
      dataIndex: '回款金额',
      key: 'paymentAmount',
      width: 150,
      align: 'right',
      render: (amount: number) => amount?.toFixed(2) || '0.00',
    },
    {
      title: '备注',
      dataIndex: '备注',
      key: 'remark',
      ellipsis: true,
    },
  ], []);

  return (
    <Card title="项目回款明细" className="shadow-sm">
      <Table<PaymentDetailItem>
        columns={columns}
        dataSource={paymentDetailList}
        rowKey="id"
        pagination={false}
        size="small"
        bordered
        className="bg-white"
      />
    </Card>
  );
});

PaymentDetailTable.displayName = 'PaymentDetailTable';

export default PaymentDetailTable; 