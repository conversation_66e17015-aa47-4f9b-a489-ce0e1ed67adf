import React, { useMemo, useCallback } from 'react';
import { Table, Tabs, Button, Tag, Space, Card } from 'antd';
import { EyeOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import { ContractManagementProps, ContractItem, TabKey } from '../types';

const { TabPane } = Tabs;

/**
 * 合同管理组件
 * 严格遵循数据解耦原则，所有数据通过props传入
 */
const Contract: React.FC<ContractManagementProps> = React.memo(({
  incomeContracts,
  costContracts,
  loading,
  error,
  currentPage,
  pageSize,
  total,
  onTabChange,
  onPageChange,
  onView,
  onConfirmSign,
  onSearch,
  onRefresh
}) => {
  const navigate = useNavigate();
  
  // 当前活跃的tab
  const [activeTab, setActiveTab] = React.useState<string>(TabKey.INCOME);

  // 处理tab切换
  const handleTabChange = useCallback((key: string) => {
    setActiveTab(key);
    onTabChange(key);
  }, [onTabChange]);

  // 处理查看操作 - 跳转到详情页面
  const handleView = useCallback((contractId: string) => {
    navigate(`/contract-management/${contractId}`);
  }, [navigate]);

  // 渲染操作按钮
  const renderActions = useCallback((record: ContractItem) => (
    <Space size="small">
      <Button
        type="link"
        size="small"
        icon={<EyeOutlined />}
        onClick={() => handleView(record.id)}
        className="text-blue-600 hover:text-blue-800"
      >
        查看
      </Button>
      <Button
        type="link"
        size="small"
        icon={<CheckCircleOutlined />}
        onClick={() => onConfirmSign(record.id)}
        className="text-green-600 hover:text-green-800"
      >
        确认签约
      </Button>
    </Space>
  ), [handleView, onConfirmSign]);

  // 渲染合同状态标签
  const renderStatus = useCallback((status: string) => {
    const statusColors: Record<string, string> = {
      '草稿': 'default',
      '待审核': 'processing',
      '已通过': 'success',
      '已拒绝': 'error',
      '已签约': 'success',
      '通过评审': 'success',
      '确认签约': 'success'
    };

    return (
      <Tag color={statusColors[status] || 'default'}>
        {status}
      </Tag>
    );
  }, []);

  // 渲染是否超出合同约定部分
  const renderExceedStatus = useCallback((exceed: string) => {
    return (
      <Tag color={exceed === '是' ? 'red' : 'green'}>
        {exceed}
      </Tag>
    );
  }, []);

  // 表格列配置
  const columns: ColumnsType<ContractItem> = useMemo(() => [
    {
      title: '序号',
      dataIndex: '序号',
      key: 'index',
      width: 80,
      align: 'center',
    },
    {
      title: '合同编号',
      dataIndex: '合同编号',
      key: 'contractNumber',
      width: 160,
      ellipsis: true,
    },
    {
      title: '合同名称',
      dataIndex: '合同名称',
      key: 'contractName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '所属商机项目',
      dataIndex: '所属商机项目',
      key: 'opportunityProject',
      width: 200,
      ellipsis: true,
    },
    {
      title: '合同类型',
      dataIndex: '合同类型',
      key: 'contractType',
      width: 120,
      align: 'center',
    },
    {
      title: '合同税率',
      dataIndex: '合同税率',
      key: 'taxRate',
      width: 100,
      align: 'center',
    },
    {
      title: '合同签约金额(万元)',
      dataIndex: '合同签约金额',
      key: 'signAmount',
      width: 140,
      align: 'right',
      render: (amount: number) => amount?.toFixed(2) || '0.00',
    },
    {
      title: '合同收入金额(万元)',
      dataIndex: '合同收入金额',
      key: 'incomeAmount',
      width: 140,
      align: 'right',
      render: (amount: number) => amount?.toFixed(2) || '0.00',
    },
    {
      title: '甲方名称',
      dataIndex: '甲方名称',
      key: 'partyAName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '商机项目是否超出合同约定部分',
      dataIndex: '商机项目是否超出合同约定部分',
      key: 'isExceed',
      width: 180,
      align: 'center',
      render: renderExceedStatus,
    },
    {
      title: '销售负责人',
      dataIndex: '销售负责人',
      key: 'salesperson',
      width: 120,
      align: 'center',
    },
    {
      title: '合同状态',
      dataIndex: '合同状态',
      key: 'status',
      width: 100,
      align: 'center',
      render: renderStatus,
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      align: 'center',
      fixed: 'right',
      render: (_, record) => renderActions(record),
    },
  ], [renderActions, renderStatus, renderExceedStatus]);

  // 获取当前显示的数据
  const currentData = useMemo(() => {
    return activeTab === TabKey.INCOME ? incomeContracts : costContracts;
  }, [activeTab, incomeContracts, costContracts]);

  // 错误状态处理
  if (error) {
    return (
      <Card className="m-6">
        <div className="py-8 text-center text-red-500">
          <p>加载失败: {error}</p>
          {onRefresh && (
            <Button type="primary" onClick={onRefresh} className="mt-4">
              重新加载
            </Button>
          )}
        </div>
      </Card>
    );
  }

  return (
    <Card className="m-6" title={`合同列表 (${total})`}>
      <Tabs 
        activeKey={activeTab}
        onChange={handleTabChange}
        className="mb-4"
      >
        <TabPane tab="收入合同" key={TabKey.INCOME} />
        <TabPane tab="成本合同" key={TabKey.COST} />
      </Tabs>

      <Table<ContractItem>
        columns={columns}
        dataSource={currentData}
        loading={loading}
        rowKey="id"
        scroll={{ x: 1600 }}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
          onChange: onPageChange,
          onShowSizeChange: onPageChange,
        }}
        size="small"
        bordered
        className="bg-white"
      />
    </Card>
  );
});

Contract.displayName = 'Contract';

export default Contract;