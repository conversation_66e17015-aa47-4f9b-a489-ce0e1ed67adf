import React from 'react';
import { Card, Form, Input, InputNumber } from 'antd';
import type { ContractDetailData } from '../types';

interface ContractInfoEditableProps {
  contractData: ContractDetailData;
}

/**
 * 可编辑的合同信息组件
 */
const ContractInfoEditable: React.FC<ContractInfoEditableProps> = React.memo(({ contractData }) => {
  return (
    <Card title="合同信息" className="shadow-sm">
      <div className="grid grid-cols-1 gap-4">
        <Form.Item
          label="合同税率"
          name="合同税率"
          rules={[{ required: true, message: '请输入合同税率' }]}
        >
          <Input placeholder="请输入合同税率" />
        </Form.Item>

        <Form.Item
          label="合同签约金额(万元)"
          name="合同签约金额"
          rules={[{ required: true, message: '请输入合同签约金额' }]}
        >
          <InputNumber
            className="w-full"
            placeholder="请输入合同签约金额"
            precision={3}
            min={0}
          />
        </Form.Item>

        <Form.Item
          label="合同收入金额(万元)"
          name="合同收入金额"
          rules={[{ required: true, message: '请输入合同收入金额' }]}
        >
          <InputNumber
            className="w-full"
            placeholder="请输入合同收入金额"
            precision={2}
            min={0}
          />
        </Form.Item>
      </div>
    </Card>
  );
});

ContractInfoEditable.displayName = 'ContractInfoEditable';

export default ContractInfoEditable; 