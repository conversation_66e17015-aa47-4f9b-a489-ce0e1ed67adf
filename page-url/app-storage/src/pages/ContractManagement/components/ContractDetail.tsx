import React from "react";
import { Spin } from "antd";
import { EditOutlined, ReloadOutlined } from "@ant-design/icons";
import { PageLayout } from "@/components";
import type { ContractDetailProps } from "../types";
import type { HeaderAction } from "@/components";
import {
  ContractBasicInfo,
  ContractInfo,
  ContractPeriod,
  StagePaymentTable,
  PaymentDetailTable,
  PartyInfo,
  ContractClauses,
  ProductServiceTable,
  ErrorDisplay,
  LoadingDisplay,
} from "./";

/**
 * 合同详情组件
 * 展示完整的合同信息，包括基本信息、合同信息、分阶段回款、项目回款明细等
 */
const ContractDetail: React.FC<ContractDetailProps> = React.memo(
  ({ contractData, loading, error, onBack, onEdit, onRefresh }) => {
    // 错误状态处理
    if (error) {
      return <ErrorDisplay error={error} onRefresh={onRefresh} />;
    }

    // 数据为空时的处理
    if (!contractData) {
      return <LoadingDisplay loading={loading} onBack={onBack} />;
    }

    // 配置header操作按钮
    const headerActions: HeaderAction[] = [
      ...(onRefresh ? [{
        key: 'refresh',
        label: '刷新',
        icon: <ReloadOutlined />,
        type: 'text' as const,
        onClick: onRefresh,
      }] : []),
      ...(onEdit ? [{
        key: 'edit',
        label: '编辑',
        icon: <EditOutlined />,
        type: 'primary' as const,
        onClick: () => onEdit(contractData.id),
      }] : []),
    ];

    return (
      <PageLayout
        title="合同详情"
        showBack={!!onBack}
        onBack={onBack}
        actions={headerActions}
        fixed={true}
        contentClassName="space-y-6"
      >
        <Spin spinning={loading}>
          {/* 合同基本信息 */}
          <ContractBasicInfo contractData={contractData} />

          {/* 合同信息 */}
          <ContractInfo contractData={contractData} />

          {/* 合同周期 */}
          <ContractPeriod contractData={contractData} />

          {/* 分阶段回款 */}
          <StagePaymentTable stagePaymentList={contractData.分阶段回款列表} />

          {/* 项目回款明细 */}
          <PaymentDetailTable
            paymentDetailList={contractData.项目回款明细列表}
          />

          {/* 甲方信息 */}
          <PartyInfo title="甲方信息" partyInfo={contractData.甲方信息} />

          {/* 乙方信息 */}
          <PartyInfo title="乙方信息" partyInfo={contractData.乙方信息} />

          {/* 知识产权及其他条款 */}
          <ContractClauses
            title="知识产权及其他条款"
            content={contractData.知识产权及其他条款}
          />

          {/* 违约条款及违约 */}
          <ContractClauses
            title="违约条款及违约"
            content={contractData.违约条款及违约}
          />

          {/* 产品服务 */}
          <ProductServiceTable />
        </Spin>
      </PageLayout>
    );
  }
);

ContractDetail.displayName = "ContractDetail";

export default ContractDetail;
