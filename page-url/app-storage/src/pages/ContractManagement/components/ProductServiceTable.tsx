import React, { useMemo } from 'react';
import { Card, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';

/**
 * 产品服务数据接口
 */
interface ProductServiceItem {
  key: string;
  productName: string;
  businessScope: string;
  measurementMethod: string;
  implementationContent: string;
  quantity: number;
  status: string;
}

/**
 * 产品服务表格组件
 */
const ProductServiceTable: React.FC = React.memo(() => {
  // 产品服务表格列配置
  const columns: ColumnsType<ProductServiceItem> = useMemo(() => [
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '业务范围',
      dataIndex: 'businessScope',
      key: 'businessScope',
      width: 150,
      ellipsis: true,
    },
    {
      title: '实现内容情况',
      dataIndex: 'measurementMethod',
      key: 'measurementMethod',
      width: 150,
      ellipsis: true,
    },
    {
      title: '实施内容情况',
      dataIndex: 'implementationContent',
      key: 'implementationContent',
      width: 200,
      ellipsis: true,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      align: 'center',
    },
    {
      title: '状态选项及实现情况',
      dataIndex: 'status',
      key: 'status',
      width: 150,
      ellipsis: true,
    },
  ], []);

  // 模拟产品服务数据
  const productServiceData: ProductServiceItem[] = useMemo(() => [
    {
      key: '1',
      productName: '智能化中台产品理解执行平台',
      businessScope: '互联网服务',
      measurementMethod: 'XXXX套件',
      implementationContent: 'XXXX套件',
      quantity: 1,
      status: '可用'
    },
    {
      key: '2',
      productName: '智能投数据化理管平台',
      businessScope: '互联网服务',
      measurementMethod: 'XXXX套件',
      implementationContent: 'XXXX套件',
      quantity: 1,
      status: '可用'
    },
    {
      key: '3',
      productName: '智能化运营服务平台',
      businessScope: '互联网服务',
      measurementMethod: 'XXXX套件',
      implementationContent: 'XXXX套件',
      quantity: 1,
      status: '可用'
    },
    {
      key: '4',
      productName: '智能化中台产品理解数字平台',
      businessScope: '互联网服务',
      measurementMethod: 'XXXX套件',
      implementationContent: 'XXXX套件',
      quantity: 1,
      status: '可用'
    },
    {
      key: '5',
      productName: '智能化中台产品理解及定平台',
      businessScope: '互联网服务',
      measurementMethod: '一次一次数据服务',
      implementationContent: 'XXXX套件',
      quantity: 1,
      status: '可用'
    },
    {
      key: '6',
      productName: '智能化中台理管数据服务管平台',
      businessScope: '互联网服务',
      measurementMethod: '一个一次数据服务',
      implementationContent: 'XXXX套件',
      quantity: 1,
      status: '可用'
    },
    {
      key: '7',
      productName: '智能化运营管理数据管理平台',
      businessScope: '互联网服务',
      measurementMethod: '数字运营数据系平台',
      implementationContent: 'XXXX套件',
      quantity: 1,
      status: '可用'
    },
  ], []);

  return (
    <Card title="产品服务" className="shadow-sm">
      <Table<ProductServiceItem>
        columns={columns}
        dataSource={productServiceData}
        rowKey="key"
        pagination={false}
        size="small"
        bordered
        scroll={{ x: 1200 }}
        className="bg-white"
      />
    </Card>
  );
});

ProductServiceTable.displayName = 'ProductServiceTable';

export default ProductServiceTable; 