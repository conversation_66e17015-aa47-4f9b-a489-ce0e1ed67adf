import React from 'react';
import { Card, Descriptions } from 'antd';
import type { ContractDetailData } from '../types';

interface ContractPeriodProps {
  contractData: ContractDetailData;
}

/**
 * 合同周期组件
 */
const ContractPeriod: React.FC<ContractPeriodProps> = React.memo(({ contractData }) => {
  return (
    <Card title="合同周期" className="shadow-sm">
      <Descriptions column={1} bordered size="small" className="bg-white">
        <Descriptions.Item label="整体周期">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center">
              <span className="w-20 text-gray-600">合同开始：</span>
              <span className="flex-1 px-2 py-1 text-sm border-b border-gray-300">
                {contractData.合同开始日期 || ''}
              </span>
            </div>
            <div className="flex items-center">
              <span className="w-12 text-gray-600">至</span>
              <span className="flex-1 px-2 py-1 text-sm border-b border-gray-300">
                {contractData.合同结束日期 || ''}
              </span>
            </div>
          </div>
        </Descriptions.Item>
        <Descriptions.Item label="合同期间">
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center">
              <div className="mb-2 text-xs text-gray-500">开始日期</div>
              <div className="py-1 text-sm border-b border-gray-300">
                {contractData.合同开始日期 || ''}
              </div>
            </div>
            <div className="text-center">
              <div className="mb-2 text-xs text-gray-500">结束日期</div>
              <div className="py-1 text-sm border-b border-gray-300">
                {contractData.合同结束日期 || ''}
              </div>
            </div>
            <div className="text-center">
              <div className="mb-2 text-xs text-gray-500">结算方式</div>
              <div className="py-1 text-sm border-b border-gray-300">
                {contractData.结算方式 || ''}
              </div>
            </div>
            <div className="text-center">
              <div className="mb-2 text-xs text-gray-500">交付条件</div>
              <div className="py-1 text-sm border-b border-gray-300">
                {contractData.交付条件 || ''}
              </div>
            </div>
          </div>
        </Descriptions.Item>
        <Descriptions.Item label="回款条件">
          <div className="grid grid-cols-4 gap-2 text-center">
            <div className="p-2 bg-gray-50 rounded border border-gray-200">
              <div className="mb-1 text-xs text-gray-500">付款方式</div>
              <div className="text-xs">{contractData.结算方式 || ''}</div>
            </div>
            <div className="p-2 bg-gray-50 rounded border border-gray-200">
              <div className="mb-1 text-xs text-gray-500">付款比例</div>
              <div className="text-xs">60%</div>
            </div>
            <div className="p-2 bg-gray-50 rounded border border-gray-200">
              <div className="mb-1 text-xs text-gray-500">付款时间</div>
              <div className="text-xs">按阶段付款</div>
            </div>
            <div className="p-2 bg-gray-50 rounded border border-gray-200">
              <div className="mb-1 text-xs text-gray-500">交付条件</div>
              <div className="text-xs">{contractData.交付条件 || ''}</div>
            </div>
          </div>
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
});

ContractPeriod.displayName = 'ContractPeriod';

export default ContractPeriod; 