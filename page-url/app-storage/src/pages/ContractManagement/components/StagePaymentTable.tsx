import React, { useMemo } from 'react';
import { Card, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import type { StagePaymentItem } from '../types';

interface StagePaymentTableProps {
  stagePaymentList: StagePaymentItem[];
}

/**
 * 分阶段回款表格组件
 */
const StagePaymentTable: React.FC<StagePaymentTableProps> = React.memo(({ stagePaymentList }) => {
  // 分阶段回款表格列配置
  const columns: ColumnsType<StagePaymentItem> = useMemo(() => [
    {
      title: '阶段名称',
      dataIndex: '阶段名称',
      key: 'stageName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '合同内容',
      dataIndex: '合同内容',
      key: 'contractContent',
      width: 200,
      ellipsis: true,
    },
    {
      title: '回款比例',
      dataIndex: '回款比例',
      key: 'paymentRatio',
      width: 100,
      align: 'center',
    },
    {
      title: '交付条件',
      dataIndex: '交付条件',
      key: 'deliveryCondition',
      width: 200,
      ellipsis: true,
    },
    {
      title: '实施内容和交付成果',
      dataIndex: '实施内容和交付成果',
      key: 'implementationContent',
      ellipsis: true,
    },
  ], []);

  return (
    <Card title="分阶段回款" className="shadow-sm">
      <Table<StagePaymentItem>
        columns={columns}
        dataSource={stagePaymentList}
        rowKey="id"
        pagination={false}
        size="small"
        bordered
        scroll={{ x: 1000 }}
        className="bg-white"
      />
    </Card>
  );
});

StagePaymentTable.displayName = 'StagePaymentTable';

export default StagePaymentTable; 