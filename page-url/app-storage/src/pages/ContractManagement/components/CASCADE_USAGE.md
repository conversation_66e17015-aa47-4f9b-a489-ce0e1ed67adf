# 级联选择组件使用指南

## 概述

级联选择组件已成功移动到 `ContractManagement/components` 目录下，并完成了从CSS到Tailwind CSS的样式重构。

## 文件结构

```
page-url/app-storage/src/pages/ContractManagement/components/
├── Cascade.tsx                    # 主组件
├── ServiceScenarioList.tsx        # 服务场景列表
├── ProductDetails.tsx             # 产品详情
├── OperationalServiceTable.tsx    # 运营服务明细表格
├── ProjectBuildList.tsx           # 商机项目建设清单
├── CascadeDemo.tsx                # 演示页面
├── cascade.ts                     # 类型定义
├── cascadeUtils.ts                # 工具函数
├── mockData.ts                    # 模拟数据
└── index.ts                       # 导出文件
```

## 快速使用

### 1. 基本导入

```tsx
import { Cascade, mockServiceScenarios } from './components';
// 或者
import { Cascade } from './components/Cascade';
import { mockServiceScenarios } from './components/mockData';
```

### 2. 基本使用

```tsx
import React from 'react';
import { Cascade, mockServiceScenarios } from './components';

const MyPage: React.FC = () => {
  const handleSelectionChange = (selectedItems) => {
    console.log('选择变化:', selectedItems);
  };

  const handleQuantityChange = (itemId, quantity) => {
    console.log('数量变化:', { itemId, quantity });
  };

  return (
    <Cascade
      initialData={mockServiceScenarios}
      onSelectionChange={handleSelectionChange}
      onQuantityChange={handleQuantityChange}
    />
  );
};
```

### 3. 在现有页面中集成

```tsx
// 在 ContractManagement 页面中使用
import { CascadeDemo } from './components';

// 直接使用演示组件
<CascadeDemo />

// 或者自定义使用
import { Cascade, mockServiceScenarios } from './components';
<Cascade initialData={mockServiceScenarios} />
```

## 样式说明

- ✅ 所有组件已完全使用 Tailwind CSS 重构
- ✅ 删除了所有 `.css` 文件依赖
- ✅ 保持了原有的视觉效果和响应式设计
- ✅ 支持移动端和桌面端适配

## 核心功能

1. **级联选择机制**：勾选服务场景 → 必选产品自动勾选且禁用
2. **数据同步机制**：选择变化自动同步到商机项目建设清单
3. **数量编辑功能**：运营服务明细支持实时数量编辑
4. **双向数据绑定**：运营服务和建设清单数量实时同步

## 类型定义

所有类型定义都在 `cascade.ts` 中，包括：
- `ServiceScenario` - 服务场景
- `RequiredProduct` - 必选产品
- `OperationalService` - 运营服务
- `ProjectBuildItem` - 商机项目建设清单项
- `CascadeProps` - 主组件Props

## 工具函数

`cascadeUtils.ts` 提供了完整的业务逻辑工具函数：
- `generateProjectBuildItems` - 生成建设清单项
- `updateScenarioSelection` - 更新场景选择状态
- `generateCompleteProjectBuildList` - 生成完整建设清单
- `updateServiceQuantityAndSync` - 同步数量更新

## 演示

运行 `CascadeDemo` 组件可以查看完整的功能演示。

## 注意事项

1. 确保项目已配置 Tailwind CSS
2. 需要 Ant Design 组件库支持
3. 所有组件都是 TypeScript 编写，提供完整的类型安全
