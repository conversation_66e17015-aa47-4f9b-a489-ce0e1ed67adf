import React from 'react';
import { Card, Descriptions } from 'antd';
import type { ContractDetailData } from '../types';

interface ContractBasicInfoProps {
  contractData: ContractDetailData;
}

/**
 * 合同基本信息组件
 */
const ContractBasicInfo: React.FC<ContractBasicInfoProps> = React.memo(({ contractData }) => {
  return (
    <Card title="合同基本信息" className="shadow-sm">
      <Descriptions column={1} bordered size="small" className="bg-white">
        <Descriptions.Item label="合同名称">
          {contractData.合同名称}
        </Descriptions.Item>
        <Descriptions.Item label="合同编号">
          {contractData.合同编号}
        </Descriptions.Item>
        <Descriptions.Item label="所属商机项目">
          {contractData.所属商机项目}
        </Descriptions.Item>
        <Descriptions.Item label="所属项目小组成员">
          {contractData.所属商机项目}团队
        </Descriptions.Item>
        <Descriptions.Item label="合同类型">
          {contractData.合同类型}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
});

ContractBasicInfo.displayName = 'ContractBasicInfo';

export default ContractBasicInfo; 