import React, { useState, useCallback } from 'react';
import { Spin, Form, message } from 'antd';
import { SaveOutlined, CheckOutlined, ReloadOutlined } from '@ant-design/icons';
import { PageLayout } from '@/components';
import type { ContractConfirmProps, ContractDetailData } from '../types';
import type { HeaderAction } from '@/components';
import {
  ContractBasicInfo,
  ContractInfoEditable,
  ContractPeriodEditable,
  StagePaymentTable,
  PaymentDetailTable,
  PartyInfoEditable,
  ContractClauses,
  ProductServiceTable,
  ErrorDisplay,
  LoadingDisplay,
} from './';

/**
 * 确认签约组件
 * 展示合同信息并允许编辑部分字段
 */
const ContractConfirm: React.FC<ContractConfirmProps> = React.memo(({
  contractData,
  loading,
  saving,
  error,
  onBack,
  onSave,
  onConfirm,
  onRefresh
}) => {
  const [form] = Form.useForm();
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // 处理表单值变化
  const handleFormChange = useCallback(() => {
    setHasChanges(true);
  }, []);

  // 保存处理
  const handleSave = useCallback(async (): Promise<void> => {
    if (!contractData || !onSave) return;

    try {
      const formValues = await form.validateFields();
      
      // 合并表单数据到合同数据中
      const updatedData: ContractDetailData = {
        ...contractData,
        ...formValues,
      };

      await onSave(updatedData);
      setHasChanges(false);
    } catch (err) {
      console.error("表单验证失败:", err);
      message.error("请检查输入信息");
    }
  }, [contractData, form, onSave]);

  // 确认签约处理
  const handleConfirm = useCallback(async (): Promise<void> => {
    if (!contractData || !onConfirm) return;

    // 如果有未保存的更改，先保存
    if (hasChanges) {
      await handleSave();
    }

    await onConfirm();
  }, [contractData, hasChanges, handleSave, onConfirm]);

  // 错误状态处理
  if (error) {
    return <ErrorDisplay error={error} onRefresh={onRefresh} />;
  }

  // 数据为空时的处理
  if (!contractData) {
    return <LoadingDisplay loading={loading} onBack={onBack} />;
  }

  // 配置header操作按钮
  const headerActions: HeaderAction[] = [
    ...(onRefresh ? [{
      key: 'refresh',
      label: '刷新',
      icon: <ReloadOutlined />,
      type: 'text' as const,
      disabled: saving,
      onClick: onRefresh,
    }] : []),
    ...(onConfirm ? [{
      key: 'confirm',
      label: '确认签约',
      icon: <CheckOutlined />,
      type: 'primary' as const,
      loading: saving,
      onClick: handleConfirm,
    }] : []),
  ];

  // 副标题：显示未保存更改状态
  const subtitle = hasChanges ? (
    <span className="px-2 py-1 text-xs text-orange-600 bg-orange-100 rounded">
      有未保存的更改
    </span>
  ) : null;

  return (
    <PageLayout
      title="确认签约"
      showBack={!!onBack}
      onBack={onBack}
      subtitle={subtitle}
      actions={headerActions}
      fixed={true}
      contentClassName="space-y-6"
    >
      <Spin spinning={loading || saving}>
        <Form
          form={form}
          layout="vertical"
          initialValues={contractData}
          onValuesChange={handleFormChange}
        >
          {/* 合同基本信息 - 只读 */}
          <ContractBasicInfo contractData={contractData} />

          {/* 合同信息 - 可编辑 */}
          <ContractInfoEditable contractData={contractData} />

          {/* 合同周期 - 可编辑 */}
          <ContractPeriodEditable contractData={contractData} />

          {/* 分阶段回款 - 只读 */}
          <StagePaymentTable stagePaymentList={contractData.分阶段回款列表} />

          {/* 项目回款明细 - 只读 */}
          <PaymentDetailTable paymentDetailList={contractData.项目回款明细列表} />

          {/* 甲方信息 - 可编辑 */}
          <PartyInfoEditable 
            title="甲方信息" 
            partyInfo={contractData.甲方信息}
            isPartyA={true}
          />

          {/* 乙方信息 - 可编辑 */}
          <PartyInfoEditable 
            title="乙方信息" 
            partyInfo={contractData.乙方信息}
            isPartyA={false}
          />

          {/* 知识产权及其他条款 - 只读 */}
          <ContractClauses 
            title="知识产权及其他条款"
            content={contractData.知识产权及其他条款}
          />

          {/* 违约条款及违约 - 只读 */}
          <ContractClauses 
            title="违约条款及违约"
            content={contractData.违约条款及违约}
          />

          {/* 产品服务 - 只读 */}
          <ProductServiceTable />
        </Form>
      </Spin>
    </PageLayout>
  );
});

ContractConfirm.displayName = 'ContractConfirm';

export default ContractConfirm; 