import React from "react";
import { Input, Button, Card, Space, Switch, Select, Typography, Menu, Layout } from "antd";
import { useNavigate, useLocation } from "react-router-dom";
import MasterContext from "@/context/masterContext";
import { useCounterStore, useUserStore, useTokenIDStore } from "@/store";
import { usePersistStore, useSessionStore } from "@/store/persistStore";
import styles from "./index.less";
import { cpapi } from "@clickpaas/cp-api";
import { routeConfigs, ExtendedRouteConfig } from "@/router";
import {
  HomeOutlined,
  BarChartOutlined,
  UnorderedListOutlined,
  EyeOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  PlusOutlined,
  EditOutlined,
  SearchOutlined,
  AuditOutlined
} from "@ant-design/icons";

const { Title, Text } = Typography;
const { Option } = Select;
const { Sider, Content } = Layout;

/**
 * 菜单配置项接口
 */
interface MenuItemConfig {
  key: string;
  label: string;
  icon?: React.ReactNode;
  children?: MenuItemConfig[];
}

/**
 * 主页组件
 * @returns JSX.Element
 */
const Home = () => {
  /** APaaS 平台传递给微应用的状态数据 */
  const props = React.useContext(MasterContext);
  /** 当组件运行在 APaaS 基座应用时才存在 props 属性 */
  const { configurations } = props || {};
  /** configurations 是组件的配置项信息 */

  const navigate = useNavigate();
  const location = useLocation();

  // 使用计数器 store
  const { count, increment, decrement, reset } = useCounterStore();
  const { tokenID, setTokenID } = useTokenIDStore();
  // 使用用户 store
  const { user, setUser, clearUser } = useUserStore();

  // 使用持久化 store
  const { theme, language, settings, setTheme, setLanguage, updateSettings } =
    usePersistStore();

  // 使用会话 store
  const {
    currentPage,
    breadcrumbs,
    setCurrentPage,
    addBreadcrumb,
    clearBreadcrumbs,
  } = useSessionStore();
  
  // 菜单展开状态
  const [openKeys, setOpenKeys] = React.useState<string[]>(['/contract-review']);
  
  // 监听路径变化，更新展开的菜单
   React.useEffect(() => {
     const currentOpenKeys = getOpenKeys();
     if (currentOpenKeys.length > 0) {
       setOpenKeys(prev => {
         const newKeys = Array.from(new Set([...prev, ...currentOpenKeys]));
         return newKeys;
       });
     }
   }, [location.pathname]);

  /**
   * 图标映射表
   */
  const iconMap: Record<string, React.ReactNode> = {
    HomeOutlined: <HomeOutlined />,
    PlusOutlined: <PlusOutlined />,
    UnorderedListOutlined: <UnorderedListOutlined />,
    EyeOutlined: <EyeOutlined />,
    DollarOutlined: <DollarOutlined />,
    ClockCircleOutlined: <ClockCircleOutlined />,
    FileTextOutlined: <FileTextOutlined />,
    AuditOutlined: <AuditOutlined />,
    EditOutlined: <EditOutlined />,
    BarChartOutlined: <BarChartOutlined />,
  };

  /**
   * 从路由配置生成菜单项
   * @param routes 路由配置数组
   * @param parentPath 父级路径
   * @returns 菜单配置数组
   */
  const generateMenuItems = (routes: ExtendedRouteConfig[], parentPath = ""): MenuItemConfig[] => {
    return routes
      .filter(route => {
        // 过滤掉隐藏的路由和没有meta的路由
        return route.meta && !route.meta.hidden;
      })
      .map(route => {
        // 修复路径拼接逻辑
        let fullPath: string;
        if (route.index) {
          fullPath = "/";
        } else if (route.path?.startsWith('/')) {
          // 绝对路径
          fullPath = route.path;
        } else {
          // 相对路径，需要与父路径拼接
          fullPath = parentPath === "/" ? `/${route.path}` : `${parentPath}/${route.path}`;
        }
        
        const menuItem: MenuItemConfig = {
          key: fullPath,
          label: route.meta!.title,
          icon: route.meta!.icon ? iconMap[route.meta!.icon] : undefined,
        };

        // 如果有子路由，递归生成子菜单
        if (route.children && route.children.length > 0) {
          const childItems = generateMenuItems(route.children, fullPath);
          if (childItems.length > 0) {
            menuItem.children = childItems;
          }
        }

        return menuItem;
      });
  };

  /**
   * 菜单配置数据 - 从路由配置生成
   */
  const menuItems: MenuItemConfig[] = React.useMemo(() => {
    // 获取根路由的子路由（即主要的页面路由）
    const rootRoute = routeConfigs.find(route => route.path === "/");
    if (rootRoute && rootRoute.children) {
      return generateMenuItems(rootRoute.children);
    }
    return [];
  }, []);

  /**
   * 处理菜单点击事件
   * @param menuInfo 菜单信息
   */
  const handleMenuClick = ({ key }: { key: string }) => {
    if (key) {
      // 检查是否为父级菜单项（有子菜单的项）
      const hasChildren = menuItems.some(item => 
        item.key === key && item.children && item.children.length > 0
      );
      
      // 只有非父级菜单项才进行导航
      if (!hasChildren) {
        navigate(key);
      }
    }
  };

  /**
   * 获取当前选中的菜单项
   */
  const getSelectedKeys = () => {
    const pathname = location.pathname;
    return [pathname === "/" ? "/" : pathname];
  };

  /**
   * 获取展开的菜单项
   * @returns 需要展开的菜单项key数组
   */
  const getOpenKeys = () => {
    const pathname = location.pathname;
    const openKeys: string[] = [];
    
    // 递归查找包含当前路径的父级菜单
    const findParentKeys = (items: MenuItemConfig[], currentPath: string): string[] => {
      for (const item of items) {
        if (item.children) {
          // 检查子菜单中是否包含当前路径
          const hasCurrentPath = item.children.some(child => 
            currentPath.startsWith(child.key)
          );
          if (hasCurrentPath) {
            return [item.key, ...findParentKeys(item.children, currentPath)];
          }
        }
      }
      return [];
    };
    
    const parentKeys = findParentKeys(menuItems, pathname);
    // 如果是合同评审相关路径，确保展开合同评审菜单
    if (pathname.startsWith('/contract-review')) {
      return ['/contract-review', ...parentKeys];
    }
    return parentKeys;
  };

  /**
   * 模拟设置用户信息
   */
  const handleSetUser = () => {
    setUser({
      id: "1",
      name: "张三",
      email: "<EMAIL>",
    });
  };

  /**
   * 获取用户信息
   */
  const getUserInfo = async() => {
    console.log("获取用户信息=================");
    const res = await cpapi.common.getUserInfo({ 
      otherWindow: window.parent, 
      data: {
      }
    })
    console.log("获取用户信息res=================", res);
  }

  return (
    <Layout style={{ minHeight: "100vh" }}>
      {/* 左侧菜单 */}
      <Sider width={250} theme="light" style={{ borderRight: "1px solid #f0f0f0" }}>
        <div style={{ padding: "16px", borderBottom: "1px solid #f0f0f0" }}>
          <Title level={4} style={{ margin: 0, textAlign: "center" }}>
            系统菜单
          </Title>
        </div>
        <Menu
          mode="inline"
          selectedKeys={getSelectedKeys()}
          openKeys={openKeys}
          onOpenChange={setOpenKeys}
          onClick={handleMenuClick}
          items={menuItems}
          style={{ border: "none" }}
          inlineCollapsed={false} // 确保菜单不折叠
        />
      </Sider>

      {/* 右侧内容区域 */}
      <Content style={{ padding: "24px", backgroundColor: "#fff" }}>
        <div className="content-wrapper">
          <header className="mb-6">
            <Title level={2}>微应用管理系统</Title>
            <Text type="secondary">当前路径: {location.pathname}</Text>
          </header>

          <Space direction="vertical" size="large" style={{ width: "100%" }}>
            {/* 操作按钮区域 */}
            <Card title="系统操作" size="small">
              <Space wrap>
                <Button type="primary" onClick={getUserInfo}>
                  获取用户信息
                </Button>
                <Input
                  placeholder="设置tokenID"
                  value={tokenID}
                  onChange={(e) => setTokenID(e.target.value)}
                  style={{ width: 200 }}
                />
                <Button
                  type="primary"
                  onClick={() => (document.cookie = `tokenId=${tokenID}; _lang=zh_CN`)}
                >
                  设置cookie
                </Button>
              </Space>
            </Card>

            {/* 计数器示例 */}
            <Card title="计数器状态管理" size="small">
              <Space>
                <Text>当前计数: {count}</Text>
                <Button onClick={increment}>增加</Button>
                <Button onClick={decrement}>减少</Button>
                <Button onClick={reset}>重置</Button>
              </Space>
            </Card>

            {/* 用户信息示例 */}
            <Card title="用户信息管理" size="small">
              {user ? (
                <Space direction="vertical">
                  <Text>用户ID: {user.id}</Text>
                  <Text>姓名: {user.name}</Text>
                  <Text>邮箱: {user.email}</Text>
                  <Button onClick={clearUser}>清除用户</Button>
                </Space>
              ) : (
                <Space>
                  <Text>暂无用户信息</Text>
                  <Button onClick={handleSetUser}>设置用户</Button>
                </Space>
              )}
            </Card>

            {/* 持久化设置示例 */}
            <Card title="持久化设置 (localStorage)" size="small">
              <Space direction="vertical" style={{ width: "100%" }}>
                <Space>
                  <Text>主题:</Text>
                  <Select value={theme} onChange={setTheme} style={{ width: 120 }}>
                    <Option value="light">浅色</Option>
                    <Option value="dark">深色</Option>
                  </Select>
                </Space>
                <Space>
                  <Text>语言:</Text>
                  <Select
                    value={language}
                    onChange={setLanguage}
                    style={{ width: 120 }}
                  >
                    <Option value="zh">中文</Option>
                    <Option value="en">English</Option>
                  </Select>
                </Space>
                <Space>
                  <Text>通知:</Text>
                  <Switch
                    checked={settings.notifications}
                    onChange={(checked) =>
                      updateSettings({ notifications: checked })
                    }
                  />
                </Space>
                <Space>
                  <Text>自动保存:</Text>
                  <Switch
                    checked={settings.autoSave}
                    onChange={(checked) => updateSettings({ autoSave: checked })}
                  />
                </Space>
              </Space>
            </Card>

            {/* 会话状态示例 */}
            <Card title="会话状态管理 (sessionStorage)" size="small">
              <Space direction="vertical" style={{ width: "100%" }}>
                <Space>
                  <Text>当前页面: {currentPage}</Text>
                  <Button onClick={() => setCurrentPage("list")}>
                    切换到列表页
                  </Button>
                  <Button onClick={() => setCurrentPage("detail")}>
                    切换到详情页
                  </Button>
                </Space>
                <Space>
                  <Text>面包屑: {breadcrumbs.join(" > ") || "无"}</Text>
                  <Button onClick={() => addBreadcrumb("新页面")}>
                    添加面包屑
                  </Button>
                  <Button onClick={clearBreadcrumbs}>清空面包屑</Button>
                </Space>
              </Space>
            </Card>
          </Space>
        </div>
      </Content>
    </Layout>
  );
};

export default Home;
