  ### curl获取指标数据
 ```sh
curl 'https://pms.tongbaninfo.com:8888/app/api/standardList/front/getListData' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -b '_currency=null; cp-hub-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjJmNWNmZDk1LTY1ZTUtMTFlYi1iM2M3LTAyMjZjYTViMThhYSIsInVzZXJuYW1lIjoiYWRtaW4iLCJlbWFpbCI6IiIsImV4cCI6NjE3NDcxMjg1NjcsImlhdCI6MTc0NzEyODYyN30.LK4YFgg89pjJ4FLdUc-W2LLUBS98SUlTo3ksm7fAY2k; envList=[{%22userTag%22:%22beta%22%2C%22feTag%22:%22beta%22%2C%22id%22:%22y31b3vs2b%22}]; _timezone=UTC%2B08%3A00; ipaasTokenId=A789EDDE116B21606DE16F93EF4C3CE9; tokenId=2dbdee00d36062504b4befcce9e140f6; _lang=zh_CN; tongbaninfo.com_watch_currentTime=2025-05-27T18:33:01+08:00' \
  -H 'Origin: https://pms.tongbaninfo.com:8888' \
  -H 'Referer: https://pms.tongbaninfo.com:8888/app' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H '_lang: zh_CN' \
  -H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sw6: 1-MS42MDY2MDg5ODcuMTc0ODM0MTk4MjAwNzAwMDE=-MS42MDY2MDg5ODcuMTc0ODM0MTk4MjAwNzAwMDE=-0-1-1-IzEyNy4wLjAuMTo4MA==-LTE=-LTE=' \
  --data-raw '{"requestType":"code","layoutId":"coordinate_KQM3961","resourceType":"menu","resourceId":"KQM1674","standardCollectionId":"KQM1689","pageNo":1,"pageSize":50,"tempFilterBoxDataMap":null,"showModelType":"list","filterBoxDataMap":{"columnFilterObj":{"expression":"1","conditions":[{"fieldCode":"KQM17695","fieldType":"MULTITOONE","fieldName":"所属指标模板","properties":null,"operator":"CONTAIN","operatorName":null,"text":{"positionStatus":[null],"icon":[null],"text":["部门平均工资"],"value":["KQM3086034"]},"value":["KQM3086034"],"valueName":"部门平均工资","type":null,"querySubNode":null,"serialNumber":1}],"isAdvanced":true},"filterObj":null,"listViewFilterObj":null,"searchFilterObj":null,"viewId":"all_KQM1689"},"needToRecordLastViewId":true,"lastViewId":"all_KQM1689","filterBoxData":null,"_crumb":"********************************"}'
 ```



### 这是返回数据 其中 【KQM1689:KQM17431】是部门  【KQM18354】是实际工资 帮我转成相关合理的数据结构
 ```sh
 {
    "code": 200,
    "requestTime": null,
    "appName": null,
    "msg": "服务器成功返回请求的数据",
    "subCode": null,
    "subMsg": null,
    "subMsgType": "success",
    "data": {
        "body": [
            {
                "id": "KQM3072016",
                "data": {
                    "KQM17591": {
                        "text": "KQM3072016",
                        "value": "KQM3072016"
                    },
                    "KQM1689:KQM17431": {
                        "text": "市场营销部",
                        "value": "市场营销部"
                    },
                    "KQM19670": {
                        "text": "上海通办信息服务有限公司",
                        "value": "KQM2259052"
                    },
                    "KQM17592": {
                        "text": "2025-05-27 18:25:17",
                        "value": "2025-05-27 18:25:17"
                    },
                    "KQM17696": {
                        "text": null,
                        "value": null
                    },
                    "KQM17597": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17697": {
                        "text": null,
                        "value": null
                    },
                    "KQM18354": {
                        "text": 30000.0,
                        "value": 30000.0
                    },
                    "KQM18355": {
                        "text": "KQM2253732",
                        "value": "KQM2253732"
                    },
                    "KQM17695": {
                        "text": "部门平均工资",
                        "value": "KQM3086034"
                    },
                    "KQM17431": {
                        "text": "市场营销部",
                        "value": "市场营销部"
                    },
                    "KQM17596": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM18600": {
                        "text": "分组为部门id",
                        "value": "分组为部门id"
                    },
                    "KQM17600": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3072016"
                    },
                    "KQM21405": {
                        "text": null,
                        "value": null
                    }
                },
                "children": null
            },
            {
                "id": "KQM3072047",
                "data": {
                    "KQM17591": {
                        "text": "KQM3072047",
                        "value": "KQM3072047"
                    },
                    "KQM1689:KQM17431": {
                        "text": "研发二部",
                        "value": "研发二部"
                    },
                    "KQM19670": {
                        "text": "上海通办信息服务有限公司",
                        "value": "KQM2259052"
                    },
                    "KQM17592": {
                        "text": "2025-05-27 18:29:14",
                        "value": "2025-05-27 18:29:14"
                    },
                    "KQM17696": {
                        "text": null,
                        "value": null
                    },
                    "KQM17597": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17697": {
                        "text": null,
                        "value": null
                    },
                    "KQM18354": {
                        "text": 30000.0,
                        "value": 30000.0
                    },
                    "KQM18355": {
                        "text": "KQM2259055",
                        "value": "KQM2259055"
                    },
                    "KQM17695": {
                        "text": "部门平均工资",
                        "value": "KQM3086034"
                    },
                    "KQM17431": {
                        "text": "研发二部",
                        "value": "研发二部"
                    },
                    "KQM17596": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17600": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3072047"
                    },
                    "KQM21405": {
                        "text": null,
                        "value": null
                    }
                },
                "children": null
            },
            {
                "id": "KQM3072074",
                "data": {
                    "KQM17591": {
                        "text": "KQM3072074",
                        "value": "KQM3072074"
                    },
                    "KQM1689:KQM17431": {
                        "text": "交付实施二部",
                        "value": "交付实施二部"
                    },
                    "KQM19670": {
                        "text": "上海通办信息服务有限公司",
                        "value": "KQM2259052"
                    },
                    "KQM17592": {
                        "text": "2025-05-27 18:31:10",
                        "value": "2025-05-27 18:31:10"
                    },
                    "KQM17696": {
                        "text": null,
                        "value": null
                    },
                    "KQM17597": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17697": {
                        "text": null,
                        "value": null
                    },
                    "KQM18354": {
                        "text": 30000.0,
                        "value": 30000.0
                    },
                    "KQM18355": {
                        "text": "KQM2259057",
                        "value": "KQM2259057"
                    },
                    "KQM17695": {
                        "text": "部门平均工资",
                        "value": "KQM3086034"
                    },
                    "KQM17431": {
                        "text": "交付实施二部",
                        "value": "交付实施二部"
                    },
                    "KQM17596": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17600": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3072074"
                    },
                    "KQM21405": {
                        "text": null,
                        "value": null
                    }
                },
                "children": null
            },
            {
                "id": "KQM3086063",
                "data": {
                    "KQM17591": {
                        "text": "KQM3086063",
                        "value": "KQM3086063"
                    },
                    "KQM1689:KQM17431": {
                        "text": "咨询服务部",
                        "value": "咨询服务部"
                    },
                    "KQM19670": {
                        "text": "上海通办信息服务有限公司",
                        "value": "KQM2259052"
                    },
                    "KQM17592": {
                        "text": "2025-05-27 18:26:17",
                        "value": "2025-05-27 18:26:17"
                    },
                    "KQM17696": {
                        "text": null,
                        "value": null
                    },
                    "KQM17597": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17697": {
                        "text": null,
                        "value": null
                    },
                    "KQM18354": {
                        "text": 30000.0,
                        "value": 30000.0
                    },
                    "KQM18355": {
                        "text": "KQM2253734",
                        "value": "KQM2253734"
                    },
                    "KQM17695": {
                        "text": "部门平均工资",
                        "value": "KQM3086034"
                    },
                    "KQM17431": {
                        "text": "咨询服务部",
                        "value": "咨询服务部"
                    },
                    "KQM17596": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17600": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3086063"
                    },
                    "KQM21405": {
                        "text": null,
                        "value": null
                    }
                },
                "children": null
            },
            {
                "id": "KQM3086070",
                "data": {
                    "KQM17591": {
                        "text": "KQM3086070",
                        "value": "KQM3086070"
                    },
                    "KQM1689:KQM17431": {
                        "text": "产品创新部",
                        "value": "产品创新部"
                    },
                    "KQM19670": {
                        "text": "上海通办信息服务有限公司",
                        "value": "KQM2259052"
                    },
                    "KQM17592": {
                        "text": "2025-05-27 18:27:17",
                        "value": "2025-05-27 18:27:17"
                    },
                    "KQM17696": {
                        "text": null,
                        "value": null
                    },
                    "KQM17597": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17697": {
                        "text": null,
                        "value": null
                    },
                    "KQM18354": {
                        "text": 30000.0,
                        "value": 30000.0
                    },
                    "KQM18355": {
                        "text": "KQM2253731",
                        "value": "KQM2253731"
                    },
                    "KQM17695": {
                        "text": "部门平均工资",
                        "value": "KQM3086034"
                    },
                    "KQM17431": {
                        "text": "产品创新部",
                        "value": "产品创新部"
                    },
                    "KQM17596": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17600": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3086070"
                    },
                    "KQM21405": {
                        "text": null,
                        "value": null
                    }
                },
                "children": null
            },
            {
                "id": "KQM3086091",
                "data": {
                    "KQM17591": {
                        "text": "KQM3086091",
                        "value": "KQM3086091"
                    },
                    "KQM1689:KQM17431": {
                        "text": "质量保障部",
                        "value": "质量保障部"
                    },
                    "KQM19670": {
                        "text": "上海通办信息服务有限公司",
                        "value": "KQM2259052"
                    },
                    "KQM17592": {
                        "text": "2025-05-27 18:27:56",
                        "value": "2025-05-27 18:27:56"
                    },
                    "KQM17696": {
                        "text": null,
                        "value": null
                    },
                    "KQM17597": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17697": {
                        "text": null,
                        "value": null
                    },
                    "KQM18354": {
                        "text": 30000.0,
                        "value": 30000.0
                    },
                    "KQM18355": {
                        "text": "KQM2253736",
                        "value": "KQM2253736"
                    },
                    "KQM17695": {
                        "text": "部门平均工资",
                        "value": "KQM3086034"
                    },
                    "KQM17431": {
                        "text": "质量保障部",
                        "value": "质量保障部"
                    },
                    "KQM17596": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17600": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3086091"
                    },
                    "KQM21405": {
                        "text": null,
                        "value": null
                    }
                },
                "children": null
            },
            {
                "id": "KQM3086094",
                "data": {
                    "KQM17591": {
                        "text": "KQM3086094",
                        "value": "KQM3086094"
                    },
                    "KQM1689:KQM17431": {
                        "text": "研发一部",
                        "value": "研发一部"
                    },
                    "KQM19670": {
                        "text": "上海通办信息服务有限公司",
                        "value": "KQM2259052"
                    },
                    "KQM17592": {
                        "text": "2025-05-27 18:28:48",
                        "value": "2025-05-27 18:28:48"
                    },
                    "KQM17696": {
                        "text": null,
                        "value": null
                    },
                    "KQM17597": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17697": {
                        "text": null,
                        "value": null
                    },
                    "KQM18354": {
                        "text": 30000.0,
                        "value": 30000.0
                    },
                    "KQM18355": {
                        "text": "KQM2259056",
                        "value": "KQM2259056"
                    },
                    "KQM17695": {
                        "text": "部门平均工资",
                        "value": "KQM3086034"
                    },
                    "KQM17431": {
                        "text": "研发一部",
                        "value": "研发一部"
                    },
                    "KQM17596": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17600": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3086094"
                    },
                    "KQM21405": {
                        "text": null,
                        "value": null
                    }
                },
                "children": null
            },
            {
                "id": "KQM3086122",
                "data": {
                    "KQM17591": {
                        "text": "KQM3086122",
                        "value": "KQM3086122"
                    },
                    "KQM1689:KQM17431": {
                        "text": "研发三部",
                        "value": "研发三部"
                    },
                    "KQM19670": {
                        "text": "上海通办信息服务有限公司",
                        "value": "KQM2259052"
                    },
                    "KQM17592": {
                        "text": "2025-05-27 18:29:57",
                        "value": "2025-05-27 18:29:57"
                    },
                    "KQM17696": {
                        "text": null,
                        "value": null
                    },
                    "KQM17597": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17697": {
                        "text": null,
                        "value": null
                    },
                    "KQM18354": {
                        "text": 30000.0,
                        "value": 30000.0
                    },
                    "KQM18355": {
                        "text": "KQM2253735",
                        "value": "KQM2253735"
                    },
                    "KQM17695": {
                        "text": "部门平均工资",
                        "value": "KQM3086034"
                    },
                    "KQM17431": {
                        "text": "研发三部",
                        "value": "研发三部"
                    },
                    "KQM17596": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17600": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3086122"
                    },
                    "KQM21405": {
                        "text": null,
                        "value": null
                    }
                },
                "children": null
            },
            {
                "id": "KQM3086128",
                "data": {
                    "KQM17591": {
                        "text": "KQM3086128",
                        "value": "KQM3086128"
                    },
                    "KQM1689:KQM17431": {
                        "text": "交付实施一部",
                        "value": "交付实施一部"
                    },
                    "KQM19670": {
                        "text": "上海通办信息服务有限公司",
                        "value": "KQM2259052"
                    },
                    "KQM17592": {
                        "text": "2025-05-27 18:30:31",
                        "value": "2025-05-27 18:30:31"
                    },
                    "KQM17696": {
                        "text": null,
                        "value": null
                    },
                    "KQM17597": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17697": {
                        "text": null,
                        "value": null
                    },
                    "KQM18354": {
                        "text": 30000.0,
                        "value": 30000.0
                    },
                    "KQM18355": {
                        "text": "KQM2967713",
                        "value": "KQM2967713"
                    },
                    "KQM17695": {
                        "text": "部门平均工资",
                        "value": "KQM3086034"
                    },
                    "KQM17431": {
                        "text": "交付实施一部",
                        "value": "交付实施一部"
                    },
                    "KQM17596": {
                        "text": "李会明",
                        "value": "KQM989"
                    },
                    "KQM17600": {
                        "text": "运营管理系统",
                        "value": "HW44"
                    },
                    "id": {
                        "text": "",
                        "value": "KQM3086128"
                    },
                    "KQM21405": {
                        "text": null,
                        "value": null
                    }
                },
                "children": null
            }
        ],
        "pagination": {
            "current": 1,
            "pageSize": 50,
            "total": 9,
            "pages": 1,
            "hasMorePage": false,
            "dimTotal": "9"
        },
        "viewId": null,
        "viewName": null,
        "filterBoxData": null,
        "columnFilterBoxData": {
            "id": null,
            "filterName": null,
            "isAdvanced": true,
            "expression": "1",
            "conditions": [
                {
                    "serialNumber": "1",
                    "fieldCode": "KQM17695",
                    "fieldName": "所属指标模板",
                    "fieldType": "MULTITOONE",
                    "operator": "CONTAIN",
                    "operatorName": null,
                    "value": [
                        "KQM3086034"
                    ],
                    "valueName": "部门平均工资",
                    "level": null,
                    "valueCode": null,
                    "type": null,
                    "querySubNode": null,
                    "text": {
                        "positionStatus": [
                            null
                        ],
                        "icon": [
                            null
                        ],
                        "text": [
                            "部门平均工资"
                        ],
                        "value": [
                            "KQM3086034"
                        ]
                    },
                    "result": null,
                    "properties": null
                }
            ],
            "convertToServerTimeZone": null,
            "empty": false
        },
        "buttonMap": null,
        "listShowModel": "list"
    },
    "tid": null,
    "originalTid": null
}
 ```