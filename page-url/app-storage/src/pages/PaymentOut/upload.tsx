import React, { useState, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { 
  Card, 
  message, 
  Descriptions
} from "antd";
import { 
  ReloadOutlined
} from "@ant-design/icons";
import { PageLayout, FileUpload } from "@/components";
import type { HeaderAction, ExtendedUploadFile, FileInfo } from "@/components";
import type { UploadReceiptFormData } from "./types";

/**
 * 上传回单页面组件
 */
const UploadReceipt: React.FC = () => {
  const { paymentOutId } = useParams<{ paymentOutId: string }>();
  const navigate = useNavigate();
  
  // 状态管理
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [uploading, setUploading] = useState(false);

  // 模拟付款信息数据
  const mockPaymentInfo = {
    付款申请编号: "FK2025061500-1",
    合同名称: "2025年四川省大府易享数据平台合同",
    合同编号: "HT2024051800-1",
    客户名称: "上海智汇科技有限公司",
    发票申请编号: "FP2025061200-1",
    发票金额: 80.00,
    付款金额: 60.00,
    付款方式: "银行转账",
    付款银行: "中国银行",
    付款账户: "977893333322221133",
    付款时间: "2025-06-13",
    备注: "预付款项，按照合同约定支付60%",
  };

  // 处理文件上传变化
  const handleFileChange = useCallback((newFileList: ExtendedUploadFile[], fileInfos: FileInfo[]) => {
    setFileList(newFileList);
    console.log('文件列表变化:', newFileList);
    console.log('文件信息:', fileInfos);
  }, []);

  // 返回按钮处理
  const handleBack = useCallback((): void => {
    navigate("/payment-out");
  }, [navigate]);

  // 刷新按钮处理
  const handleRefresh = useCallback((): void => {
    // 重置文件列表和上传状态
    setFileList([]);
    setUploading(false);
    message.info('页面已刷新');
  }, []);

  // 提交上传
  const handleSubmit = useCallback(async (): Promise<void> => {
    if (fileList.length === 0) {
      message.error('请选择要上传的回单凭证');
      return;
    }

    // 检查文件是否上传成功
    const uploadedFiles = fileList.filter(file => file.status === 'done' && file.fileId);
    if (uploadedFiles.length === 0) {
      message.error('请等待文件上传完成');
      return;
    }

    setUploading(true);
    try {
      // 模拟提交API调用
      await new Promise((resolve) => setTimeout(resolve, 1500));

      const uploadData: UploadReceiptFormData = {
        上传回单凭证: fileList[0].originFileObj as File,
        上传文件信息: uploadedFiles.map(file => ({
          fileId: file.fileId!,
          fileName: file.name,
          fileUrl: file.url || '',
          fileSize: file.size || 0,
        }))
      };

      // 这里应该调用实际的API
      console.log('提交数据:', uploadData);
      
      message.success('上传回单凭证成功');
      navigate("/payment-out");
    } catch (error) {
      message.error('提交失败，请重试');
    } finally {
      setUploading(false);
    }
  }, [fileList, navigate]);

  // 配置header操作按钮
  const headerActions: HeaderAction[] = [
    ...[
      {
        key: "refresh",
        label: "刷新",
        icon: <ReloadOutlined />,
        type: "text" as const,
        disabled: uploading,
        onClick: handleRefresh,
      },
    ],
    ...[
      {
        key: "confirm",
        label: "确认",
        type: "primary" as const,
        loading: uploading,
        onClick: handleSubmit,
      },
    ],
  ];

  return (
    <PageLayout
      title="付款详情"
      showBack={true}
      onBack={handleBack}
      actions={headerActions}
    >
      <Card>
        {/* 付款信息区域 */}
        <Descriptions
          bordered
          column={2}
          size="middle"
          className="mb-6"
          labelStyle={{
            width: "150px",
            fontWeight: "500",
            backgroundColor: "#fafafa",
          }}
          contentStyle={{
            backgroundColor: "#ffffff",
          }}
        >
          <Descriptions.Item label="付款申请编号" span={2}>
            <span className="font-medium text-blue-600">
              {mockPaymentInfo.付款申请编号}
            </span>
          </Descriptions.Item>

          <Descriptions.Item label="合同名称" span={2}>
            <span className="font-medium">{mockPaymentInfo.合同名称}</span>
          </Descriptions.Item>

          <Descriptions.Item label="合同编号">
            {mockPaymentInfo.合同编号}
          </Descriptions.Item>

          <Descriptions.Item label="客户名称">
            {mockPaymentInfo.客户名称}
          </Descriptions.Item>

          <Descriptions.Item label="发票申请编号">
            {mockPaymentInfo.发票申请编号}
          </Descriptions.Item>

          <Descriptions.Item label="发票金额（万元）">
            <span className="text-lg font-medium text-green-600">
              {mockPaymentInfo.发票金额.toFixed(2)}
            </span>
          </Descriptions.Item>

          <Descriptions.Item label="付款金额（万元）">
            <span className="text-lg font-medium text-blue-600">
              {mockPaymentInfo.付款金额.toFixed(2)}
            </span>
          </Descriptions.Item>

          <Descriptions.Item label="付款方式">
            <span className="px-2 py-1 text-sm text-blue-800 bg-blue-100 rounded">
              {mockPaymentInfo.付款方式}
            </span>
          </Descriptions.Item>

          <Descriptions.Item label="付款银行">
            {mockPaymentInfo.付款银行}
          </Descriptions.Item>

          <Descriptions.Item label="付款账户">
            <span className="font-mono text-sm">{mockPaymentInfo.付款账户}</span>
          </Descriptions.Item>

          <Descriptions.Item label="付款时间">
            {mockPaymentInfo.付款时间}
          </Descriptions.Item>

          <Descriptions.Item label="备注" span={2}>
            <div className="p-3 bg-gray-50 rounded-md">
              <span className="text-gray-700">{mockPaymentInfo.备注}</span>
            </div>
          </Descriptions.Item>
        </Descriptions>

        {/* 上传回单区域 */}
        <Card
          title={
            <div className="flex items-center">
              <span className="mr-1 text-red-500">*</span>
              <span>上传回单凭证</span>
            </div>
          }
          className="mb-6"
        >
          <FileUpload
            value={fileList}
            onChange={handleFileChange}
            config={{
              mode: 'dragger',
              maxCount: 1,
              maxSizeMB: 20,
              allowedExts: ['rar', 'zip', 'doc', 'docx', 'pdf', 'jpg', 'jpeg', 'png'],
              dragText: '点击或拖拽文件到此区域上传',
              dragHint: '支持扩展名：.rar .zip .doc .docx .pdf .jpg...',
              showDescription: true,
              disabled: uploading,
            }}
          />
        </Card>
      </Card>
    </PageLayout>
  );
};

export default UploadReceipt; 