# 付款列表模块

## 概述

付款列表模块用于管理企业的付款申请记录，包括查看付款详情和上传回单凭证等功能。

## 功能特性

### 1. 付款列表 (`/payment-out`)
- 显示所有付款申请记录
- 支持按合同名称、客户名称、付款申请编号、销售负责人进行搜索
- 分页展示数据
- 实时刷新功能
- 支持查看详情和上传回单操作

### 2. 付款详情 (`/payment-out/:paymentOutId`)
- 显示详细的付款信息
- 包括合同信息、金额信息、付款方式等
- 支持下载付款材料
- 返回列表功能

### 3. 上传回单 (`/payment-out/:paymentOutId/upload`)
- 显示付款详情信息
- 支持上传回单凭证文件
- 文件类型验证（.rar、.zip、.doc、.docx、.pdf、.jpg）
- 文件大小限制（20MB）
- 拖拽上传支持

## 数据结构

### PaymentOutListItem
```typescript
interface PaymentOutListItem {
  id: string;
  序号: number;
  付款申请编号: string;
  合同名称: string;
  合同编码: string;
  客户名称: string;
  发票抬头: string;
  纳税人识别号: string;
  发票金额: number; // 万元
  付款金额: number; // 万元
  销售负责人: string;
  建单状态: string;
}
```

### PaymentOutDetailData
```typescript
interface PaymentOutDetailData {
  id: string;
  付款申请编号: string;
  合同名称: string;
  合同编号: string;
  客户名称: string;
  发票申请编号: string;
  发票金额: number; // 万元
  付款金额: number; // 万元
  付款方式: string;
  付款银行: string;
  付款账户: string;
  付款时间: string;
  上传付款材料: string;
  备注: string;
}
```

## 路由配置

```typescript
// 付款列表
{
  path: "/payment-out",
  element: <PaymentOut />,
  meta: { title: "付款列表", icon: "AccountBookOutlined" }
}

// 付款详情
{
  path: "/payment-out/:paymentOutId",
  element: <PaymentOutDetail />,
  meta: { title: "付款详情", hidden: true }
}

// 上传回单
{
  path: "/payment-out/:paymentOutId/upload",
  element: <UploadReceipt />,
  meta: { title: "上传回单", hidden: true }
}
```

## 组件结构

```
PaymentOut/
├── index.tsx          # 付款列表主页面
├── detail.tsx         # 付款详情页面
├── upload.tsx         # 上传回单页面
├── types.ts           # 类型定义
├── README.md          # 文档说明
└── components/        # 组件目录
    ├── index.ts       # 组件导出
    └── PaymentOutTable.tsx  # 付款列表表格组件
```

## 使用示例

### 导航到付款列表
```typescript
navigate('/payment-out');
```

### 查看付款详情
```typescript
navigate(`/payment-out/${paymentOutId}`);
```

### 上传回单
```typescript
navigate(`/payment-out/${paymentOutId}/upload`);
```

## API 接口

本模块当前使用模拟数据，实际项目中需要替换为真实的API调用：

- `GET /api/payment-out` - 获取付款列表
- `GET /api/payment-out/:id` - 获取付款详情
- `POST /api/payment-out/:id/upload` - 上传回单凭证

## 样式规范

- 使用 Tailwind CSS 进行样式设计
- 遵循项目统一的设计规范
- 响应式设计，支持不同屏幕尺寸
- 使用 Ant Design 组件库

## 技术特点

- **TypeScript**: 严格的类型安全，禁止使用 `any`
- **组件拆分**: 单个组件不超过 400 行
- **状态管理**: 使用 React Hooks 进行状态管理
- **文件上传**: 支持拖拽上传和文件验证
- **响应式设计**: 适配不同屏幕尺寸

## 注意事项

1. 文件上传仅支持指定格式（.rar、.zip、.doc、.docx、.pdf、.jpg）
2. 文件大小限制为 20MB
3. 表格支持横向滚动以适配更多列
4. 所有金额单位为万元，显示时保留两位小数
5. 建单状态使用不同颜色的标签进行区分 