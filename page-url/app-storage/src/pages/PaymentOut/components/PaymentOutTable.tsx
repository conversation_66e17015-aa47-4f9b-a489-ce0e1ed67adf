import React from 'react';
import { Table, Button, Space, Tag } from 'antd';
import { EyeOutlined, UploadOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { PaymentOutListItem } from '../types';

interface PaymentOutTableProps {
  dataSource: PaymentOutListItem[];
  loading: boolean;
  currentPage: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number, size?: number) => void;
  onView: (itemId: string) => void;
  onUploadReceipt: (itemId: string) => void;
}

/**
 * 获取建单状态标签颜色
 */
const getStatusTagColor = (status: string): string => {
  switch (status) {
    case '草稿':
      return 'default';
    case '待审核':
      return 'processing';
    case '已审核':
      return 'warning';
    case '已付款':
      return 'success';
    case '已完成':
      return 'success';
    default:
      return 'default';
  }
};

/**
 * 付款列表表格组件
 */
const PaymentOutTable: React.FC<PaymentOutTableProps> = ({
  dataSource,
  loading,
  currentPage,
  pageSize,
  total,
  onPageChange,
  onView,
  onUploadReceipt,
}) => {
  const columns: ColumnsType<PaymentOutListItem> = [
    {
      title: '序号',
      dataIndex: '序号',
      key: '序号',
      width: 80,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '付款申请编号',
      dataIndex: '付款申请编号',
      key: '付款申请编号',
      width: 180,
      fixed: 'left',
    },
    {
      title: '合同名称',
      dataIndex: '合同名称',
      key: '合同名称',
      width: 200,
      ellipsis: true,
    },
    {
      title: '合同编码',
      dataIndex: '合同编码',
      key: '合同编码',
      width: 120,
    },
    {
      title: '客户名称',
      dataIndex: '客户名称',
      key: '客户名称',
      width: 150,
      ellipsis: true,
    },
    {
      title: '发票抬头',
      dataIndex: '发票抬头',
      key: '发票抬头',
      width: 180,
      ellipsis: true,
    },
    {
      title: '纳税人识别号',
      dataIndex: '纳税人识别号',
      key: '纳税人识别号',
      width: 180,
    },
    {
      title: '发票金额（万元）',
      dataIndex: '发票金额',
      key: '发票金额',
      width: 140,
      align: 'right',
      render: (amount: number) => amount.toFixed(2),
    },
    {
      title: '付款金额（万元）',
      dataIndex: '付款金额',
      key: '付款金额',
      width: 140,
      align: 'right',
      render: (amount: number) => amount.toFixed(2),
    },
    {
      title: '销售负责人',
      dataIndex: '销售负责人',
      key: '销售负责人',
      width: 120,
    },
    {
      title: '建单状态',
      dataIndex: '建单状态',
      key: '建单状态',
      width: 100,
      align: 'center',
      render: (status: string) => (
        <Tag color={getStatusTagColor(status)}>{status}</Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      align: 'center',
      fixed: 'right',
      render: (_, record) => (
        <Space size="small" >
          <Button
            type="link"
            size="small"
            onClick={() => onView(record.id)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => onUploadReceipt(record.id)}
          >
            上传回单
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <Table<PaymentOutListItem>
        columns={columns}
        dataSource={dataSource}
        loading={loading}
        rowKey="id"
        scroll={{ x: 1600 }}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          onChange: onPageChange,
          onShowSizeChange: onPageChange,
          pageSizeOptions: ['10', '20', '50', '100'],
        }}
        className="min-h-96"
      />
    </div>
  );
};

export default PaymentOutTable; 