import React, { useState, useCallback, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Input, Button, Space, Card, message } from "antd";
import { SearchOutlined, ReloadOutlined } from "@ant-design/icons";
import { PageHeader } from "@/components";
import type { HeaderAction } from "@/components";
import { PaymentOutTable } from "./components";
import type { PaymentOutListItem } from "./types";

const { Search } = Input;

/**
 * 付款列表页面组件
 */
const PaymentOut: React.FC = () => {
  const navigate = useNavigate();
  
  // 模拟数据
  const mockPaymentOutData: PaymentOutListItem[] = [
    {
      id: "1",
      序号: 1,
      付款申请编号: "FK2025061500-1",
      合同名称: "2025年四川省大府易享数据平台合同",
      合同编码: "CT0092",
      客户名称: "成都大数据中心",
      发票抬头: "成都启明科技有限公司",
      纳税人识别号: "91110108M3333123X",
      发票金额: 3539.82,
      付款金额: 3539.82,
      销售负责人: "李明",
      建单状态: "已完成",
    },
    {
      id: "2",
      序号: 2,
      付款申请编号: "FK2025061500-2",
      合同名称: "2025年四川省大府易享数据平台合同",
      合同编码: "CT0092",
      客户名称: "成都大数据中心",
      发票抬头: "成都启明科技有限公司",
      纳税人识别号: "91110108MA01A123X",
      发票金额: 3539.82,
      付款金额: 0,
      销售负责人: "李明",
      建单状态: "未完成",
    },
  ];

  // 状态管理
  const [paymentOutList, setPaymentOutList] =
    useState<PaymentOutListItem[]>(mockPaymentOutData);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchText, setSearchText] = useState("");

  // 模拟数据加载
  const fetchPaymentOutData = useCallback(async (search?: string) => {
    setLoading(true);
    try {
      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 800));

      let filteredData = mockPaymentOutData;
      if (search) {
        filteredData = mockPaymentOutData.filter(
          (item) =>
            item.合同名称.includes(search) ||
            item.客户名称.includes(search) ||
            item.付款申请编号.includes(search) ||
            item.销售负责人.includes(search)
        );
      }

      setPaymentOutList(filteredData);
    } catch (error) {
      message.error("获取付款数据失败");
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始化数据
  useEffect(() => {
    fetchPaymentOutData();
  }, [fetchPaymentOutData]);

  // 分页处理
  const handlePageChange = useCallback(
    (page: number, size?: number) => {
      setCurrentPage(page);
      if (size && size !== pageSize) {
        setPageSize(size);
      }
    },
    [pageSize]
  );

  // 搜索处理
  const handleSearch = useCallback(
    (value: string) => {
      setSearchText(value);
      setCurrentPage(1);
      fetchPaymentOutData(value);
    },
    [fetchPaymentOutData]
  );

  // 刷新数据
  const handleRefresh = useCallback(() => {
    setCurrentPage(1);
    setSearchText("");
    fetchPaymentOutData();
  }, [fetchPaymentOutData]);

  // 查看详情
  const handleView = useCallback((itemId: string) => {
    navigate(`/payment-out/${itemId}`);
  }, [navigate]);

  // 上传回单
  const handleUploadReceipt = useCallback((itemId: string) => {
    navigate(`/payment-out/${itemId}/upload`);
  }, [navigate]);

  // 配置header操作按钮
  const headerActions: HeaderAction[] = [
    {
      key: 'refresh',
      label: '刷新',
      icon: <ReloadOutlined />,
      type: 'primary',
      loading: loading,
      onClick: handleRefresh,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader
        title="付款列表"
        actions={headerActions}
        bordered={true}
      />

      <div className="p-6">
        <Card className="mb-6">
          <div className="flex justify-between items-center">
            <Space>
              <Search
                placeholder="搜索合同名称、客户名称、付款申请编号或销售负责人"
                allowClear
                enterButton={<SearchOutlined />}
                size="middle"
                style={{ width: 400 }}
                onSearch={handleSearch}
                loading={loading}
              />
            </Space>

            <div className="text-gray-600">
              共找到 {paymentOutList.length} 条记录
            </div>
          </div>
        </Card>

        <PaymentOutTable
          dataSource={paymentOutList}
          loading={loading}
          currentPage={currentPage}
          pageSize={pageSize}
          total={paymentOutList.length}
          onPageChange={handlePageChange}
          onView={handleView}
          onUploadReceipt={handleUploadReceipt}
        />
      </div>
    </div>
  );
};

export default PaymentOut; 