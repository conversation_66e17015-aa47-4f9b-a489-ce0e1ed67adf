/**
 * 付款列表相关类型定义
 */

// 付款列表项数据接口
export interface PaymentOutListItem {
  id: string;
  序号: number;
  付款申请编号: string;
  合同名称: string;
  合同编码: string;
  客户名称: string;
  发票抬头: string;
  纳税人识别号: string;
  发票金额: number; // 万元
  付款金额: number; // 万元
  销售负责人: string;
  建单状态: string;
}

// 付款列表页面Props
export interface PaymentOutListProps {
  // 数据相关
  paymentOutList: PaymentOutListItem[];
  loading: boolean;
  error?: string;
  
  // 分页相关
  currentPage: number;
  pageSize: number;
  total: number;
  
  // 回调函数
  onPageChange: (page: number, size?: number) => void;
  onView: (itemId: string) => void;
  onUploadReceipt: (itemId: string) => void;
  onSearch?: (searchText: string) => void;
  onRefresh?: () => void;
}

// 建单状态枚举
export enum PaymentOutStatus {
  DRAFT = '草稿',
  PENDING = '待审核',
  APPROVED = '已审核',
  PAID = '已付款',
  COMPLETED = '已完成'
}

// 付款详情数据接口
export interface PaymentOutDetailData {
  id: string;
  付款申请编号: string;
  合同名称: string;
  合同编号: string;
  客户名称: string;
  发票申请编号: string;
  发票金额: number; // 万元
  付款金额: number; // 万元
  付款方式: string;
  付款银行: string;
  付款账户: string;
  付款时间: string;
  上传付款材料: string;
  备注: string;
}

// 付款详情页面Props
export interface PaymentOutDetailProps {
  paymentOutData: PaymentOutDetailData | null;
  loading: boolean;
  error?: string;
  onBack?: () => void;
  onRefresh?: () => void;
}

// 上传回单表单数据
export interface UploadReceiptFormData {
  上传回单凭证: File;
  // 添加上传文件的详细信息
  上传文件信息?: {
    fileId: string;
    fileName: string;
    fileUrl: string;
    fileSize: number;
  }[];
}

// 上传回单页面Props
export interface UploadReceiptProps {
  paymentOutId: string;
  loading: boolean;
  onBack?: () => void;
  onSubmit: (data: UploadReceiptFormData) => Promise<void>;
}

// 表格列配置
export interface PaymentOutTableColumn {
  fixed?: 'left' | 'right';
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (text: any, record: PaymentOutListItem, index: number) => React.ReactNode;
} 