import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Card, Descriptions, Spin, Alert, message } from "antd";
import { ReloadOutlined } from "@ant-design/icons";
import { PageLayout } from "@/components";
import type { HeaderAction } from "@/components";
import type { PaymentOutDetailData } from "./types";

/**
 * 付款详情页面组件
 */
const PaymentOutDetail: React.FC = () => {
  const { paymentOutId } = useParams<{ paymentOutId: string }>();
  const navigate = useNavigate();
  
  // 状态管理
  const [paymentOutData, setPaymentOutData] = useState<PaymentOutDetailData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>();

  // 模拟获取付款详情数据
  const fetchPaymentOutDetail = useCallback(async (id: string): Promise<void> => {
    setLoading(true);
    setError(undefined);

    try {
      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 模拟数据
      const mockDetailData: PaymentOutDetailData = {
        id: id,
        付款申请编号: "FK2025061500-1",
        合同名称: "2025年四川省大府易享数据平台合同",
        合同编号: "HT2024051800-1",
        客户名称: "上海智汇科技有限公司",
        发票申请编号: "FP2025061200-1",
        发票金额: 80.00,
        付款金额: 60.00,
        付款方式: "银行转账",
        付款银行: "中国银行",
        付款账户: "977893333322221133",
        付款时间: "2025-06-13",
        上传付款材料: "合同扫描件.jpeg",
        备注: "预付款项，按照合同约定支付60%",
      };

      setPaymentOutData(mockDetailData);
    } catch (err) {
      setError("获取付款详情失败，请稍后重试");
      console.error("获取付款详情失败:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 页面初始化时获取数据
  useEffect(() => {
    if (paymentOutId) {
      fetchPaymentOutDetail(paymentOutId);
    } else {
      setError("缺少付款ID参数");
    }
  }, [paymentOutId, fetchPaymentOutDetail]);

  // 返回按钮处理
  const handleBack = useCallback((): void => {
    navigate("/payment-out");
  }, [navigate]);

  // 刷新按钮处理
  const handleRefresh = useCallback((): void => {
    if (paymentOutId) {
      fetchPaymentOutDetail(paymentOutId);
    }
  }, [paymentOutId, fetchPaymentOutDetail]);

  // 配置header操作按钮
  const headerActions: HeaderAction[] = [
    {
      key: 'refresh',
      label: '刷新',
      icon: <ReloadOutlined />,
      type: 'primary',
      loading: loading,
      onClick: handleRefresh,
    },
  ];

  // 如果没有数据且不在加载中，显示错误信息
  if (!paymentOutData && !loading && !error) {
    return (
      <PageLayout
        title="付款详情"
        showBack={true}
        onBack={handleBack}
        actions={headerActions}
      >
        <div className="flex justify-center items-center min-h-96">
          <div className="text-center">
            <p className="mb-4 text-gray-500">未找到付款信息</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      title="付款详情"
      showBack={true}
      onBack={handleBack}
      actions={headerActions}
    >
      <div className="space-y-6">
        <Card title="付款详情" className="shadow-sm">
          {error && (
            <Alert
              message="错误"
              description={error}
              type="error"
              showIcon
              className="mb-6"
            />
          )}

          <Spin spinning={loading}>
            {paymentOutData && (
              <Descriptions
                bordered
                column={2}
                size="middle"
                labelStyle={{
                  width: "150px",
                  fontWeight: "500",
                  backgroundColor: "#fafafa",
                }}
                contentStyle={{
                  backgroundColor: "#ffffff",
                }}
              >
                <Descriptions.Item label="付款申请编号" span={2}>
                  <span className="font-medium text-blue-600">
                    {paymentOutData.付款申请编号}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="合同名称" span={2}>
                  <span className="font-medium">{paymentOutData.合同名称}</span>
                </Descriptions.Item>

                <Descriptions.Item label="合同编号">
                  {paymentOutData.合同编号}
                </Descriptions.Item>

                <Descriptions.Item label="客户名称">
                  {paymentOutData.客户名称}
                </Descriptions.Item>

                <Descriptions.Item label="发票申请编号" span={2}>
                  <span className="font-medium">{paymentOutData.发票申请编号}</span>
                </Descriptions.Item>

                <Descriptions.Item label="发票金额（万元）">
                  <span className="text-lg font-medium text-green-600">
                    {paymentOutData.发票金额.toFixed(2)}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="付款金额（万元）">
                  <span className="text-lg font-medium text-blue-600">
                    {paymentOutData.付款金额.toFixed(2)}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="付款方式">
                  <span className="px-2 py-1 text-sm text-blue-800 bg-blue-100 rounded">
                    {paymentOutData.付款方式}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="付款银行">
                  {paymentOutData.付款银行}
                </Descriptions.Item>

                <Descriptions.Item label="付款账户">
                  <span className="font-mono text-sm">{paymentOutData.付款账户}</span>
                </Descriptions.Item>

                <Descriptions.Item label="付款时间">
                  {paymentOutData.付款时间}
                </Descriptions.Item>

                <Descriptions.Item label="上传付款材料">
                  <a
                    href="#"
                    className="text-blue-600 underline hover:text-blue-800"
                    onClick={(e) => {
                      e.preventDefault();
                      message.info("正在下载文件...");
                    }}
                  >
                    {paymentOutData.上传付款材料}
                  </a>
                </Descriptions.Item>

                <Descriptions.Item label="备注" span={2}>
                  <div className="p-3 bg-gray-50 rounded-md">
                    <span className="text-gray-700">{paymentOutData.备注}</span>
                  </div>
                </Descriptions.Item>
              </Descriptions>
            )}
          </Spin>
        </Card>
      </div>
    </PageLayout>
  );
};

export default PaymentOutDetail; 