// 实施成本数据项接口
export interface ImplementationCostItem {
  id: string;
  sequence: number; // 序号
  productCategory: string; // 产品类别
  productName: string; // 产品项目名称
  operationService: string; // 运营服务
  assessUserId: string; // 评估人
  department: string; // 部门信息
  // 新的动态部门成本结构
  departmentCosts: Record<string, number | null>; // 各部门成本 { "研发一部": 1, "市场营销部": null }
  departmentAssessors: Record<string, string>; // 各部门评估人 { "研发一部": "userId1", "市场营销部": "userId2" }
  // 添加分组相关字段
  groupKey: string; // 分组键
  departmentList: string[]; // 包含的部门列表
}



// 合计数据接口
export interface TotalCosts {
  // 动态部门成本合计
  departmentTotals: Record<string, number>;
}

// 组件模式类型
export type ComponentMode = 'edit' | 'readonly';

// 组件Props接口
export interface ImplementationCostsProps {
  className?: string;
  onDataChange?: (data: ImplementationCostItem[]) => void;
  initialData?: ImplementationCostItem[];
  mode?: ComponentMode; // 组件模式：编辑或只读
}

// 表格列配置类型 - 现在是动态的部门名称
export type CostField = string; // 部门名称，如 "研发一部"、"市场营销部" 等 