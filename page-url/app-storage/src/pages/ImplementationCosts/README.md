# 实施成本组件 (ImplementationCosts)

这是一个用于管理和计算实施成本的表格组件，支持实时编辑和自动计算合计。

## 功能特性

- ✅ 实时编辑成本数据
- ✅ 自动计算各项成本合计
- ✅ 响应式设计，支持横向滚动
- ✅ TypeScript 类型安全
- ✅ 符合项目开发规范

## 使用方法

```typescript
import React from 'react';
import ImplementationCosts from '@/pages/ImplementationCosts';
import type { ImplementationCostItem } from '@/pages/ImplementationCosts/types';

const MyPage: React.FC = () => {
  const handleDataChange = (data: ImplementationCostItem[]) => {
    console.log('数据已更新:', data);
    // 处理数据变化，如保存到服务器
  };

  return (
    <div className="p-6">
      <ImplementationCosts
        className="mb-6"
        onDataChange={handleDataChange}
      />
    </div>
  );
};

export default MyPage;
```

## 组件属性 (Props)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `className` | `string` | `''` | 自定义样式类名 |
| `onDataChange` | `(data: ImplementationCostItem[]) => void` | - | 数据变化回调函数 |
| `initialData` | `ImplementationCostItem[]` | 默认数据 | 初始数据 |

## 数据结构

### ImplementationCostItem

```typescript
interface ImplementationCostItem {
  id: string;                    // 唯一标识
  sequence: number;              // 序号
  productCategory: string;       // 产品类别
  productName: string;          // 产品项目名称
  operationService: string;     // 运营服务
  consultingCost: number;       // 咨询服务费实施成本（人月）
  marketingCost: number;        // 市场营销费实施成本（人月）
  qualityAssuranceCost: number; // 质量保障费实施成本（人月）
  deliveryImplementation: number; // 交付实施一部分
}
```

## 样式定制

组件使用 Tailwind CSS 进行样式设计，支持通过 `className` 属性进行样式定制：

```typescript
<ImplementationCosts 
  className="shadow-lg border border-gray-200" 
/>
```

## 注意事项

1. 组件会自动计算各项成本的合计
2. 输入框只接受数字，非数字输入会被转换为 0
3. 合计行以蓝色高亮显示
4. 表格支持横向滚动，适配小屏幕设备 