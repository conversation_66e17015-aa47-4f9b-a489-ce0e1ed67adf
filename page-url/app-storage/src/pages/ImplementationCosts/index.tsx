import React, { useState, useCallback, useMemo, useEffect, useRef } from "react";
import { Table, Input, InputNumber, Space, Button, message } from "antd";
import type { ColumnsType } from "antd/es/table";
import type {
  ImplementationCostItem,
  ImplementationCostsProps,
  TotalCosts,
} from "./types";
import {
  getDepartmentSalaryData,
  getOpportunityGroupListData,
  getUserInfo,
  saveListData,
} from "@/api";
import {
  processApiData,
  getAllDepartments,
} from "@/mapping/ImplementationCostItem";
import type { ApiDataItem } from "@/mapping/ImplementationCostItem";
import { useUserStore } from "@/store";
import {
  processDepartmentSalaryData,
  ProcessedDepartmentSalaryItem,
  getDepartmentSalaryMap,
} from "@/mapping/DepartmentSalary";

const ImplementationCosts: React.FC<ImplementationCostsProps> = (props) => {
  const { mode = "edit", className, onDataChange, initialData } = props;
  console.log("props=================", props);
  const { user, setUser, clearUser } = useUserStore();
  const [data, setData] = useState<ImplementationCostItem[]>([]);
  const [originalData, setOriginalData] = useState<ImplementationCostItem[]>(
    []
  );
  const [rawApiData, setRawApiData] = useState<ApiDataItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  // 添加独立的部门列表状态，避免频繁重新计算
  const [departments, setDepartments] = useState<string[]>([]);

  // 判断是否为只读模式
  const isReadonly = mode === "readonly";
  const [departmentSalaryDataList, setDepartmentSalaryDataList] = useState<
    ProcessedDepartmentSalaryItem[]
  >([]);
  const [departmentSalaryMap, setDepartmentSalaryMap] = useState<
    Record<string, number>
  >({});

  const getDepartmentSalaryDataList = async () => {
    const response = await getDepartmentSalaryData();
    const rawData = response.data.body;
    const processedData = processDepartmentSalaryData(rawData);
    const salaryMap = getDepartmentSalaryMap(processedData);
    setDepartmentSalaryDataList(processedData);
    setDepartmentSalaryMap(salaryMap);
    console.log("获取部门平均工资数据=======", processedData);
    console.log("部门工资映射表=======", salaryMap);
  };

  const getList = async (currentUserInfo?: any) => {
    getDepartmentSalaryDataList();
  
    const url = new URL(window.location.href);
    // 获取哈希部分
    const hashPart = url.hash;
    // 提取哈希部分的查询参数
    const hashParams = new URLSearchParams(hashPart.split("?")[1]);
    // 获取id的值
    const idValue = hashParams.get("id");
    console.log("idValue=================", idValue);

    // 使用传入的用户信息或当前状态中的用户信息
    const userInfoToUse = currentUserInfo || userInfo;
    console.log("使用的用户信息=======", userInfoToUse);

    try {
      setLoading(true);
      const res = await getOpportunityGroupListData({
        fieldCode: idValue || "KQM3096778",
      });
      const result = res.data.body as ApiDataItem[];
      console.log("获取结果=======", result);

      // 保存原始API数据
      setRawApiData(result);

      // 使用映射表处理数据
      const processedData = processApiData(result);
      console.log("原始数据=======", result);
      console.log("处理后的数据=======", processedData);
      console.log("用户信息=======", userInfoToUse);

      setData(processedData);
      
      // 设置部门列表（只在数据加载时设置一次）
      const allDepts = getAllDepartments(processedData);
      setDepartments(allDepts);
      console.log("所有部门列表=======", allDepts);
      
      // 只在编辑模式下保存原始数据用于比较
      if (!isReadonly) {
        setOriginalData(JSON.parse(JSON.stringify(processedData)));
      }
      onDataChange?.(processedData);
    } catch (error) {
      console.error("获取数据失败:", error);
    } finally {
      setLoading(false);
    }
  };
  const [userInfo, setUserInfo] = useState<any>({
    id: "KQM1287",
  });
  const getUser = async () => {
    console.log("获取用户信息=================11111111");
    try {
      const res = await getUserInfo();
      console.log("获取用户信息res=================", res);
      setUserInfo(res?.data);
      setUser(res?.data);
      return res?.data; // 返回用户信息供后续使用
    } catch (error) {
      console.error("获取用户信息失败:", error);
      return null;
    }
  };
  // 初始化数据的函数
  const initializeData = async () => {
    try {
      // 先获取用户信息
      const userInfoData = await getUser();
      if (userInfoData) {
        // 用户信息获取成功后再获取列表数据，并传入用户信息
        await getList(userInfoData);
      }
    } catch (error) {
      console.error("初始化数据失败:", error);
    }
  };

  useEffect(() => {
    initializeData();
  }, []);

  // 使用稳定的部门列表，避免频繁重新计算
  const allDepartments = useMemo(() => {
    return departments;
  }, [departments]);

  // 计算合计
  const totalCosts = useMemo((): TotalCosts => {
    const departmentTotals: Record<string, number> = {};

    // 初始化所有部门的合计为0
    allDepartments.forEach((dept) => {
      departmentTotals[dept] = 0;
    });

    // 累加每个部门的成本
    data.forEach((item) => {
      Object.entries(item.departmentCosts).forEach(([dept, cost]) => {
        // 只有当 cost 不为 null 时才累加
        if (cost !== null) {
          departmentTotals[dept] = (departmentTotals[dept] || 0) + cost;
        }
      });
    });

    return { departmentTotals };
  }, [data, allDepartments]);

  // 检查用户是否有权限编辑特定部门的成本字段（仅在编辑模式下有效）
  const canEditCostField = useCallback((
    record: ImplementationCostItem,
    department: string
  ): boolean => {
    if (isReadonly) return false;
    // 检查当前用户是否是该部门的评估人
    return record.departmentAssessors[department] === userInfo.id;
  }, [isReadonly, userInfo.id]);

  // 获取排序后的部门列表（可编辑的排在前面）
  const sortedDepartments = useMemo(() => {
    return [...allDepartments].sort((deptA, deptB) => {
      // 检查当前用户是否可以编辑这些部门的任何一行数据
      const canEditDeptA = data.some((item) => canEditCostField(item, deptA));
      const canEditDeptB = data.some((item) => canEditCostField(item, deptB));

      // 可编辑的部门排在前面
      if (canEditDeptA && !canEditDeptB) return -1;
      if (!canEditDeptA && canEditDeptB) return 1;
      return 0; // 保持原有顺序
    });
  }, [allDepartments, data, canEditCostField]);

  // 内部状态更新函数，不触发外部回调
  const updateInternalData = useCallback(
    (id: string, department: string, value: number | null) => {
      if (isReadonly) return;

      setData((prevData) => {
        return prevData.map((item) =>
          item.id === id
            ? {
                ...item,
                departmentCosts: {
                  ...item.departmentCosts,
                  [department]: value,
                },
              }
            : item
        );
      });
    },
    [isReadonly]
  );

  // 外部数据同步函数，会触发 onDataChange
  const syncExternalData = useCallback(
    (id: string, department: string, value: number | null) => {
      if (isReadonly) return;

      setData((prevData) => {
        const newData = prevData.map((item) =>
          item.id === id
            ? {
                ...item,
                departmentCosts: {
                  ...item.departmentCosts,
                  [department]: value,
                },
              }
            : item
        );
        // 只在这里调用 onDataChange
        onDataChange?.(newData);
        return newData;
      });
    },
    [onDataChange, isReadonly]
  );

  // 改进 CostCell 组件，区分内部更新和外部同步
  const CostCell: React.FC<{
    value: number | null;
    recordId: string;
    department: string;
    disabled?: boolean;
    onInternalUpdate: (id: string, department: string, value: number | null) => void;
    onExternalSync: (id: string, department: string, value: number | null) => void;
  }> = React.memo(({ value, recordId, department, disabled, onInternalUpdate, onExternalSync }) => {
    const [inputValue, setInputValue] = useState<number | null>(value);
    const inputValueRef = useRef<number | null>(value);

    // 当外部 value 变化时，同步更新内部状态
    useEffect(() => {
      setInputValue(value);
      inputValueRef.current = value;
    }, [value]);

    // 更新 ref 当 inputValue 变化时
    useEffect(() => {
      inputValueRef.current = inputValue;
    }, [inputValue]);

    const handleBlur = useCallback(() => {
      if (isReadonly || disabled) return;
      // 失去焦点时同步外部数据
      onExternalSync(recordId, department, inputValueRef.current);
    }, [recordId, department, isReadonly, disabled, onExternalSync]);

    const handleChange = useCallback((val: number | null) => {
      if (isReadonly || disabled) return;
      // 输入时只更新内部状态，不触发外部回调
      setInputValue(val);
      // 同时更新内部数据状态，但不触发 onDataChange
      onInternalUpdate(recordId, department, val);
    }, [recordId, department, isReadonly, disabled, onInternalUpdate]);

    return (
      <InputNumber
        value={inputValue}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder="待输入人月"
        className="w-full text-center"
        size="small"
        disabled={disabled || isReadonly}
        readOnly={isReadonly}
        min={0}
        precision={2}
        step={0.01}
        controls={false}
      />
    );
  }, (prevProps, nextProps) => {
    // 自定义比较函数，只有关键属性变化时才重新渲染
    return (
      prevProps.value === nextProps.value &&
      prevProps.disabled === nextProps.disabled &&
      prevProps.recordId === nextProps.recordId &&
      prevProps.department === nextProps.department &&
      prevProps.onInternalUpdate === nextProps.onInternalUpdate &&
      prevProps.onExternalSync === nextProps.onExternalSync
    );
  });

  // 动态生成部门成本列 - 优化依赖项
  const departmentColumns = useMemo((): ColumnsType<ImplementationCostItem> => {
    return sortedDepartments.map((department) => {
      const departmentSalary = departmentSalaryMap[department] || 3; // 默认3万
      const salaryInWan = (departmentSalary).toFixed(4); // 转换为万元
      return {
        title: (
          <div className="text-center">
            <div>{department}实施成本（人月）</div>
            <div className="text-xs font-normal text-gray-500">
              平均工资：{salaryInWan}万元/月
            </div>
          </div>
        ),
        dataIndex: ["departmentCosts", department],
        key: `department_${department}`,
        width: 180,
        align: "center" as const,
        render: (value: number | null, record: ImplementationCostItem) => (
          <CostCell
            value={value}
            recordId={record.id}
            department={department}
            disabled={!canEditCostField(record, department)}
            onInternalUpdate={updateInternalData}
            onExternalSync={syncExternalData}
          />
        ),
      };
    });
  }, [sortedDepartments, departmentSalaryMap, canEditCostField, updateInternalData, syncExternalData]);

  // 基础列配置
  const baseColumns: ColumnsType<ImplementationCostItem> = [
    {
      title: "序号",
      dataIndex: "index",
      key: "index",
      width: 60,
      align: "center",
      render: (
        value: number,
        record: ImplementationCostItem,
        index: number
      ) => {
        return index + 1;
      },
    },
    {
      title: "产品类别",
      dataIndex: "productCategory",
      key: "productCategory",
      width: 100,
      align: "center",
    },
    {
      title: "产品项目名称",
      dataIndex: "productName",
      key: "productName",
      width: 150,
      align: "center",
    },
    {
      title: "运营服务",
      dataIndex: "operationService",
      key: "operationService",
      width: 150,
      align: "center",
    },
  ];

  // 完整的表格列配置
  const columns: ColumnsType<ImplementationCostItem> = useMemo(() => [
    ...baseColumns,
    ...departmentColumns,
  ], [baseColumns, departmentColumns]);

  // 计算表格滚动宽度
  const scrollWidth = 760 + sortedDepartments.length * 180; // 基础宽度 + 部门列宽度

  // 保存数据（仅在编辑模式下可用）
  const saveData = async () => {
    if (isReadonly) return;
    console.log("保存数据=================", data);
    try {
      setSaving(true);

      // 构建保存数据的请求参数 - 只保存修改过的数据
      const standardPDTOList: Array<{
        lineNum: string;
        data: { KQM21318: number };
        id: string;
        beforeData: { KQM21318: number };
      }> = [];
      // 比较当前数据和原始数据，找出修改过的项
      data.forEach((currentItem) => {
        const originalItem = originalData.find(
          (orig) => orig.id === currentItem.id
        );
        if (!originalItem) return;

        // 比较每个部门的成本
        Object.entries(currentItem.departmentCosts).forEach(
          ([department, currentCost]) => {
            const originalCost = originalItem.departmentCosts[department]??null;

            // 如果成本发生了变化，添加到保存列表
            if (currentCost !== originalCost) {
              // 查找对应的原始API数据项
              const relatedRawItems = rawApiData.filter((rawItem) => {
                const rawDepartment = rawItem.data.KQM21341?.text || "";
                // 根据分组逻辑找到相关的原始数据项
                const productNameId = rawItem.data.KQM21313?.value || "";
                const operationServiceId = rawItem.data.KQM21315?.value || "";

                let groupKey: string;
                if (operationServiceId && operationServiceId.trim() !== "") {
                  groupKey = `operation_${operationServiceId}`;
                } else if (productNameId && productNameId.trim() !== "") {
                  groupKey = `product_${productNameId}`;
                } else {
                  return false;
                }

                return (
                  groupKey === currentItem.groupKey &&
                  rawDepartment === department
                );
              });

              // 为每个相关的原始数据项创建保存记录
              relatedRawItems.forEach((rawItem) => {
                const originalRawCost =
                  parseFloat(rawItem.data.KQM21318?.value || "0") || 0;

                standardPDTOList.push({
                  lineNum: rawItem.id, // 使用原始API数据的真实ID
                  data: {
                    KQM21318: currentCost ?? 0, // 如果为null则使用0
                  },
                  id: rawItem.id, // 使用原始API数据的真实ID
                  beforeData: {
                    KQM21318: originalRawCost, // 使用原始API数据的成本值
                  },
                });
              });
            }
          }
        );
      });

      // if (standardPDTOList.length === 0) {
      //   message.info("没有数据需要保存");
      //   return;
      // }

      console.log("准备保存的数据:", standardPDTOList);

      const result = await saveListData({
        standardPDTOList,
      });

      console.log("保存结果:", result);

      // 保存成功后更新原始数据状态
      setOriginalData(JSON.parse(JSON.stringify(data)));
      message.success(`成功保存 ${standardPDTOList.length} 项数据`);
    } catch (error) {
      console.error("保存数据失败:", error);
      message.error("数据保存失败，请重试");
    } finally {
      setSaving(false);
    }
  };

  // 计算所有部门成本的总和（按对应部门工资计算）
  const totalCost = useMemo(() => {
    return Object.entries(totalCosts.departmentTotals).reduce(
      (acc, [department, personMonths]) => {
        const departmentSalary = departmentSalaryMap[department] || 3; // 默认3万
        const costInWan = (personMonths * departmentSalary) ; // 转换为万元
        return acc + costInWan;
      },
      0
    );
  }, [totalCosts, departmentSalaryMap]);

  // 检查是否有可编辑的字段
  const hasEditableFields = useMemo(() => {
    if (isReadonly) return false;

    // 遍历所有数据项和所有部门，检查是否有可编辑的字段
    for (const item of data) {
      for (const department of allDepartments) {
        if (canEditCostField(item, department)) {
          return true;
        }
      }
    }
    return false;
  }, [data, allDepartments, isReadonly, canEditCostField]);

  return (
    <div className={`bg-white rounded-lg shadow-sm ${className || ""}`}>
      <Space>
        <Input value={totalCost.toFixed(4)} disabled />
        <span> 自动累计（万元）</span>
      </Space>
      <div className="flex justify-between items-center p-4 border-b border-gray-200">
        <div className="text-sm text-gray-500">
          由各部总监评估实施成本，按各部门平均人月薪资计算
          {isReadonly ? "（只读模式）" : ""}
        </div>
        {/* 只在编辑模式下且有可编辑字段时显示保存按钮 */}
        {!isReadonly && hasEditableFields && (
          <Button type="primary" onClick={saveData} loading={saving}>
            保存实施成本
          </Button>
        )}
      </div>

      {/* 主表格 */}
      <div className="p-4">
        <Table<ImplementationCostItem>
          columns={columns}
          dataSource={data}
          pagination={false}
          size="small"
          bordered
          rowKey="id"
          scroll={{ x: scrollWidth }}
          loading={loading}
          summary={() => (
            <Table.Summary fixed>
              <Table.Summary.Row className="font-semibold bg-blue-50">
                <Table.Summary.Cell index={0} align="center">
                  {data.length + 1}
                </Table.Summary.Cell>
                <Table.Summary.Cell
                  index={1}
                  align="center"
                ></Table.Summary.Cell>
                <Table.Summary.Cell
                  index={2}
                  align="center"
                ></Table.Summary.Cell>
                <Table.Summary.Cell index={3} align="center">
                  <span className="font-semibold text-blue-600">
                    合计（万元）
                  </span>
                </Table.Summary.Cell>
                {sortedDepartments.map((department, index) => {
                  const personMonths =
                    totalCosts.departmentTotals[department] || 0;
                  const departmentSalary =
                    departmentSalaryMap[department] || 3; // 默认3万
                  const costInWan = (personMonths * departmentSalary) ; // 转换为万元

                  return (
                    <Table.Summary.Cell
                      key={department}
                      index={4 + index}
                      align="center"
                    >
                      <span className="font-semibold text-blue-600">
                        {costInWan.toFixed(4)}
                      </span>
                    </Table.Summary.Cell>
                  );
                })}
              </Table.Summary.Row>
            </Table.Summary>
          )}
        />
      </div>
    </div>
  );
};

export default ImplementationCosts;
