import React, { useState, useEffect } from "react";
import { getStandardListData, StandardListParams } from "../../api";
import styles from "./index.less";

const List = (props: any) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // 获取列表数据的方法
  const fetchListData = async (params?: StandardListParams) => {
    setLoading(true);
    setError(null);

    try {
      const result = await getStandardListData(params);
      setData(result);
      console.log("获取到的数据:", result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "获取数据失败";
      setError(errorMessage);
      console.error("获取数据失败:", err);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchListData();
  }, []);

  // 重新获取数据的方法
  const handleRefresh = () => {
    fetchListData();
  };

  // 自定义参数获取数据的方法
  const handleCustomFetch = () => {
    const customParams: StandardListParams = {
      pageNo: 1,
      pageSize: 10,
      // 可以根据需要修改其他参数
    };
    fetchListData(customParams);
  };

  return (
    <div className={styles.container}>
      
      <div style={{ padding: "20px" }}>
        <h2>标准列表数据</h2>

        <div style={{ marginBottom: "20px" }}>
          <button
            onClick={handleRefresh}
            disabled={loading}
            style={{ marginRight: "10px", padding: "8px 16px" }}
          >
            {loading ? "加载中..." : "刷新数据"}
          </button>

          <button
            onClick={handleCustomFetch}
            disabled={loading}
            style={{ padding: "8px 16px" }}
          >
            自定义参数获取
          </button>
        </div>

        {loading && <div style={{ color: "#1890ff" }}>正在加载数据...</div>}

        {error && (
          <div style={{ color: "#ff4d4f", marginBottom: "20px" }}>
            错误: {error}
          </div>
        )}

        {data && (
          <div>
            <h3>数据内容:</h3>
            <pre
              style={{
                background: "#f5f5f5",
                padding: "16px",
                borderRadius: "4px",
                overflow: "auto",
                maxHeight: "400px",
              }}
            >
              {JSON.stringify(data, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default List;
