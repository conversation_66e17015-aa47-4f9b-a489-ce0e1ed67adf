import React from "react";
import { createHashRouter } from "react-router-dom";
import BasicLayout from "@/layouts/BasicLayout";
import NoMatch from "@/components/NoMatch";
import List from "@/pages/List";
import View from "@/pages/View";
import Home from "@/pages/Home";
import ImplementationCosts from "@/pages/ImplementationCosts";
import Readonly from "./pages/ImplementationCosts/readonly";
import New from "./pages/New";
import WorkHoursStatistics from "./pages/WorkHoursStatistics";
import ContractManagement from "./pages/ContractManagement/page";
import ContractDetailPage from "./pages/ContractManagement/detail";
import ContractConfirmPage from "@/pages/ContractManagement/confirm";
import PaymentList from "./pages/PaymentList";
import PaymentDetail from "./pages/PaymentList/detail";
import InvoiceSubmit from "./pages/PaymentList/submit";
import PaymentOut from "./pages/PaymentOut";
import PaymentOutDetail from "./pages/PaymentOut/detail";
import UploadReceipt from "./pages/PaymentOut/upload";

/**
 * 路由元数据接口
 */
export interface RouteMetadata {
  title: string;
  icon?: string;
  hidden?: boolean;
}

/**
 * 扩展的路由配置接口
 */
export interface ExtendedRouteConfig {
  path?: string;
  element?: React.ReactElement;
  index?: boolean;
  children?: ExtendedRouteConfig[];
  meta?: RouteMetadata;
}

/**
 * 带有中文配置的路由数据
 */
export const routeConfigs: ExtendedRouteConfig[] = [
  {
    path: "/",
    element: <BasicLayout />,
    children: [
      {
        index: true,
        element: <Home />,
        meta: {
          title: "首页",
          icon: "HomeOutlined",
        },
      },
      {
        path: "/new",
        element: <New />,
        meta: {
          title: "新建",
          icon: "PlusOutlined",
        },
      },
      {
        path: "/list",
        element: <List />,
        meta: {
          title: "列表",
          icon: "UnorderedListOutlined",
        },
      },
      {
        path: "/view",
        element: <View />,
        meta: {
          title: "查看",
          icon: "EyeOutlined",
        },
      },
      {
        path: "/implementation-costs",
        element: <ImplementationCosts />,
        meta: {
          title: "实施成本",
          icon: "DollarOutlined",
        },
      },
      {
        path: "/implementation-costs-readonly",
        element: <Readonly />,
        meta: {
          title: "实施成本(只读)",
          icon: "DollarOutlined",
          hidden: true,
        },
      },
      {
        path: "/work-hours-statistics",
        element: <WorkHoursStatistics />,
        meta: {
          title: "工时统计",
          icon: "ClockCircleOutlined",
        },
      },
      {
        path: "/contract-management",
        element: <ContractManagement />,
        meta: { title: "合同管理", icon: "FileTextOutlined", },
      },
      {
        path: "/contract-management/:contractId",
        element: <ContractDetailPage />,
        meta: { title: "合同详情", icon: "FileTextOutlined", hidden: true, },
      },
      {
        // 确认签约
        path: "/contract-management/:contractId/confirm",
        element: <ContractConfirmPage />,
        meta: { title: "确认签约", icon: "FileTextOutlined", },
      },
      {
        // 回款列表
        path: "/payment-list",
        element: <PaymentList />,
        meta: { title: "回款列表", icon: "FileTextOutlined", },
      },
      {
        // 发票详情
        path: "/payment-list/:paymentId",
        element: <PaymentDetail />,
        meta: { title: "发票详情", icon: "FileTextOutlined", },
      },
      {
        // 提交回款
        path: "/payment-list/:paymentId/submit",
        element: <InvoiceSubmit />,
        meta: { title: "提交回款", icon: "FileTextOutlined", },
      },
      {
        // 付款列表
        path: "/payment-out",
        element: <PaymentOut />,
        meta: { title: "付款列表", icon: "AccountBookOutlined", },
      },
      {
        // 付款详情
        path: "/payment-out/:paymentOutId",
        element: <PaymentOutDetail />,
        meta: { title: "付款详情", icon: "AccountBookOutlined", hidden: true, },
      },
      {
        // 上传回单
        path: "/payment-out/:paymentOutId/upload",
        element: <UploadReceipt />,
        meta: { title: "上传回单", icon: "UploadOutlined", hidden: true, },
      },
    ],
  },
  {
    path: "*",
    element: <NoMatch />,
    meta: {
      title: "页面未找到",
      hidden: true,
    },
  },
];



/**
 * 创建路由实例
 * @param basename 路由基础路径
 * @returns 路由实例
 */
const initRouter = (basename?: string) => {
  // 将扩展路由配置转换为标准路由配置
  const convertToStandardRoutes = (routes: ExtendedRouteConfig[]): any[] => {
    return routes.map(route => {
      const { meta, ...standardRoute } = route;
      if (route.children) {
        return {
          ...standardRoute,
          children: convertToStandardRoutes(route.children),
        };
      }
      return standardRoute;
    });
  };

  return createHashRouter(
    convertToStandardRoutes(routeConfigs),
    {
      basename,
    }
  );
};

export default initRouter;
