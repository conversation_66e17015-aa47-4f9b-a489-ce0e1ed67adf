// API基础配置
// const BASE_URL = 'https://pms.tongbaninfo.com:8888/app/api';
const BASE_URL = "/app/api";

// 请求头配置
const getHeaders = () => ({
  Accept: "application/json, text/plain, */*",
  "Accept-Language": "zh-CN,zh;q=0.9",
  Connection: "keep-alive",
  "Content-Type": "application/json",
  Origin: "https://pms.tongbaninfo.com:8888",
  Referer: "https://pms.tongbaninfo.com:8888/app",
  "Sec-Fetch-Dest": "empty",
  "Sec-Fetch-Mode": "cors",
  "Sec-Fetch-Site": "same-origin",
  "User-Agent":
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
  _lang: "zh_C<PERSON>",
  "sec-ch-ua":
    '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
  "sec-ch-ua-mobile": "?0",
  "sec-ch-ua-platform": '"macOS"',
  sw6: "1-MS42MDY2MDc0NTcuMTc0ODEwMDQ2MzM0MDAwMDE=-MS42MDY2MDc0NTcuMTc0ODEwMDQ2MzM0MDAwMDE=-0-1-1-IzEyNy4wLjAuMTo4MA==-LTE=-LTE=",
  cookie: "_currency=null; cp-hub-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjJmNWNmZDk1LTY1ZTUtMTFlYi1iM2M3LTAyMjZjYTViMThhYSIsInVzZXJuYW1lIjoiYWRtaW4iLCJlbWFpbCI6IiIsImV4cCI6NjE3NDcxMjg1NjcsImlhdCI6MTc0NzEyODYyN30.LK4YFgg89pjJ4FLdUc-W2LLUBS98SUlTo3ksm7fAY2k; envList=[{%22userTag%22:%22beta%22%2C%22feTag%22:%22beta%22%2C%22id%22:%22y31b3vs2b%22}]; _timezone=UTC%2B08%3A00; tokenId=ebd37bd24dbacb55dfcdf34c528dff48; _lang=zh_CN; tongbaninfo.com_watch_currentTime=2025-05-24T23:27:43+08:00",
});



// 标准列表数据请求参数接口
export interface StandardListParams {
  requestType?: string;
  layoutId?: string;
  resourceType?: string;
  resourceId?: string;
  standardCollectionId?: string;
  pageNo?: number;
  pageSize?: number;
  tempFilterBoxDataMap?: any;
  showModelType?: string;
  filterBoxDataMap?: {
    columnFilterObj?: {
      expression?: string;
      conditions?: Array<{
        fieldCode?: string;
        fieldType?: string;
        fieldName?: string;
        operator?: string;
        text?: any;
        value?: any;
        valueName?: string;
        type?: any;
        serialNumber?: number;
      }>;
      isAdvanced?: boolean;
    };
    filterObj?: any;
    listViewFilterObj?: any;
    searchFilterObj?: any;
    viewId?: string;
  };
  needToRecordLastViewId?: boolean;
  lastViewId?: string;
  filterBoxData?: any;
  _crumb?: string;
}

// 获取标准列表数据
export const getStandardListData = async (params: StandardListParams = {}) => {
  const defaultParams: StandardListParams = {
    requestType: "code",
    layoutId: "coordinate_KQM4399",
    resourceType: "menu",
    resourceId: "KQM1734",
    standardCollectionId: "KQM1741",
    pageNo: 1,
    pageSize: 15,
    tempFilterBoxDataMap: null,
    showModelType: "list",
    filterBoxDataMap: {
      columnFilterObj: {
        expression: "1",
        conditions: [
          {
            fieldCode: "KQM17988",
            fieldType: "IDRELATIONSHIP",
            fieldName: "创建人",
            operator: "CONTAIN",
            text: {
              icon: [null],
              text: ["李会明"],
              value: ["KQM989"],
              positionStatus: [null],
            },
            value: ["KQM989"],
            valueName: "李会明",
            type: null,
            serialNumber: 1,
          },
        ],
        isAdvanced: true,
      },
      filterObj: null,
      listViewFilterObj: null,
      searchFilterObj: null,
      viewId: "all_KQM1741",
    },
    needToRecordLastViewId: true,
    lastViewId: "all_KQM1741",
    filterBoxData: null,
    _crumb: "********************************",
  };

  // 合并默认参数和传入参数
  const finalParams = { ...defaultParams, ...params };

  try {
    const response = await fetch(`${BASE_URL}/standardList/front/getListData`, {
      method: "POST",
      headers: {
        ...getHeaders(),
      },
      body: JSON.stringify(finalParams),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("获取标准列表数据失败:", error);
    throw error;
  }
};

// 商机小组列表请求参数接口
export interface OpportunityGroupListParams {
  fieldCode: string; // 动态字段代码
  pageNo?: number;
  pageSize?: number;
}

// 获取商机小组列表数据
export const getOpportunityGroupListData = async (params: OpportunityGroupListParams) => {
  const { fieldCode, pageNo = 1, pageSize = 500 } = params;

  const requestData = {
    requestType: "code",
    layoutId: "coordinate_KQM4828",
    resourceType: "menu",
    resourceId: "KQM2060",
    standardCollectionId: "KQM1790",
    pageNo,
    pageSize,
    tempFilterBoxDataMap: null,
    showModelType: "list",
    filterBoxDataMap: {
      columnFilterObj: {
        expression: "1",
        conditions: [
          {
            "fieldCode": "KQM21343",
            fieldType: "MULTITOONE",
            fieldName: "所属商机小组",
            operator: "CONTAIN",
            text: {
              icon: [null],
              value: [fieldCode],
              positionStatus: [null],
            },
            value: [fieldCode],
            type: null,
            serialNumber: 1,
          },
        ],
        isAdvanced: true,
      },
      filterObj: null,
      listViewFilterObj: null,
      searchFilterObj: null,
      viewId: "all_KQM1790",
    },
    needToRecordLastViewId: true,
    lastViewId: "all_KQM1790",
    filterBoxData: null,
    _crumb: "********************************",
  };

  try {
    const response = await fetch(`${BASE_URL}/standardList/front/getListData`, {
      method: "POST",
      headers: {
        ...getHeaders(),
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("获取商机小组列表数据失败:", error);
    throw error;
  }
};

// 保存列表数据请求参数接口
export interface SaveListDataParams {
  requestType?: string;
  layoutId?: string;
  resourceType?: string;
  resourceId?: string;
  standardCollectionId?: string;
  pageType?: string;
  apiName?: string;
  deviceType?: string;
  standardPDTOList: Array<{
    lineNum: string; // 数据ID
    data: {
      KQM21318: number; // 固定字段，值为当前输入框的值
    };
    id: string; // 数据ID
    beforeData: {
      KQM21318: number; // 修改前的值
    };
  }>;
  _crumb?: string;
}

// 保存列表数据
export const saveListData = async (params: SaveListDataParams) => {
  const defaultParams = {
    requestType: "code",
    layoutId: "coordinate_KQM4828",
    resourceType: "button",
    resourceId: "KQM2060",
    standardCollectionId: "KQM1790",
    pageType: "listRowEdit",
    apiName: "listRowEdit",
    deviceType: "web",
    _crumb: "********************************",
  };

  // 合并默认参数和传入参数
  const finalParams = { ...defaultParams, ...params };

  try {
    const response = await fetch(`${BASE_URL}/standardWrite/front/standard/saveListData`, {
      method: "POST",
      headers: {
        ...getHeaders(),
      },
      body: JSON.stringify(finalParams),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("保存列表数据失败:", error);
    throw error;
  }
};

// 获取用户信息请求参数接口
export interface GetUserInfoParams {
  enterpriseId?: string;
  _crumb?: string;
}

// 获取用户信息
export const getUserInfo = async (params: GetUserInfoParams = {}) => {
  const { 
    enterpriseId = "HW71", 
    _crumb = "********************************" 
  } = params;

  const url = `${BASE_URL}/eadmin/personal/info?enterpriseId=${enterpriseId}&_crumb=${_crumb}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        ...getHeaders(),
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("获取用户信息失败:", error);
    throw error;
  }
};

// 部门平均工资数据请求参数接口
export interface DepartmentSalaryParams {
  requestType?: string;
  layoutId?: string;
  resourceType?: string;
  resourceId?: string;
  standardCollectionId?: string;
  pageNo?: number;
  pageSize?: number;
  tempFilterBoxDataMap?: any;
  showModelType?: string;
  filterBoxDataMap?: {
    columnFilterObj?: {
      expression?: string;
      conditions?: Array<{
        fieldCode?: string;
        fieldType?: string;
        fieldName?: string;
        operator?: string;
        text?: any;
        value?: any;
        valueName?: string;
        type?: any;
        serialNumber?: number;
      }>;
      isAdvanced?: boolean;
    };
    filterObj?: any;
    listViewFilterObj?: any;
    searchFilterObj?: any;
    viewId?: string;
  };
  needToRecordLastViewId?: boolean;
  lastViewId?: string;
  filterBoxData?: any;
  _crumb?: string;
}

// 获取部门平均工资数据
export const getDepartmentSalaryData = async (params: DepartmentSalaryParams = {}) => {
  const defaultParams: DepartmentSalaryParams = {
    requestType: "code",
    layoutId: "coordinate_KQM3961",
    resourceType: "menu",
    resourceId: "KQM1674",
    standardCollectionId: "KQM1689",
    pageNo: 1,
    pageSize: 50,
    tempFilterBoxDataMap: null,
    showModelType: "list",
    filterBoxDataMap: {
      columnFilterObj: {
        expression: "1",
        conditions: [
          {
            fieldCode: "KQM17695",
            fieldType: "MULTITOONE",
            fieldName: "所属指标模板",
            operator: "CONTAIN",
            text: {
              positionStatus: [null],
              icon: [null],
              text: ["部门平均工资"],
              value: ["KQM3086034"],
            },
            value: ["KQM3086034"],
            valueName: "部门平均工资",
            type: null,
            serialNumber: 1,
          },
        ],
        isAdvanced: true,
      },
      filterObj: null,
      listViewFilterObj: null,
      searchFilterObj: null,
      viewId: "all_KQM1689",
    },
    needToRecordLastViewId: true,
    lastViewId: "all_KQM1689",
    filterBoxData: null,
    _crumb: "********************************",
  };

  // 合并默认参数和传入参数
  const finalParams = { ...defaultParams, ...params };

  try {
    const response = await fetch(`${BASE_URL}/standardList/front/getListData`, {
      method: "POST",
      headers: {
        ...getHeaders(),
      },
      body: JSON.stringify(finalParams),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("获取部门平均工资数据失败:", error);
    throw error;
  }
};

// 工时统计筛选条件接口
export interface WorkHoursFilterConditions {
  departments?: Array<{ text: string; value: string }>; // 部门筛选
  employees?: Array<{ text: string; value: string }>; // 员工筛选
  workTypes?: Array<{ text: string; value: string }>; // 工时类型筛选（按值）
  approvalStatuses?: Array<{ text: string; value: string }>; // 审批状态筛选
  opportunityProjects?: Array<{ text: string; value: string }>; // 商机项目筛选
  productProjects?: Array<{ text: string; value: string }>; // 产品项目筛选
  consultingTasks?: Array<{ text: string; value: string }>; // 咨询任务筛选
  dateRange?: [string, string]; // 日期范围筛选
}

// 构建工时统计筛选条件
const buildWorkHoursFilterConditions = (filters: WorkHoursFilterConditions) => {
  const conditions: any[] = [];
  let serialNumber = 1;

  // 部门筛选 - MULTITOONE类型
  if (filters.departments && filters.departments.length > 0) {
    conditions.push({
      fieldCode: "KQM19098",
      fieldType: "MULTITOONE",
      fieldName: "部门",
      operator: "CONTAIN",
      text: {
        icon: filters.departments.map(() => null),
        text: filters.departments.map(d => d.text),
        value: filters.departments.map(d => d.value),
        positionStatus: filters.departments.map(() => null),
      },
      value: filters.departments.map(d => d.value),
      valueName: filters.departments.map(d => d.text).join(","),
      type: null,
      serialNumber: serialNumber++,
    });
  }

  // 员工筛选 - MULTITOONE类型
  if (filters.employees && filters.employees.length > 0) {
    conditions.push({
      fieldCode: "KQM19036",
      fieldType: "MULTITOONE",
      fieldName: "填写人员",
      operator: "CONTAIN",
      text: {
        icon: filters.employees.map(() => null),
        text: filters.employees.map(e => e.text),
        value: filters.employees.map(e => e.value),
        positionStatus: filters.employees.map(() => null),
      },
      value: filters.employees.map(e => e.value),
      valueName: filters.employees.map(e => e.text).join(","),
      type: null,
      serialNumber: serialNumber++,
    });
  }

  // 工时类型筛选 - RADIO类型
  if (filters.workTypes && filters.workTypes.length > 0) {
    // 对于RADIO类型，通常只支持单选，但这里我们处理多个条件
    filters.workTypes.forEach(workType => {
      conditions.push({
        fieldCode: "HW23543",
        fieldType: "RADIO",
        fieldName: "工时类型",
        operator: "CONTAIN",
        text: {
          dataConfigMap: { masterStyle: {} },
          text: [workType.text],
          value: [workType.value],
        },
        value: [workType.value],
        valueName: workType.text,
        type: null,
        serialNumber: serialNumber++,
      });
    });
  }

  // 审批状态筛选 - RADIO类型
  if (filters.approvalStatuses && filters.approvalStatuses.length > 0) {
    // 对于RADIO类型，通常只支持单选，但这里我们处理多个条件
    filters.approvalStatuses.forEach(status => {
      conditions.push({
        fieldCode: "HW23575",
        fieldType: "RADIO",
        fieldName: "审批状态",
        operator: "CONTAIN",
        text: {
          dataConfigMap: { masterStyle: {} },
          text: [status.text],
          value: [status.value],
        },
        value: [status.value],
        valueName: status.text,
        type: null,
        serialNumber: serialNumber++,
      });
    });
  }

  // 商机项目筛选 - MULTITOONE类型
  if (filters.opportunityProjects && filters.opportunityProjects.length > 0) {
    conditions.push({
      fieldCode: "HW23572",
      fieldType: "MULTITOONE",
      fieldName: "所属商机项目",
      operator: "CONTAIN",
      text: {
        icon: filters.opportunityProjects.map(() => null),
        text: filters.opportunityProjects.map(p => p.text),
        value: filters.opportunityProjects.map(p => p.value),
        positionStatus: filters.opportunityProjects.map(() => null),
      },
      value: filters.opportunityProjects.map(p => p.value),
      valueName: filters.opportunityProjects.map(p => p.text).join(","),
      type: null,
      serialNumber: serialNumber++,
    });
  }

  // 产品项目筛选 - MULTITOONE类型
  if (filters.productProjects && filters.productProjects.length > 0) {
    conditions.push({
      fieldCode: "KQM18553",
      fieldType: "MULTITOONE",
      fieldName: "产品项目",
      operator: "CONTAIN",
      text: {
        icon: filters.productProjects.map(() => null),
        text: filters.productProjects.map(p => p.text),
        value: filters.productProjects.map(p => p.value),
        positionStatus: filters.productProjects.map(() => null),
      },
      value: filters.productProjects.map(p => p.value),
      valueName: filters.productProjects.map(p => p.text).join(","),
      type: null,
      serialNumber: serialNumber++,
    });
  }

  // 咨询任务筛选 - MULTITOONE类型
  if (filters.consultingTasks && filters.consultingTasks.length > 0) {
    conditions.push({
      fieldCode: "KQM20885",
      fieldType: "MULTITOONE",
      fieldName: "咨询任务",
      operator: "CONTAIN",
      text: {
        icon: filters.consultingTasks.map(() => null),
        text: filters.consultingTasks.map(t => t.text),
        value: filters.consultingTasks.map(t => t.value),
        positionStatus: filters.consultingTasks.map(() => null),
      },
      value: filters.consultingTasks.map(t => t.value),
      valueName: filters.consultingTasks.map(t => t.text).join(","),
      type: null,
      serialNumber: serialNumber++,
    });
  }

  // 日期范围筛选
  if (filters.dateRange && filters.dateRange.length === 2) {
    conditions.push({
      fieldCode: "HW21967",
      fieldType: "DATE",
      fieldName: "工时日期",
      operator: "BETWEEN",
      text: {
        startDate: filters.dateRange[0],
        endDate: filters.dateRange[1],
      },
      value: {
        startDate: filters.dateRange[0],
        endDate: filters.dateRange[1],
      },
      type: null,
      serialNumber: serialNumber++,
    });
  }

  return conditions;
};

// 获取带筛选条件的工时统计数据
export const getWorkHoursStatisticsDataWithFilters = async (
  filters: WorkHoursFilterConditions = {},
  params: WorkHoursStatisticsParams = {}
) => {
  const conditions = buildWorkHoursFilterConditions(filters);
  
  // 构建表达式：对于同类型的多个条件使用OR连接，不同类型之间使用AND连接
  let expression = "1";
  if (conditions.length > 0) {
    expression = conditions.map((_, index) => index + 1).join(" AND ");
    
    // 如果有多个工时类型条件，需要用OR连接
    const workTypeConditions = conditions.filter(c => c.fieldCode === "HW23543");
    const approvalStatusConditions = conditions.filter(c => c.fieldCode === "HW23575");
    
    if (workTypeConditions.length > 1 || approvalStatusConditions.length > 1) {
      // 重新构建表达式
      const groups: string[] = [];
      let currentIndex = 1;
      
      // 处理非RADIO类型的条件
      const nonRadioConditions = conditions.filter(c => c.fieldType !== "RADIO");
      nonRadioConditions.forEach(() => {
        groups.push(currentIndex.toString());
        currentIndex++;
      });
      
      // 处理工时类型条件（RADIO类型）
      if (workTypeConditions.length > 0) {
        const workTypeGroup = [];
        for (let i = 0; i < workTypeConditions.length; i++) {
          workTypeGroup.push(currentIndex.toString());
          currentIndex++;
        }
        if (workTypeGroup.length > 1) {
          groups.push(`(${workTypeGroup.join(" OR ")})`);
        } else {
          groups.push(workTypeGroup[0]);
        }
      }
      
      // 处理审批状态条件（RADIO类型）
      if (approvalStatusConditions.length > 0) {
        const statusGroup = [];
        for (let i = 0; i < approvalStatusConditions.length; i++) {
          statusGroup.push(currentIndex.toString());
          currentIndex++;
        }
        if (statusGroup.length > 1) {
          groups.push(`(${statusGroup.join(" OR ")})`);
        } else {
          groups.push(statusGroup[0]);
        }
      }
      
      expression = groups.join(" AND ");
    }
  }
  
  const defaultParams: WorkHoursStatisticsParams = {
    requestType: "code",
    layoutId: "coordinate_HW4218",
    resourceType: "menu",
    resourceId: "HW929",
    standardCollectionId: "HW760",
    pageNo: 1,
    pageSize: 1000, // 增加页面大小以获取更多数据
    tempFilterBoxDataMap: null,
    showModelType: "list",
    filterBoxDataMap: {
      columnFilterObj: conditions.length > 0 ? {
        expression,
        conditions,
        isAdvanced: true,
      } : null,
      filterObj: null,
      listViewFilterObj: null,
      searchFilterObj: null,
      viewId: "all_HW760",
    },
    needToRecordLastViewId: true,
    lastViewId: "all_HW760",
    filterBoxData: null,
    _crumb: "********************************",
  };

  // 合并默认参数和传入参数
  const finalParams = { ...defaultParams, ...params };

  try {
    const response = await fetch(`${BASE_URL}/standardList/front/getListData`, {
      method: "POST",
      headers: {
        ...getHeaders(),
      },
      body: JSON.stringify(finalParams),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("获取工时统计数据失败:", error);
    throw error;
  }
};

// 工时统计数据请求参数接口
export interface WorkHoursStatisticsParams {
  requestType?: string;
  layoutId?: string;
  resourceType?: string;
  resourceId?: string;
  standardCollectionId?: string;
  pageNo?: number;
  pageSize?: number;
  tempFilterBoxDataMap?: any;
  showModelType?: string;
  filterBoxDataMap?: {
    columnFilterObj?: any;
    filterObj?: any;
    listViewFilterObj?: any;
    searchFilterObj?: any;
    viewId?: string;
  };
  needToRecordLastViewId?: boolean;
  lastViewId?: string;
  filterBoxData?: any;
  _crumb?: string;
}

// 获取工时统计数据
export const getWorkHoursStatisticsData = async (params: WorkHoursStatisticsParams = {}) => {
  const defaultParams: WorkHoursStatisticsParams = {
    requestType: "code",
    layoutId: "coordinate_HW4218",
    resourceType: "menu",
    resourceId: "HW929",
    standardCollectionId: "HW760",
    pageNo: 1,
    pageSize: 500,
    tempFilterBoxDataMap: null,
    showModelType: "list",
    filterBoxDataMap: {
      columnFilterObj: null,
      filterObj: null,
      listViewFilterObj: null,
      searchFilterObj: null,
      viewId: "all_HW760",
    },
    needToRecordLastViewId: true,
    lastViewId: "all_HW760",
    filterBoxData: null,
    _crumb: "********************************",
  };

  // 合并默认参数和传入参数
  const finalParams = { ...defaultParams, ...params };

  try {
    const response = await fetch(`${BASE_URL}/standardList/front/getListData`, {
      method: "POST",
      headers: {
        ...getHeaders(),
      },
      body: JSON.stringify(finalParams),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("获取工时统计数据失败:", error);
    throw error;
  }
};

// 获取字段选项的接口参数
export interface GetFieldOptionsParams {
  keyword?: string;
  pageNo?: number;
  pageSize?: number;
  standardCollectionId?: string;
  parentMetadataId?: string;
  resourceType?: string;
  requestType?: string;
  queryFieldId: string;
  deviceType?: string;
  parentLayoutId?: string;
  _crumb?: string;
}

// 获取主数据选项的接口参数
export interface GetMasterDataOptionsParams {
  pageNo?: number;
  pageSize?: number;
  apiId: string;
  standardCollectionId?: string;
  fieldId: string;
  _crumb?: string;
}

// 获取字段选项数据
export const getFieldOptions = async (params: GetFieldOptionsParams) => {
  const defaultParams: GetFieldOptionsParams = {
    keyword: "",
    pageNo: 1,
    pageSize: params.pageSize || 500,
    standardCollectionId: "",
    parentMetadataId: "HW760",
    resourceType: "standard",
    requestType: "code",
    queryFieldId: params.queryFieldId,
    deviceType: "web",
    parentLayoutId: "coordinate_HW4218",
    _crumb: "********************************",
  };

  const finalParams = { ...defaultParams, ...params };

  try {
    const response = await fetch(`${BASE_URL}/standardList/front/getSimpleSearchListDataBySetField`, {
      method: "POST",
      headers: {
        ...getHeaders(),
      },
      body: JSON.stringify(finalParams),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("获取字段选项失败:", error);
    throw error;
  }
};

// 获取主数据选项
export const getMasterDataOptions = async (params: GetMasterDataOptionsParams) => {
  const defaultParams: GetMasterDataOptionsParams = {
    pageNo: 1,
    pageSize: 100,
    apiId: params.apiId,
    standardCollectionId: "HW760",
    fieldId: params.fieldId,
    _crumb: "********************************",
  };

  const finalParams = { ...defaultParams, ...params };

  try {
    const response = await fetch(`${BASE_URL}/masterData/front/queryPage`, {
      method: "POST",
      headers: {
        ...getHeaders(),
      },
      body: JSON.stringify(finalParams),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("获取主数据选项失败:", error);
    throw error;
  }
};

// 获取部门选项
export const getDepartmentOptions = () => {
  return getFieldOptions({
    queryFieldId: "KQM19098",
    standardCollectionId: "KQM1700",
  });
};

// 获取员工选项
export const getEmployeeOptions = () => {
  return getFieldOptions({
    queryFieldId: "KQM19036",
    pageSize: 1000,
    standardCollectionId: "KQM1696", // 需要确认正确的standardCollectionId
  });
};

// 获取工时类型选项
export const getWorkTypeOptions = () => {
  return getMasterDataOptions({
    apiId: "HW2373",
    fieldId: "HW23543",
  });
};

// 获取审批状态选项
export const getApprovalStatusOptions = () => {
  return getMasterDataOptions({
    apiId: "HW2375",
    fieldId: "HW23575",
  });
};

// 获取产品项目选项
export const getProductProjectOptions = () => {
  return getFieldOptions({
    queryFieldId: "KQM18553",
    standardCollectionId: "KQM1744",
  });
};

// 获取咨询任务选项
export const getConsultingTaskOptions = () => {
  return getFieldOptions({
    queryFieldId: "KQM20885",
    standardCollectionId: "KQM1892",
  });
};

// 获取商机项目选项
export const getOpportunityProjectOptions = () => {
  return getFieldOptions({
    queryFieldId: "KQM17348",
    standardCollectionId: "KQM1658",
  });
};

// 获取合同项目选项
export const getContractProjectOptions = () => {
  return getFieldOptions({
    queryFieldId: "KQM19356",
    standardCollectionId: "", // 需要确认正确的standardCollectionId
  });
};

// 导出所有API方法
export default {
  getStandardListData,
  getOpportunityGroupListData,
  saveListData,
  getUserInfo,
  getDepartmentSalaryData,
  getWorkHoursStatisticsData,
  getWorkHoursStatisticsDataWithFilters,
  getFieldOptions,
  getMasterDataOptions,
  getDepartmentOptions,
  getEmployeeOptions,
  getWorkTypeOptions,
  getApprovalStatusOptions,
  getProductProjectOptions,
  getConsultingTaskOptions,
  getOpportunityProjectOptions,
  getContractProjectOptions,
};
