// 实施成本项目字段映射表
export const FIELD_CODE_MAPPING = {
  // 基础信息字段
  KQM20760: "sequence", // 序号
  KQM21312: "productCategory", // 产品类别
  KQM21313: "productName", // 产品项目名称
  KQM21315: "operationService", // 运营服务
  KQM21341: "department", // 部门
  KQM21319: "assessUserId", // 评估人

  // 成本字段 - 根据部门映射到不同的成本类型
  KQM21318: "consultingCost", // 人月成本
  // 质量保障费和交付实施成本需要根据部门来判断
} as const;

// 数据转换接口
export type ApiDataItem = {
  id: string;
  data: {
    [key: string]: {
      text: string | null;
      value: string | null;
    };
  };
  children: null;
};

export interface ProcessedCostItem {
  id: string;
  sequence: number;
  productCategory: string;
  productName: string;
  operationService: string;
  department: string;
  assessUserId: string;
  // 新的动态部门成本结构
  departmentCosts: Record<string, number | null>; // 各部门成本 { "研发一部": 1, "市场营销部": null }
  departmentAssessors: Record<string, string>; // 各部门评估人 { "研发一部": "userId1", "市场营销部": "userId2" }
  // 添加分组相关字段
  groupKey: string;
  departmentList: string[]; // 包含的部门列表
}

// 数据处理函数
export const processApiData = (apiData: ApiDataItem[]): ProcessedCostItem[] => {
  // 创建分组映射
  const groupMap = new Map<
    string,
    {
      items: ApiDataItem[];
      groupKey: string;
      productName: string;
      productCategory: string;
      operationService: string;
      sequence: number;
    }
  >();

  // 第一步：按照规则进行分组
  apiData.forEach((item, index) => {
    const data = item.data;
    const productName = data.KQM21313?.text || "";
    const productNameId = data.KQM21313?.value || "";
    const operationService = data.KQM21315?.text || "暂无";
    const operationServiceId = data.KQM21315?.value || "";
    const productCategory = data.KQM21312?.text || "";
    const sequence = parseInt(data.KQM20760?.text || "0");

    // 确定分组键：优先使用 operationServiceId，如果没有则使用 productNameId
    let groupKey: string;
    if (operationServiceId && operationServiceId.trim() !== "") {
      groupKey = `operation_${operationServiceId}`;
    } else if (productNameId && productNameId.trim() !== "") {
      groupKey = `product_${productNameId}`;
    } else {
      // 如果都没有，使用索引作为唯一标识
      groupKey = `index_${index}`;
    }

    if (!groupMap.has(groupKey)) {
      groupMap.set(groupKey, {
        items: [],
        groupKey,
        productName,
        productCategory,
        operationService,
        sequence: sequence || index + 1,
      });
    }

    groupMap.get(groupKey)!.items.push(item);
  });

  // 第二步：处理每个分组，合并部门数据
  const result: ProcessedCostItem[] = [];

  groupMap.forEach((group, groupKey) => {
    // 初始化部门成本和评估人数据
    const departmentCosts: Record<string, number | null> = {};
    const departmentAssessors: Record<string, string> = {};

    // 收集所有部门和评估人信息
    const departmentList: string[] = [];
    const assessUserIds: string[] = [];
    let primaryAssessUserId = "";

    // 遍历组内所有项目，收集各部门的成本和评估人信息
    group.items.forEach((item) => {
      const data = item.data;
      const department = data.KQM21341?.text || "";
      const assessUserId = data.KQM21319?.value || "";
      const consultingCost = data.KQM21318?.value !== undefined && data.KQM21318?.value !== null && data.KQM21318?.value !== '' ? parseFloat(data.KQM21318.value) : null;

      console.log(
        `处理数据项 ${item.id}: 部门=${department}, 成本=${consultingCost}, 评估人=${assessUserId}`
      );

      if (department) {
        // 添加部门到列表（如果不存在）
        if (!departmentList.includes(department)) {
          departmentList.push(department);
        }

        // 累加该部门的成本（只有当 consultingCost 不为 null 时才累加）
        if (consultingCost !== null) {
          departmentCosts[department] = (departmentCosts[department] || 0) + consultingCost;
        } else if (!(department in departmentCosts)) {
          // 如果该部门还没有记录且当前成本为null，则设置为null
          departmentCosts[department] = null;
        }

        // 记录该部门的评估人
        if (assessUserId) {
          departmentAssessors[department] = assessUserId;
        }
      }

      if (assessUserId && !assessUserIds.includes(assessUserId)) {
        assessUserIds.push(assessUserId);
        if (!primaryAssessUserId) {
          primaryAssessUserId = assessUserId;
        }
      }
    });

    console.log(`分组 ${groupKey} 处理完成:`, {
      departmentList,
      departmentCosts,
      departmentAssessors,
    });

    // 创建合并后的数据项
    result.push({
      id: groupKey, // 使用分组键作为ID
      sequence: group.sequence,
      productCategory: group.productCategory,
      productName: group.productName,
      operationService: group.operationService,
      department: departmentList.join(", "), // 显示所有相关部门
      assessUserId: primaryAssessUserId, // 使用第一个评估人ID
      groupKey,
      departmentList,
      departmentCosts,
      departmentAssessors,
    });
  });

  // 按序号排序
  return result.sort((a, b) => a.sequence - b.sequence);
};

// 获取所有可能的部门列表（用于动态生成表格列）
export const getAllDepartments = (data: ProcessedCostItem[]): string[] => {
  const departments = new Set<string>();
  data.forEach((item) => {
    item.departmentList.forEach((dept) => departments.add(dept));
  });
  return Array.from(departments).sort();
};

export default {
  FIELD_CODE_MAPPING,
  processApiData,
  getAllDepartments,
};
