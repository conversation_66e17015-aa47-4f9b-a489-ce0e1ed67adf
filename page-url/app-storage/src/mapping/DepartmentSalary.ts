// 部门平均工资字段映射表
export const DEPARTMENT_SALARY_FIELD_MAPPING = {
  // 基础信息字段
  KQM17591: "recordId", // 记录ID
  "KQM1689:KQM17431": "department", // 部门（组合字段）
  KQM17431: "departmentName", // 部门名称
  KQM18354: "averageSalary", // 实际工资
  KQM17695: "salaryTemplate", // 所属指标模板
  KQM17597: "creator", // 创建人
  KQM17596: "modifier", // 修改人
  KQM17592: "createTime", // 创建时间
  KQM19670: "company", // 公司
  KQM17600: "system", // 系统
  KQM18355: "departmentId", // 部门ID
} as const;

// API返回的原始数据项接口
export type DepartmentSalaryApiDataItem = {
  id: string;
  data: {
    [key: string]: {
      text: string | number | null;
      value: string | number | null;
    };
  };
  children: null;
};

// 处理后的部门工资数据接口
export interface ProcessedDepartmentSalaryItem {
  id: string;
  recordId: string;
  department: string;
  departmentName: string;
  departmentId: string;
  averageSalary: number;
  salaryTemplate: string;
  creator: string;
  creatorId: string;
  modifier: string;
  modifierId: string;
  createTime: string;
  company: string;
  companyId: string;
  system: string;
  systemId: string;
}

// 数据处理函数
export const processDepartmentSalaryData = (
  apiData: DepartmentSalaryApiDataItem[]
): ProcessedDepartmentSalaryItem[] => {
  return apiData.map((item) => {
    const data = item.data;

    // 提取各字段数据
    const recordId = data.KQM17591?.text?.toString() || "";
    const department = data["KQM1689:KQM17431"]?.text?.toString() || "";
    const departmentName = data.KQM17431?.text?.toString() || "";
    const departmentId = data.KQM18355?.text?.toString() || "";
    const averageSalary = parseFloat(data.KQM18354?.value?.toString() || "0") || 0;
    const salaryTemplate = data.KQM17695?.text?.toString() || "";
    const creator = data.KQM17597?.text?.toString() || "";
    const creatorId = data.KQM17597?.value?.toString() || "";
    const modifier = data.KQM17596?.text?.toString() || "";
    const modifierId = data.KQM17596?.value?.toString() || "";
    const createTime = data.KQM17592?.text?.toString() || "";
    const company = data.KQM19670?.text?.toString() || "";
    const companyId = data.KQM19670?.value?.toString() || "";
    const system = data.KQM17600?.text?.toString() || "";
    const systemId = data.KQM17600?.value?.toString() || "";

    console.log(`处理部门工资数据: ${departmentName} - ${averageSalary}元`);

    return {
      id: item.id,
      recordId,
      department,
      departmentName,
      departmentId,
      averageSalary,
      salaryTemplate,
      creator,
      creatorId,
      modifier,
      modifierId,
      createTime,
      company,
      companyId,
      system,
      systemId,
    };
  });
};

// 获取部门工资映射表（部门名称 -> 平均工资）
export const getDepartmentSalaryMap = (
  data: ProcessedDepartmentSalaryItem[]
): Record<string, number> => {
  const salaryMap: Record<string, number> = {};
  data.forEach((item) => {
    if (item.departmentName && item.averageSalary > 0) {
      salaryMap[item.departmentName] = item.averageSalary;
    }
  });
  return salaryMap;
};

// 获取所有部门列表
export const getAllDepartmentNames = (
  data: ProcessedDepartmentSalaryItem[]
): string[] => {
  const departments = new Set<string>();
  data.forEach((item) => {
    if (item.departmentName) {
      departments.add(item.departmentName);
    }
  });
  return Array.from(departments).sort();
};

// 根据部门名称查找工资信息
export const findSalaryByDepartment = (
  data: ProcessedDepartmentSalaryItem[],
  departmentName: string
): ProcessedDepartmentSalaryItem | undefined => {
  return data.find((item) => item.departmentName === departmentName);
};

export default {
  DEPARTMENT_SALARY_FIELD_MAPPING,
  processDepartmentSalaryData,
  getDepartmentSalaryMap,
  getAllDepartmentNames,
  findSalaryByDepartment,
}; 