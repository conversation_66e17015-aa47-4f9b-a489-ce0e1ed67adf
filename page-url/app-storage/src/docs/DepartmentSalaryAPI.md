# 部门平均工资数据 API 使用文档

## 概述

本模块提供了获取和处理部门平均工资数据的完整解决方案，包括 API 请求、数据映射和数据处理功能。

## 文件结构

```
src/
├── api/index.ts                    # API 请求方法
├── mapping/DepartmentSalary.ts     # 数据映射和处理
├── examples/DepartmentSalaryExample.ts  # 使用示例
└── docs/DepartmentSalaryAPI.md     # 本文档
```

## API 方法

### getDepartmentSalaryData

获取部门平均工资数据的主要 API 方法。

```typescript
import { getDepartmentSalaryData } from "../api/index";

// 使用默认参数
const response = await getDepartmentSalaryData();

// 使用自定义参数
const response = await getDepartmentSalaryData({
  pageNo: 1,
  pageSize: 20,
  // 其他自定义参数...
});
```

#### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| requestType | string | "code" | 请求类型 |
| layoutId | string | "coordinate_KQM3961" | 布局ID |
| resourceType | string | "menu" | 资源类型 |
| resourceId | string | "KQM1674" | 资源ID |
| standardCollectionId | string | "KQM1689" | 标准集合ID |
| pageNo | number | 1 | 页码 |
| pageSize | number | 50 | 每页数量 |

#### 返回数据结构

```typescript
{
  code: 200,
  msg: "服务器成功返回请求的数据",
  data: {
    body: [
      {
        id: "KQM3072016",
        data: {
          "KQM1689:KQM17431": { text: "市场营销部", value: "市场营销部" },
          "KQM18354": { text: 30000.0, value: 30000.0 },
          // 其他字段...
        },
        children: null
      }
      // 更多数据项...
    ],
    pagination: {
      current: 1,
      pageSize: 50,
      total: 9,
      // 分页信息...
    }
  }
}
```

## 数据映射

### 字段映射表

```typescript
export const DEPARTMENT_SALARY_FIELD_MAPPING = {
  KQM17591: "recordId",           // 记录ID
  "KQM1689:KQM17431": "department", // 部门（组合字段）
  KQM17431: "departmentName",     // 部门名称
  KQM18354: "averageSalary",      // 实际工资
  KQM17695: "salaryTemplate",     // 所属指标模板
  KQM17597: "creator",            // 创建人
  KQM17596: "modifier",           // 修改人
  KQM17592: "createTime",         // 创建时间
  KQM19670: "company",            // 公司
  KQM17600: "system",             // 系统
  KQM18355: "departmentId",       // 部门ID
};
```

### 数据处理函数

#### processDepartmentSalaryData

将 API 返回的原始数据转换为结构化数据。

```typescript
import { processDepartmentSalaryData } from "../mapping/DepartmentSalary";

const rawData = response.data.body;
const processedData = processDepartmentSalaryData(rawData);
```

#### getDepartmentSalaryMap

获取部门名称到平均工资的映射表。

```typescript
import { getDepartmentSalaryMap } from "../mapping/DepartmentSalary";

const salaryMap = getDepartmentSalaryMap(processedData);
// 结果: { "市场营销部": 30000, "研发一部": 30000, ... }
```

#### getAllDepartmentNames

获取所有部门名称列表。

```typescript
import { getAllDepartmentNames } from "../mapping/DepartmentSalary";

const departments = getAllDepartmentNames(processedData);
// 结果: ["交付实施一部", "交付实施二部", "产品创新部", ...]
```

#### findSalaryByDepartment

根据部门名称查找工资信息。

```typescript
import { findSalaryByDepartment } from "../mapping/DepartmentSalary";

const marketingInfo = findSalaryByDepartment(processedData, "市场营销部");
```

## 使用示例

### 基本使用

```typescript
import { fetchAndProcessDepartmentSalaryData } from "../examples/DepartmentSalaryExample";

// 获取并处理所有部门工资数据
const result = await fetchAndProcessDepartmentSalaryData();
console.log("处理后的数据:", result.processedData);
console.log("工资映射表:", result.salaryMap);
console.log("部门列表:", result.departments);
```

### 获取特定部门工资

```typescript
import { getDepartmentAverageSalary } from "../examples/DepartmentSalaryExample";

// 获取市场营销部的平均工资
const marketingSalary = await getDepartmentAverageSalary("市场营销部");
console.log("市场营销部平均工资:", marketingSalary); // 30000
```

### 获取工资统计信息

```typescript
import { getDepartmentSalaryStatistics } from "../examples/DepartmentSalaryExample";

// 获取所有部门的工资统计
const statistics = await getDepartmentSalaryStatistics();
console.log("统计信息:", statistics);
/*
结果:
{
  totalDepartments: 9,
  totalSalary: 270000,
  averageSalary: 30000,
  maxSalary: 30000,
  minSalary: 30000,
  departments: [
    { name: "市场营销部", salary: 30000 },
    { name: "研发一部", salary: 30000 },
    // ...
  ]
}
*/
```

### 自定义查询参数

```typescript
import { getDepartmentSalaryData } from "../api/index";
import { processDepartmentSalaryData } from "../mapping/DepartmentSalary";

// 自定义查询参数
const customParams = {
  pageNo: 1,
  pageSize: 10, // 只获取前10条数据
};

const response = await getDepartmentSalaryData(customParams);
const processedData = processDepartmentSalaryData(response.data.body);
```

## 错误处理

所有方法都包含了完整的错误处理机制：

```typescript
try {
  const result = await fetchAndProcessDepartmentSalaryData();
  // 处理成功结果
} catch (error) {
  console.error("获取部门工资数据失败:", error);
  // 处理错误情况
}
```

## 数据结构说明

### ProcessedDepartmentSalaryItem

处理后的部门工资数据项结构：

```typescript
interface ProcessedDepartmentSalaryItem {
  id: string;                    // 数据项ID
  recordId: string;              // 记录ID
  department: string;            // 部门（组合字段）
  departmentName: string;        // 部门名称
  departmentId: string;          // 部门ID
  averageSalary: number;         // 平均工资
  salaryTemplate: string;        // 工资模板
  creator: string;               // 创建人姓名
  creatorId: string;             // 创建人ID
  modifier: string;              // 修改人姓名
  modifierId: string;            // 修改人ID
  createTime: string;            // 创建时间
  company: string;               // 公司名称
  companyId: string;             // 公司ID
  system: string;                // 系统名称
  systemId: string;              // 系统ID
}
```

## 注意事项

1. **认证信息**: API 请求需要正确的认证信息（cookie、token等），确保在 `getHeaders()` 中配置正确。

2. **数据一致性**: 所有部门的工资数据在示例中都是 30000，实际使用时可能会有不同的值。

3. **分页处理**: 默认每页返回50条数据，如果部门数量较多，可能需要处理分页。

4. **错误处理**: 建议在生产环境中添加更详细的错误处理和重试机制。

5. **数据缓存**: 考虑添加数据缓存机制以提高性能，避免频繁请求相同数据。 