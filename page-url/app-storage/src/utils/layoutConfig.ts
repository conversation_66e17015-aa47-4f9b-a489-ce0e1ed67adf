/**
 * 布局配置接口
 */
export interface LayoutConfig {
  /** 是否为微应用模式 */
  isMicroApp: boolean;
  /** 是否自动检测微应用环境 */
  autoDetect: boolean;
}

/**
 * 默认布局配置
 */
const defaultConfig: LayoutConfig = {
  isMicroApp: false,
  autoDetect: true,
};

let currentConfig: LayoutConfig = { ...defaultConfig };

/**
 * 设置布局配置
 */
export const setLayoutConfig = (config: Partial<LayoutConfig>): void => {
  currentConfig = { ...currentConfig, ...config };
};

/**
 * 获取当前布局配置
 */
export const getLayoutConfig = (): LayoutConfig => {
  return { ...currentConfig };
};

/**
 * 重置布局配置为默认值
 */
export const resetLayoutConfig = (): void => {
  currentConfig = { ...defaultConfig };
};

/**
 * 检测是否为微应用环境
 */
export const detectMicroApp = (): boolean => {
  // 检测是否在 iframe 中
  if (window.self !== window.top) {
    return true;
  }

  // 检测是否有微前端框架的标识
  if (
    (window as any).__POWERED_BY_QIANKUN__ ||
    (window as any).__MICRO_APP_ENVIRONMENT__
  ) {
    return true;
  }

  // 检测 PROCESS_ENV 是否有值，有值说明在微应用中
  if ((window as any).PROCESS_ENV) {
    return true;
  }

  // 检测容器高度是否受限（被嵌套的情况）
  const viewportHeight = window.innerHeight;
  const documentHeight = document.documentElement.clientHeight;
  if (documentHeight < viewportHeight * 0.8) {
    return true;
  }

  return false;
};

/**
 * 判断当前是否应该使用微应用模式
 */
export const shouldUseMicroAppMode = (): boolean => {
  const config = getLayoutConfig();

  if (!config.autoDetect) {
    return config.isMicroApp;
  }

  return config.isMicroApp || detectMicroApp();
};
