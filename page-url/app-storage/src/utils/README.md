# 微应用布局配置使用说明

## 问题背景

当应用作为微应用嵌套在其他业务系统中时，会出现双滚动条的问题：
- 外层容器（宿主应用）有自己的滚动
- 内层容器（我们的应用）也设置了固定高度和滚动

## 解决方案

我们提供了智能的布局模式切换机制，可以自动检测或手动配置微应用模式。

### 自动检测

系统会自动检测以下情况并切换到微应用模式：

1. **iframe 嵌套**：检测 `window.self !== window.top`
2. **微前端框架**：检测乾坤、micro-app 等框架标识
3. **环境变量标识**：检测 `window.PROCESS_ENV` 是否存在
4. **容器高度受限**：检测文档高度是否明显小于视窗高度

### 手动配置

#### 方式一：全局配置

```typescript
import { setLayoutConfig } from '@/utils/layoutConfig';

// 应用启动时配置
setLayoutConfig({
  isMicroApp: true,      // 强制使用微应用模式
  autoDetect: false,     // 禁用自动检测
});
```

#### 方式二：组件级配置

```typescript
import PageLayout from '@/components/PageLayout';

// 在具体页面中配置
<PageLayout isMicroApp={true} title="页面标题">
  {/* 页面内容 */}
</PageLayout>
```

### 布局模式差异

#### 独立应用模式（默认）
- 使用 `h-screen` 固定高度
- 内部滚动，header 固定在顶部
- 适合独立运行的应用

#### 微应用模式
- 使用自然高度，不设置固定高度
- 内容随父容器滚动
- 避免双滚动条问题
- 适合嵌入其他系统

### API 参考

#### setLayoutConfig(config)

设置全局布局配置。

**参数：**
- `config.isMicroApp: boolean` - 是否强制使用微应用模式
- `config.autoDetect: boolean` - 是否启用自动检测

#### getLayoutConfig()

获取当前布局配置。

#### resetLayoutConfig()

重置配置为默认值。

#### shouldUseMicroAppMode()

判断当前是否应该使用微应用模式。

### 使用建议

1. **默认情况**：无需任何配置，系统会自动检测并选择合适的布局模式

2. **确定是微应用**：在应用入口处设置全局配置
   ```typescript
   setLayoutConfig({ isMicroApp: true, autoDetect: false });
   ```

3. **特殊页面**：在个别页面中使用组件级配置
   ```typescript
   <PageLayout isMicroApp={true}>
   ```

4. **调试模式**：可以通过控制台手动切换模式进行测试
   ```javascript
   // 在浏览器控制台中执行
   window.__layoutConfig = { isMicroApp: true };
   ``` 