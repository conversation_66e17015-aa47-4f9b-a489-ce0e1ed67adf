# Zustand 状态管理使用指南

本项目使用 Zustand 作为状态管理库，提供了多种使用场景的示例。

## 安装的版本

```json
"zustand": "4.4.1"
```

## Store 文件结构

```
src/store/
├── index.ts          # 基础 store 示例
├── persistStore.ts   # 持久化 store 示例
├── asyncStore.ts     # 异步操作 store 示例
└── README.md         # 使用说明
```

## 基础用法 (index.ts)

### 1. 计数器 Store
```typescript
import { useCounterStore } from '@/store';

const { count, increment, decrement, reset } = useCounterStore();
```

### 2. 用户信息 Store
```typescript
import { useUserStore } from '@/store';

const { user, setUser, clearUser } = useUserStore();
```

### 3. 组合 Store
```typescript
import { useAppStore } from '@/store';

const { count, increment, user, setUser } = useAppStore();
```

## 持久化存储 (persistStore.ts)

### 1. 本地存储 (localStorage)
```typescript
import { usePersistStore } from '@/store/persistStore';

const { theme, setTheme, language, setLanguage } = usePersistStore();
```

### 2. 会话存储 (sessionStorage)
```typescript
import { useSessionStore } from '@/store/persistStore';

const { currentPage, setCurrentPage, breadcrumbs } = useSessionStore();
```

## 异步操作 (asyncStore.ts)

### 1. 基础异步操作
```typescript
import { useAsyncStore } from '@/store/asyncStore';

const { 
  todos, 
  loading, 
  error, 
  fetchTodos, 
  addTodo, 
  toggleTodo, 
  deleteTodo 
} = useAsyncStore();

// 使用示例
useEffect(() => {
  fetchTodos();
}, []);
```

### 2. 计算属性
```typescript
import { 
  useCompletedTodos, 
  usePendingTodos, 
  useTodoStats 
} from '@/store/asyncStore';

const completedTodos = useCompletedTodos();
const pendingTodos = usePendingTodos();
const stats = useTodoStats();
```

### 3. 订阅状态变化
```typescript
import { subscribeToTodos } from '@/store/asyncStore';

useEffect(() => {
  const unsubscribe = subscribeToTodos((todos) => {
    console.log('Todos 更新了:', todos);
  });
  
  return unsubscribe;
}, []);
```

## 最佳实践

### 1. 类型安全
- 所有 store 都使用 TypeScript 定义了完整的类型
- 使用接口定义状态和操作的类型

### 2. 状态分离
- 按功能模块分离不同的 store
- 避免单一巨大的 store

### 3. 异步操作
- 使用 async/await 处理异步操作
- 统一的错误处理和加载状态管理

### 4. 持久化
- 根据需要选择 localStorage 或 sessionStorage
- 敏感信息不要存储在本地

### 5. 性能优化
- 使用选择器避免不必要的重渲染
- 合理使用计算属性

## 中间件使用

### 1. devtools
```typescript
import { devtools } from 'zustand/middleware';

export const useStore = create()(
  devtools((set) => ({
    // store 定义
  }), {
    name: 'store-name'
  })
);
```

### 2. persist
```typescript
import { persist, createJSONStorage } from 'zustand/middleware';

export const useStore = create()(
  persist((set) => ({
    // store 定义
  }), {
    name: 'storage-key',
    storage: createJSONStorage(() => localStorage)
  })
);
```

### 3. subscribeWithSelector
```typescript
import { subscribeWithSelector } from 'zustand/middleware';

export const useStore = create()(
  subscribeWithSelector((set) => ({
    // store 定义
  }))
);
```

## 调试

1. 安装 Redux DevTools 浏览器扩展
2. 在使用 devtools 中间件的 store 中可以看到状态变化
3. 支持时间旅行调试

## 注意事项

1. Zustand 4.4.1 版本与 React 17 兼容
2. 不要在 store 外部直接修改状态
3. 异步操作要正确处理错误和加载状态
4. 持久化的数据在页面刷新后会自动恢复 