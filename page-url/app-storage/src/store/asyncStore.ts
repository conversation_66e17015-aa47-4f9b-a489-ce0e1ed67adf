import { create } from "zustand";
import { devtools, subscribeWithSelector } from "zustand/middleware";

// 模拟 API 数据类型
interface TodoItem {
  id: string;
  title: string;
  completed: boolean;
  createdAt: string;
}

interface AsyncState {
  // 数据状态
  todos: TodoItem[];
  loading: boolean;
  error: string | null;

  // 异步操作
  fetchTodos: () => Promise<void>;
  addTodo: (title: string) => Promise<void>;
  toggleTodo: (id: string) => Promise<void>;
  deleteTodo: (id: string) => Promise<void>;

  // 同步操作
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

// 模拟 API 调用
const mockApi = {
  getTodos: (): Promise<TodoItem[]> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: "1",
            title: "学习 Zustand",
            completed: false,
            createdAt: new Date().toISOString(),
          },
          {
            id: "2",
            title: "完成项目",
            completed: true,
            createdAt: new Date().toISOString(),
          },
        ]);
      }, 1000);
    });
  },

  addTodo: (title: string): Promise<TodoItem> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: Date.now().toString(),
          title,
          completed: false,
          createdAt: new Date().toISOString(),
        });
      }, 500);
    });
  },

  updateTodo: (id: string, updates: Partial<TodoItem>): Promise<TodoItem> => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.1) {
          // 90% 成功率
          resolve({
            id,
            title: "更新的待办事项",
            completed: !updates.completed,
            createdAt: new Date().toISOString(),
            ...updates,
          });
        } else {
          reject(new Error("更新失败"));
        }
      }, 500);
    });
  },

  deleteTodo: (id: string): Promise<void> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 300);
    });
  },
};

// 创建异步 store
export const useAsyncStore = create<AsyncState>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      // 初始状态
      todos: [],
      loading: false,
      error: null,

      // 获取待办事项列表
      fetchTodos: async () => {
        set({ loading: true, error: null });
        try {
          const todos = await mockApi.getTodos();
          set({ todos, loading: false });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : "获取数据失败",
            loading: false,
          });
        }
      },

      // 添加待办事项
      addTodo: async (title: string) => {
        if (!title.trim()) {
          set({ error: "标题不能为空" });
          return;
        }

        set({ loading: true, error: null });
        try {
          const newTodo = await mockApi.addTodo(title);
          set((state) => ({
            todos: [...state.todos, newTodo],
            loading: false,
          }));
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : "添加失败",
            loading: false,
          });
        }
      },

      // 切换待办事项状态
      toggleTodo: async (id: string) => {
        const todo = get().todos.find((t) => t.id === id);
        if (!todo) return;

        set({ loading: true, error: null });
        try {
          const updatedTodo = await mockApi.updateTodo(id, {
            completed: !todo.completed,
          });
          set((state) => ({
            todos: state.todos.map((t) => (t.id === id ? updatedTodo : t)),
            loading: false,
          }));
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : "更新失败",
            loading: false,
          });
        }
      },

      // 删除待办事项
      deleteTodo: async (id: string) => {
        set({ loading: true, error: null });
        try {
          await mockApi.deleteTodo(id);
          set((state) => ({
            todos: state.todos.filter((t) => t.id !== id),
            loading: false,
          }));
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : "删除失败",
            loading: false,
          });
        }
      },

      // 清除错误
      clearError: () => set({ error: null }),

      // 设置加载状态
      setLoading: (loading: boolean) => set({ loading }),
    })),
    {
      name: "async-store", // Redux DevTools 中的名称
    }
  )
);

// 订阅状态变化的示例
export const subscribeToTodos = (callback: (todos: TodoItem[]) => void) => {
  return useAsyncStore.subscribe((state) => state.todos, callback);
};

// 计算属性示例
export const useCompletedTodos = () => {
  return useAsyncStore((state) => state.todos.filter((todo) => todo.completed));
};

export const usePendingTodos = () => {
  return useAsyncStore((state) =>
    state.todos.filter((todo) => !todo.completed)
  );
};

export const useTodoStats = () => {
  return useAsyncStore((state) => ({
    total: state.todos.length,
    completed: state.todos.filter((todo) => todo.completed).length,
    pending: state.todos.filter((todo) => !todo.completed).length,
  }));
};
