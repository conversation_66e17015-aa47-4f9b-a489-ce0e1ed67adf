import { create } from "zustand";

// 定义状态类型
interface CounterState {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
}
interface TokenIDState {
  tokenID: string;
  setTokenID: (tokenID: string) => void;
}

interface UserState {
  user: {
    id: string;
    name: string;
    email: string;
  } | null;
  setUser: (user: { id: string; name: string; email: string }) => void;
  clearUser: () => void;
}

// tokenID
export const useTokenIDStore = create<TokenIDState>((set) => ({
    tokenID: '',
    setTokenID: (tokenID) => set({ tokenID }),
  }));
// 计数器 store
export const useCounterStore = create<CounterState>((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
  reset: () => set({ count: 0 }),
}));

// 用户信息 store
export const useUserStore = create<UserState>((set) => ({
  user: null,
  setUser: (user) => set({ user }),
  clearUser: () => set({ user: null }),
}));

// 组合 store 示例
interface AppState extends CounterState, UserState,TokenIDState {}

export const useAppStore = create<AppState>((set) => ({
  // 计数器状态
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
  reset: () => set({ count: 0 }),

  // 用户状态
  user: null,
  setUser: (user) => set({ user }),
  clearUser: () => set({ user: null }),
  tokenID: "",
  setTokenID: (tokenID) => set({ tokenID }),
}));
