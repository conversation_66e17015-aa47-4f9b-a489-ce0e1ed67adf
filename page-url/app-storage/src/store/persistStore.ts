import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// 定义持久化状态类型
interface PersistState {
  theme: 'light' | 'dark';
  language: 'zh' | 'en';
  settings: {
    notifications: boolean;
    autoSave: boolean;
  };
  setTheme: (theme: 'light' | 'dark') => void;
  setLanguage: (language: 'zh' | 'en') => void;
  updateSettings: (settings: Partial<PersistState['settings']>) => void;
}

// 带持久化的 store
export const usePersistStore = create<PersistState>()(
  persist(
    (set) => ({
      theme: 'light',
      language: 'zh',
      settings: {
        notifications: true,
        autoSave: false,
      },
      setTheme: (theme) => set({ theme }),
      setLanguage: (language) => set({ language }),
      updateSettings: (newSettings) =>
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        })),
    }),
    {
      name: 'app-storage-settings', // localStorage 中的 key
      storage: createJSONStorage(() => localStorage), // 使用 localStorage
    }
  )
);

// 会话存储示例
interface SessionState {
  currentPage: string;
  breadcrumbs: string[];
  setCurrentPage: (page: string) => void;
  addBreadcrumb: (crumb: string) => void;
  clearBreadcrumbs: () => void;
}

export const useSessionStore = create<SessionState>()(
  persist(
    (set) => ({
      currentPage: 'home',
      breadcrumbs: [],
      setCurrentPage: (page) => set({ currentPage: page }),
      addBreadcrumb: (crumb) =>
        set((state) => ({
          breadcrumbs: [...state.breadcrumbs, crumb],
        })),
      clearBreadcrumbs: () => set({ breadcrumbs: [] }),
    }),
    {
      name: 'app-session-data',
      storage: createJSONStorage(() => sessionStorage), // 使用 sessionStorage
    }
  )
); 