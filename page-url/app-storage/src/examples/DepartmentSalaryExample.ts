// 部门平均工资数据使用示例
import { getDepartmentSalaryData } from "../api/index";
import {
  processDepartmentSalaryData,
  getDepartmentSalaryMap,
  getAllDepartmentNames,
  findSalaryByDepartment,
} from "../mapping/DepartmentSalary";
import type {
  DepartmentSalaryApiDataItem,
  ProcessedDepartmentSalaryItem,
} from "../mapping/DepartmentSalary";

// 示例：获取并处理部门平均工资数据
export const fetchAndProcessDepartmentSalaryData = async () => {
  try {
    console.log("开始获取部门平均工资数据...");

    // 1. 调用API获取原始数据
    const response = await getDepartmentSalaryData();

    if (response.code !== 200) {
      throw new Error(`API请求失败: ${response.msg}`);
    }

    const rawData: DepartmentSalaryApiDataItem[] = response.data.body;
    console.log(`获取到 ${rawData.length} 条原始数据`);

    // 2. 处理数据
    const processedData: ProcessedDepartmentSalaryItem[] =
      processDepartmentSalaryData(rawData);
    console.log("数据处理完成:", processedData);

    // 3. 获取部门工资映射表
    const salaryMap = getDepartmentSalaryMap(processedData);
    console.log("部门工资映射表:", salaryMap);

    // 4. 获取所有部门列表
    const departments = getAllDepartmentNames(processedData);
    console.log("所有部门:", departments);

    // 5. 查找特定部门的工资信息
    const marketingDept = findSalaryByDepartment(processedData, "市场营销部");
    if (marketingDept) {
      console.log("市场营销部工资信息:", marketingDept);
    }

    return {
      rawData,
      processedData,
      salaryMap,
      departments,
    };
  } catch (error) {
    console.error("获取部门工资数据失败:", error);
    throw error;
  }
};

// 示例：获取特定部门的平均工资
export const getDepartmentAverageSalary = async (
  departmentName: string
): Promise<number | null> => {
  try {
    const { salaryMap } = await fetchAndProcessDepartmentSalaryData();
    return salaryMap[departmentName] || null;
  } catch (error) {
    console.error(`获取部门 ${departmentName} 工资失败:`, error);
    return null;
  }
};

// 示例：获取所有部门的工资统计信息
export const getDepartmentSalaryStatistics = async () => {
  try {
    const { processedData } = await fetchAndProcessDepartmentSalaryData();

    const salaries = processedData.map((item) => item.averageSalary);
    const totalSalary = salaries.reduce((sum, salary) => sum + salary, 0);
    const averageSalary = totalSalary / salaries.length;
    const maxSalary = Math.max(...salaries);
    const minSalary = Math.min(...salaries);

    const statistics = {
      totalDepartments: processedData.length,
      totalSalary,
      averageSalary: Math.round(averageSalary),
      maxSalary,
      minSalary,
      departments: processedData.map((item) => ({
        name: item.departmentName,
        salary: item.averageSalary,
      })),
    };

    console.log("部门工资统计信息:", statistics);
    return statistics;
  } catch (error) {
    console.error("获取工资统计信息失败:", error);
    throw error;
  }
};

// 示例：自定义查询参数获取数据
export const fetchDepartmentSalaryWithCustomParams = async () => {
  try {
    // 可以自定义查询参数
    const customParams = {
      pageNo: 1,
      pageSize: 20, // 限制返回数量
      // 可以添加其他自定义筛选条件
    };

    const response = await getDepartmentSalaryData(customParams);
    const processedData = processDepartmentSalaryData(response.data.body);

    return processedData;
  } catch (error) {
    console.error("自定义查询失败:", error);
    throw error;
  }
};

export default {
  fetchAndProcessDepartmentSalaryData,
  getDepartmentAverageSalary,
  getDepartmentSalaryStatistics,
  fetchDepartmentSalaryWithCustomParams,
}; 