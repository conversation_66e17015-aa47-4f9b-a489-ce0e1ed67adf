# 工时统计API升级说明

## 📋 概述

根据你提供的API参数示例，我们已经成功升级了工时统计页面的API调用方式，支持更标准化和复杂的筛选条件格式。

## 🔧 主要改进

### 1. 新增标准列表API支持

- **新函数**: `buildStandardListParams()` - 构建标准列表查询参数
- **新Hook方法**: `applyStandardFilters()` - 使用标准列表API进行筛选
- **向后兼容**: 保留了原有的API调用方式

### 2. 完整的字段代码映射

```typescript
export const FIELD_CODES = {
  workDate: 'HW21967',        // 工时日期
  department: 'HW21968',      // 部门
  employee: 'HW21969',        // 员工
  workType: 'HW21970',        // 工时类型
  approvalStatus: 'HW21971',  // 审批状态
  project: 'HW21972',         // 项目
  opportunityProject: 'HW23572', // 商机项目
  productProject: 'KQM18553',    // 产品项目
  consultingTask: 'KQM20885',    // 咨询任务
  contractProject: 'KQM19356',   // 合同项目
};
```

### 3. 支持的筛选条件类型

| 字段类型 | 操作符 | 说明 | 示例 |
|---------|--------|------|------|
| DATE | RANGE | 日期范围筛选 | 2025-06-06 到 2025-06-09 |
| MULTITOONE | CONTAIN | 多对一关系筛选 | 部门、项目筛选 |
| IDRELATIONSHIP | CONTAIN | ID关系筛选 | 员工筛选 |
| RADIO | IN | 单选值筛选 | 工时类型、审批状态 |

## 📤 API参数格式对比

### 你提供的标准格式
```json
{
  "requestType": "code",
  "layoutId": "coordinate_HW4218",
  "resourceType": "menu",
  "resourceId": "HW929",
  "standardCollectionId": "HW760",
  "pageNo": 1,
  "pageSize": 15,
  "filterBoxDataMap": {
    "columnFilterObj": {
      "expression": "1",
      "conditions": [
        {
          "fieldCode": "HW21967",
          "fieldType": "DATE",
          "fieldName": "工时日期",
          "operator": "RANGE",
          "text": {"value": ["2025-06-06", "2025-06-09"]},
          "value": ["2025-06-06", "2025-06-09"],
          "type": null,
          "serialNumber": 1
        }
      ],
      "isAdvanced": true
    },
    "viewId": "all_HW760"
  }
}
```

### 我们生成的格式
我们的 `buildStandardListParams()` 函数能够生成完全兼容的API参数格式，支持：

- ✅ 日期范围筛选
- ✅ 部门多选筛选
- ✅ 员工多选筛选
- ✅ 工时类型筛选
- ✅ 审批状态筛选
- ✅ 各类项目筛选
- ✅ 复杂条件组合（AND表达式）

## 🚀 使用方式

### 1. 在主组件中使用

```typescript
// 应用筛选
const handleApplyFilters = async () => {
  // 使用新的标准列表API
  const standardParams = buildStandardListParams(filters, filterOptions);
  await applyStandardFilters(standardParams);
};
```

### 2. 测试功能

开发环境下可以使用测试按钮：

```typescript
// 测试API参数构建
<Button onClick={testApiCall} type="dashed">
  测试API参数构建
</Button>

// 测试标准列表参数
<Button onClick={testStandardListParams} type="dashed">
  测试标准列表参数
</Button>
```

### 3. 查看测试页面

打开 `test-page.html` 可以：
- 查看API参数格式对比
- 测试不同筛选条件的参数生成
- 验证参数格式的正确性

## 🔄 向后兼容性

- 保留了原有的 `buildApiFilters()` 函数
- 保留了原有的 `applyFilters()` Hook方法
- 可以根据需要选择使用新旧API

## 📊 功能特点

### 1. 灵活的条件组合
- 支持多个筛选条件的AND组合
- 自动生成表达式（如："1 AND 2 AND 3"）
- 支持复杂的嵌套筛选逻辑

### 2. 完整的数据结构
- 每个条件包含完整的字段信息
- 支持text和value的双重映射
- 包含必要的元数据（serialNumber、type等）

### 3. 类型安全
- 完整的TypeScript类型定义
- 编译时类型检查
- 智能代码提示

## 🧪 测试验证

### 1. 单元测试
```typescript
// 测试日期筛选
const filters = { dateRange: ["2025-06-06", "2025-06-09"] };
const params = buildStandardListParams(filters, filterOptions);
// 验证生成的参数格式
```

### 2. 集成测试
- 在开发环境中使用测试按钮
- 查看控制台输出的参数格式
- 对比实际API调用结果

### 3. 浏览器测试
- 打开 `test-page.html`
- 测试各种筛选条件组合
- 验证参数生成的正确性

## 📈 性能优化

### 1. 参数缓存
- 使用 `useCallback` 优化函数性能
- 避免不必要的参数重新构建

### 2. 条件优化
- 只在有筛选条件时构建conditions数组
- 空条件时使用简化的参数格式

### 3. 内存管理
- 及时清理不需要的数据引用
- 优化大数据量的处理

## 🔮 未来扩展

### 1. 更多筛选类型
- 支持更多字段类型（TEXTAREA、NUMBER等）
- 支持更多操作符（LIKE、NOT_IN等）
- 支持OR条件组合

### 2. 高级功能
- 筛选条件的保存和加载
- 预设筛选模板
- 筛选历史记录

### 3. 性能提升
- 服务端分页优化
- 增量数据加载
- 智能缓存策略

## 📝 总结

通过这次升级，工时统计页面现在支持：

1. ✅ **标准化API格式** - 完全兼容你提供的参数格式
2. ✅ **复杂筛选条件** - 支持多种字段类型和操作符
3. ✅ **向后兼容** - 保留原有功能不受影响
4. ✅ **类型安全** - 完整的TypeScript支持
5. ✅ **易于测试** - 提供完整的测试工具
6. ✅ **文档完善** - 详细的使用说明和示例

这个升级为后续的功能扩展和维护奠定了坚实的基础。 