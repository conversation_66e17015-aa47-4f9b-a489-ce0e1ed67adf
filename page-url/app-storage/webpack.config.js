// Generated using webpack-cli https://github.com/webpack/webpack-cli

const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const MomentLocalesPlugin = require('moment-locales-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const ImageMinimizerPlugin = require("image-minimizer-webpack-plugin");
const webpack = require("webpack");
const isProduction = process.env.NODE_ENV == 'production';

const stylesHandler = MiniCssExtractPlugin.loader;

/**
 * ClickPaaS 平台默认提供 react react-dom 的 umd 资源
 * 配置 externals 可以减少打包时间，提升加载性能
 **/

const externals = false ? {
  "react": {
    commonjs: '__CLICKPAAS_SHARE_MODULES__',
    commonjs2: '__CLICKPAAS_SHARE_MODULES__',
    root: ['__CLICKPAAS_SHARE_MODULES__', 'React']
  },
  "react-dom": {
    commonjs: '__CLICKPAAS_SHARE_MODULES__',
    commonjs2: '__CLICKPAAS_SHARE_MODULES__',
    root: ['__CLICKPAAS_SHARE_MODULES__', 'ReactDOM']
  },
  "lodash": {
    commonjs: '__CLICKPAAS_SHARE_MODULES__',
    commonjs2: '__CLICKPAAS_SHARE_MODULES__',
    root: ['__CLICKPAAS_SHARE_MODULES__', 'lodash']
  },
} : {}


const getConfig = (env, isProduction) => {
  return  {
    mode: isProduction ? 'production' : 'development',
    entry: './src/index.tsx',
    output: {
      publicPath: '',
      path: path.resolve(__dirname, 'dist'),
      libraryTarget: 'umd',
      library: "app-storage-4328730a-c20a-4150-a615-a7d6fa34a78e",
    },
    externals,
    devServer: {
      port: 9000,
      open: true,
      host: 'localhost',
      headers: {
        "Access-Control-Allow-Origin": '*'
      },
      proxy: {
        '/app/api': {
          target: 'https://pms.tongbaninfo.com:8888',
          changeOrigin: true,
          secure: true,
          logLevel: 'debug',
          onProxyReq: (proxyReq, req, res) => {
            console.log('代理请求:', req.method, req.url);
          },
          onProxyRes: (proxyRes, req, res) => {
            console.log('代理响应:', proxyRes.statusCode, req.url);
          }
        }
      }
    },
    plugins: [
      new webpack.DefinePlugin({
        "process.env": {
          NODE_ENV: JSON.stringify(process.env.NODE_ENV || "development"),
          REQUEST_KEY:JSON.stringify(env.REQUEST_KEY)
        },
      }),
      new MomentLocalesPlugin(),
      new HtmlWebpackPlugin({
        template: 'public/index.html',
      }),
      new CopyWebpackPlugin({
        patterns: [{
          from: 'public',
          globOptions: {
            dot: true,
            gitignore: true,
            ignore: ['**/public/index.html'],
          },
          noErrorOnMissing: true,
        },
        {
          from: 'README.md'
        },
        {
          from: 'screenshots',
          to: 'screenshots',
          noErrorOnMissing: true
        },
        {
          from: 'manifest.json'
        },
        ],
      }),
      new MiniCssExtractPlugin(),
  
      // Add your plugins here
      // Learn more about plugins from https://webpack.js.org/configuration/plugins/
    ],
    module: {
      rules: [{
        test: /\.(ts|tsx)$/i,
        use: [{
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-react']
          }
        },
          'ts-loader',
        ],
        exclude: ['/node_modules/'],
      },
      {
        test: /\.less$/i,
        use: [stylesHandler, {
          loader: 'css-loader',
          options: {
            modules: {
              auto: path => !path.includes("node_modules"),
              localIdentName: '[local]_[hash:base64:16]'
            }
          }
        }, 'postcss-loader', {
            loader: 'less-loader',
            options: {
              lessOptions: {
                javascriptEnabled: true,
                modifyVars: {
                  "primary-color": "#5378ff",
                },
              }
            }
          }],
      },
      {
        test: /\.css$/i,
        use: [stylesHandler, {
          loader: 'css-loader',
          options: {
            modules: {
              auto: path => !path.includes("node_modules") && !path.includes("index.css"),
              localIdentName: '[local]_[hash:base64:16]'
            }
          }
        }, 'postcss-loader'],
      },
      {
        test: /\.(eot|svg|ttf|woff|woff2|png|jpg|gif)$/i,
        type: 'asset',
      },
  
        // Add your rules for custom modules here
        // Learn more about loaders from https://webpack.js.org/loaders/
      ],
    },
    resolve: {
      extensions: ['.tsx', '.ts', '.js'],
      alias: {
        '@': path.resolve('src'),
        // 强制使用统一的 React 版本，避免版本冲突
        'react': path.resolve('./node_modules/react'),
        'react-dom': path.resolve('./node_modules/react-dom')
      }
    },
    optimization: {
      minimizer: [
        `...`,
        new CssMinimizerPlugin(),
        new ImageMinimizerPlugin({
          test: /screenshots\/.*\.(jpe?g|png|avif)$/i,
          minimizer: {
            implementation: ImageMinimizerPlugin.squooshMinify,
            options: {
              encodeOptions: {
                mozjpeg: {
                  quality: 15,
                },
                avif: {
                  cqLevel: 18,
                  speed: 6
                },
                oxipng: {
                  level: 2,
                }
              },
            },
          },
        }),
      ],
    },
  };
}

module.exports = (env) => {
  return getConfig(env, isProduction);
};