# 工时统计筛选功能改进说明

## 📋 概述

根据你提供的真实API参数格式，我们已经完全重构了工时统计页面的筛选功能，支持标准化的多条件筛选，完全兼容现有的API格式。

## 🔧 主要改进

### 1. 修正字段代码映射

根据 note.md 中的实际API数据结构，修正了所有字段代码：

```typescript
export const FIELD_CODES = {
  workDate: 'HW21967',           // 工时日期
  department: 'KQM19098',        // 部门 
  employee: 'KQM19036',          // 填写人员
  workType: 'HW23543',           // 工时类型
  approvalStatus: 'HW23575',     // 审批状态
  opportunityProject: 'HW23572', // 所属商机项目
  productProject: 'KQM18553',    // 所属产品项目
  consultingTask: 'KQM20885',    // 所属咨询任务
  contractProject: 'KQM19356',   // 所属合同项目
  workHours: 'KQM17303',         // 工时小时
};
```

### 2. 完善筛选条件格式

#### 工时类型筛选（按照你提供的格式）
```json
{
  "fieldCode": "HW23543",
  "fieldType": "RADIO",
  "fieldName": "工时类型",
  "operator": "CONTAIN",
  "text": {
    "dataConfigMap": {
      "masterStyle": {}
    },
    "text": ["休假工时"],
    "value": ["HW2374"]
  },
  "value": ["HW2374"],
  "valueName": "休假工时",
  "type": null,
  "serialNumber": 1
}
```

#### 审批状态筛选
```json
{
  "fieldCode": "HW23575",
  "fieldType": "RADIO", 
  "fieldName": "审批状态",
  "operator": "CONTAIN",
  "text": {
    "dataConfigMap": {
      "masterStyle": {}
    },
    "text": ["审批通过"],
    "value": ["HW2376"]
  },
  "value": ["HW2376"],
  "valueName": "审批通过",
  "type": null,
  "serialNumber": 2
}
```

#### 部门筛选
```json
{
  "fieldCode": "KQM19098",
  "fieldType": "MULTITOONE",
  "fieldName": "部门",
  "operator": "CONTAIN",
  "text": {
    "icon": [null],
    "text": ["研发一部"],
    "value": ["HW133"],
    "positionStatus": [null]
  },
  "value": ["HW133"],
  "type": null,
  "serialNumber": 3
}
```

#### 员工筛选
```json
{
  "fieldCode": "KQM19036",
  "fieldType": "IDRELATIONSHIP",
  "fieldName": "员工",
  "operator": "CONTAIN",
  "text": {
    "icon": [null],
    "text": ["张艳华"],
    "value": ["KQM2259418"],
    "positionStatus": [null]
  },
  "value": ["KQM2259418"],
  "type": null,
  "serialNumber": 4
}
```

#### 日期范围筛选
```json
{
  "fieldCode": "HW21967",
  "fieldType": "DATE",
  "fieldName": "工时日期",
  "operator": "RANGE",
  "text": {
    "value": ["2025-06-01", "2025-06-30"]
  },
  "value": ["2025-06-01", "2025-06-30"],
  "type": null,
  "serialNumber": 5
}
```

### 3. 支持复杂条件组合

#### 表达式构建
- 支持多条件 AND 组合
- 自动生成序列化表达式：`"1 AND 2 AND 3 AND 4 AND 5"`
- 当没有筛选条件时，使用默认表达式：`"1"`

#### 完整API参数示例
```json
{
  "requestType": "code",
  "layoutId": "coordinate_HW4218",
  "resourceType": "menu",
  "resourceId": "HW929",
  "standardCollectionId": "HW760",
  "pageNo": 1,
  "pageSize": 500,
  "tempFilterBoxDataMap": null,
  "showModelType": "list",
  "filterBoxDataMap": {
    "columnFilterObj": {
      "expression": "1 AND 2 AND 3 AND 4 AND 5",
      "conditions": [
        // ... 各种筛选条件
      ],
      "isAdvanced": true
    },
    "filterObj": null,
    "listViewFilterObj": null,
    "searchFilterObj": null,
    "viewId": "all_HW760"
  },
  "needToRecordLastViewId": true,
  "lastViewId": "all_HW760",
  "filterBoxData": null,
  "_crumb": "********************************"
}
```

## 📊 大数据处理功能

### 1. 智能分页处理
- 当 `pagination.total > 4000` 时弹出确认框
- 支持自动并发请求多页数据
- 实时显示数据获取进度

### 2. 用户体验优化
```typescript
// 大数据量确认提示
if (pagination.total > 4000) {
  const confirmed = await new Promise<boolean>((resolve) => {
    Modal.confirm({
      title: '⚠️ 大数据量提醒',
      content: `检测到数据量较大，共 ${pagination.total} 条记录

预计需要请求 ${totalPages} 页数据，可能需要较长时间

💡 建议：您可以通过筛选条件（如日期范围、部门等）来减少数据量，提升查询效率`,
      okText: '继续获取',
      cancelText: '取消',
      // ...其他配置
    });
  });
}
```

## 🛠️ 开发调试工具

### 1. 测试函数
在开发环境下提供了多个测试函数：

```typescript
// 测试基础API参数构建
testApiCall()

// 测试标准列表参数构建  
testStandardListParams()

// 测试复杂筛选条件组合
testComplexFilters()
```

### 2. 控制台调试信息
- 详细的参数构建日志
- 筛选条件验证信息
- 分页请求进度显示
- 数据处理状态追踪

## 🔄 向后兼容性

### 1. 保留旧版API支持
```typescript
// 新版标准列表API
await applyStandardFilters(filters, filterOptions);

// 旧版API（向后兼容）
const apiFilters = buildApiFilters(filters, filterOptions);
await applyFilters(apiFilters);
```

### 2. 无缝升级
- 不影响现有功能
- 保持相同的用户界面
- 支持渐进式迁移

## 📈 性能优化

### 1. 智能分页策略
- 默认页面大小：500条/页
- 根据数据总量动态调整请求策略
- 并发请求提高数据获取效率

### 2. 前端缓存优化
- 筛选选项数据缓存
- 避免重复API调用
- 优化用户交互响应速度

## 🎯 使用方式

### 1. 基础筛选
```typescript
const filters = {
  departments: ["HW133"],          // 研发一部
  workTypeValues: ["HW2374"],     // 休假工时
  dateRange: ["2025-06-01", "2025-06-30"]
};

await applyStandardFilters(filters, filterOptions);
```

### 2. 复杂筛选
```typescript
const complexFilters = {
  departments: ["HW133", "HW132"],
  employees: ["KQM2259418"],
  workTypeValues: ["HW2380", "HW2374"],
  approvalStatuses: ["HW2376"],
  productProjects: ["KQM2488070"],
  dateRange: ["2025-06-01", "2025-06-30"]
};

await applyStandardFilters(complexFilters, filterOptions);
```

## ✅ 测试验证

### 1. 参数格式验证
- 完全符合你提供的API格式
- 支持所有字段类型的筛选
- 正确的操作符和数据结构

### 2. 功能完整性测试
- 单条件筛选 ✅
- 多条件组合筛选 ✅
- 大数据量处理 ✅
- 分页数据合并 ✅
- 错误处理和用户提示 ✅

## 🔮 未来扩展

### 1. 更多筛选条件支持
- 合同项目筛选
- 更多日期筛选模式
- 自定义筛选条件

### 2. 性能进一步优化
- 虚拟滚动支持
- 流式数据加载
- 智能预加载策略 