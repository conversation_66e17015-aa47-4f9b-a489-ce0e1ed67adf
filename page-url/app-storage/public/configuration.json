{"$schema": "../node_modules/@clickpaas/create-plugin-cli/configuration-schema.json", "propName1": {"label": "配置项一", "type": "input", "defaultValue": "配置项内容"}, "propName2": {"label": "配置项二", "type": "input-number", "defaultValue": 350}, "propName3": {"label": "配置项三", "type": "switch", "defaultValue": false}, "propName4": {"label": "配置项四", "type": "select", "options": [{"label": "绿野仙踪", "value": "option1"}, {"label": "一蓑烟雨", "value": "option2"}, {"label": "赛博朋克", "value": "option3"}]}, "propName5": {"label": "配置项五", "type": "field-select", "filters": ["TEXT"]}, "propName6": {"label": "配置项六", "type": "color-picker"}}