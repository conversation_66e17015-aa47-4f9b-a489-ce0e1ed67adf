{"name": "app-storage", "version": "1.0.0", "description": "app-storage", "main": "index.js", "scripts": {"build": "webpack --mode=production --node-env=production --env REQUEST_KEY=11dbda38-c02a-42a6-b0e5-3dae0200f48f && clickpaas-plugin-cli -c", "build:dev": "webpack --mode=production --node-env=production --env REQUEST_KEY=edad3da3-f8c3-407d-b9f5-b7b7544619ec && clickpaas-plugin-cli -c", "build:prod": "webpack --mode=production --node-env=production", "serve": "webpack serve --env REQUEST_KEY=edad3da3-f8c3-407d-b9f5-b7b7544619ec", "dev": "webpack serve --node-env=development --env REQUEST_KEY=11dbda38-c02a-42a6-b0e5-3dae0200f48f"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "7.15.8", "@babel/preset-react": "7.14.5", "@clickpaas/create-plugin-cli": "6.8.49", "@squoosh/lib": "0.5.3", "@types/react": "17.0.27", "@types/react-dom": "17.0.9", "@webpack-cli/generators": "2.4.0", "archiver": "5.3.1", "autoprefixer": "10.3.7", "babel-loader": "8.2.2", "babel-plugin-import": "1.13.5", "copy-webpack-plugin": "11.0.0", "css-loader": "6.3.0", "css-minimizer-webpack-plugin": "4.2.1", "html-webpack-plugin": "5.3.2", "image-minimizer-webpack-plugin": "3.8.1", "less": "4.1.2", "less-loader": "10.0.1", "mini-css-extract-plugin": "2.4.2", "moment-locales-webpack-plugin": "1.2.0", "postcss": "^8.5.3", "postcss-loader": "6.1.1", "style-loader": "3.3.0", "tailwindcss": "^3.3.0", "ts-loader": "9.2.6", "typescript": "4.4.3", "webpack": "5.58.0", "webpack-cli": "4.10.0", "webpack-dev-server": "4.3.1", "workbox-webpack-plugin": "6.3.0"}, "dependencies": {"antd": "4.21.2", "chart.js": "^4.4.9", "react": "17.0.2", "react-chartjs-2": "^5.3.0", "react-dom": "17.0.2", "react-router-dom": "6.4.3", "zustand": "4.4.1", "click-plugin-utils": "workspace:^"}}